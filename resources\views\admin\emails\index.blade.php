@extends('admin.layouts.app')

@section('panel')
    <div class="card card-body" x-data="EmailsData()">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>Marketing emails</h2>
            <div>
                <button class="btn btn-primary-header me-2 ">New draft</button>
                <button class="btn btn-primary-header">Templates</button>
            </div>
        </div>

        <div class="d-flex flex-column align-items-start mb-3 gap-2">
            <span>Metrics time period</span>
            <select class="form-select w-auto">
                <option>Last 7 days</option>
                <option>Last 30 days</option>
                <option>Last 90 days</option>
                <option>Last 6 months</option>
                <option>Last 1 year</option>
                <option>Custom range</option>
            </select>
        </div>

        <div class="row text-center mb-4">
            <div class="col">
                <div class="border p-3">
                    <div class="fw-bold d-flex align-items-center justify-content-center mb-1">
                        <i class="fa fa-paper-plane me-2 text-primary"></i> Sent emails
                    </div>
                    <div x-text="metrics.sent"></div>
                </div>
            </div>
            <div class="col">
                <div class="border p-3">
                    <div class="fw-bold d-flex align-items-center justify-content-center mb-1">
                        <i class="fa fa-envelope-open-text me-2 text-success"></i> Open rate
                    </div>
                    <div x-text="metrics.open"></div>
                </div>
            </div>
            <div class="col">
                <div class="border p-3">
                    <div class="fw-bold d-flex align-items-center justify-content-center mb-1">
                        <i class="fa fa-mouse-pointer me-2 text-info"></i> Click rate
                    </div>
                    <div x-text="metrics.click"></div>
                </div>
            </div>
            <div class="col">
                <div class="border p-3">
                    <div class="fw-bold d-flex align-items-center justify-content-center mb-1">
                        <i class="fa fa-ban me-2 text-danger"></i> Unsubscribe rate
                    </div>
                    <div x-text="metrics.unsubscribeRate"></div>
                </div>
            </div>
            <div class="col">
                <div class="border p-3">
                    <div class="fw-bold d-flex align-items-center justify-content-center mb-1">
                        <i class="fa fa-user-slash me-2 text-warning"></i> Unsubscribes
                    </div>
                    <div x-text="metrics.unsubscribes"></div>
                </div>
            </div>
            <div class="col">
                <div class="border p-3">
                    <div class="fw-bold d-flex align-items-center justify-content-center mb-1">
                        <i class="fa fa-user-plus me-2 text-secondary"></i> Contacts added
                    </div>
                    <div x-text="metrics.contactsAdded"></div>
                </div>
            </div>
        </div>

        <ul class="nav nav-tabs mb-3">
            <li class="nav-item">
                <a class="nav-link active" href="#">Draft</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#">Sent</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#">Inbox</a>
            </li>
        </ul>

        <div class="d-flex justify-content-between mb-3">
            <input type="text" class="form-control w-25" placeholder="Search emails...">
            <div>
                <button class="btn btn-outline-secondary me-2">Email settings <span
                        class="badge bg-warning text-dark">1</span></button>
                <button class="btn btn-outline-secondary">Filters</button>
            </div>
        </div>

        <table class="table table-bordered text-center">
            <thead class="table-light">
                <tr>
                    <th scope="col"><input type="checkbox"></th>
                    <th scope="col">Title</th>
                    <th scope="col">Recipients</th>
                    <th scope="col">Created</th>
                    <th scope="col">Last updated</th>
                    <th scope="col">Status</th>
                    <th scope="col">Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td colspan="7" class="text-center mt-2">Create your first email
                        <div class="text-center mt-3">
                            <a class="btn btn-primary" href="{{route('admin.emails.templates')}}">Compose a message</a>
                        </div>
                    </td>

                </tr>
            </tbody>
        </table>


    </div>
    <style>
        .btn-primary-header {
            padding: 10px 50px;
            border-radius: 15px;
            background-color: blue;
            color: white;
        }
        .btn-primary-header:hover {
            background-color: darkblue;
        }
    </style>
@endsection
@section('scripts')
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script>
        function EmailsData() {
            return {
                metrics: {
                    sent: '0',
                    open: '0%',
                    click: '0%',
                    unsubscribeRate: '0%',
                    unsubscribes: '0',
                    contactsAdded: '0'
                }
            }
        }
    </script>
    <script src="{{ asset('js/admin/emails.js') }}"></script>
@endsection
