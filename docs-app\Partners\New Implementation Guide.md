# Partner Routing Implementation Guide

## Overview

This guide outlines the implementation of a simplified routing solution for the CREI Investment Platform that allows partners to access deal functionality while staying within the `/partner/` URL context.

## Core Principle

Partners can only see, edit, and interact with their own deals - they should never have access to other partners' deals. The implementation follows the principle that "roles and permissions are there to interact with existing modules and not the other way around."

## Implementation

### 1. Route Configuration

Instead of duplicating dozens of routes in a separate file, we reuse the existing `admin.php` routes with different prefixes:

```php
// In RouteServiceProvider.php

// Admin routes
Route::prefix("admin")
    ->middleware(["web", "admin", "check.permission"])
    ->namespace($this->namespace . "\Admin")
    ->name("admin.")
    ->group(base_path("routes/admin.php"));
    
// Partner routes for admin functionality
Route::prefix("partner")
    ->middleware(["web", "auth:admin", "role:partner", "partner.deal.access"])
    ->namespace($this->namespace . "\Admin")
    ->name("partner.")
    ->group(base_path("routes/admin.php"));
```

### 2. Middleware Configuration

The `PartnerDealAccess` middleware ensures partners can only access their own deals:

```php
// In PartnerDealAccess.php

// Special handling for the deals listing routes
if ($request->route()->getName() === 'admin.deals.index' || 
    $request->route()->getName() === 'partner.deals.index') {
    // Get the partner's deal IDs
    $partnerDealIds = $this->partnerDealService->getPartnerDealIds($partnerId);
    
    // Share the partner's deal IDs with the controller
    $request->attributes->add(['partner_deal_ids' => $partnerDealIds]);
    
    return $next($request);
}

// For other routes that involve a specific deal
$dealId = null;

// Try to get the deal ID from the route parameter
if ($request->route("deal")) {
    $dealId = $request->route("deal")->id ?? $request->route("deal");
} else if ($request->input("deal_id")) {
    $dealId = $request->input("deal_id");
}

// If no deal ID is found in the request, proceed
if (!$dealId) {
    return $next($request);
}

// Check if the deal belongs to the partner
if (!$this->partnerDealService->checkPartnerHasDealAccess($partnerId, $dealId)) {
    abort(403, "You do not have access to this deal.");
}
```

## How It Works

1. **Reuse Existing Routes**: Instead of duplicating routes, we reuse the existing `admin.php` routes file.

2. **Change URL Prefix**: By changing the prefix from `admin` to `partner`, all routes will be accessible via `/partner/...` instead of `/admin/...`.

3. **Change Route Names**: By changing the name prefix from `admin.` to `partner.`, all route names will be `partner.deals.summary` instead of `admin.deals.summary`.

4. **Apply Partner Access Control**: The `partner.deal.access` middleware is applied to all routes, ensuring partners can only access their own deals.

## Benefits

1. **Minimal Changes**: Only requires changes to 2 files instead of duplicating dozens of routes.

2. **Future-Proof**: Any new routes added to the admin interface will automatically be available to partners.

3. **Maintains Security**: The `partner.deal.access` middleware ensures partners can only access their own deals.

4. **Follows Laravel Best Practices**: Uses route groups and middleware for clean, maintainable code.

## Affected Components

- **RouteServiceProvider**: Updated to reuse admin routes for partners
- **PartnerDealAccess Middleware**: Ensures partners can only access their own deals
- **PartnerDealService**: Provides methods to check partner access to deals

## Testing

To verify the implementation:

1. Log in as a partner
2. Navigate to `/partner/deals`
3. Click on a deal to view its summary
4. Verify the URL remains in the `/partner/` context
5. Test all deal-related functionality (classes, offerings, documents, etc.)

## Potential Issues and Solutions

### Permission Conflicts

Some admin routes may have permissions that partners shouldn't have. Solution: Use Laravel's Gates and Policies to add additional permission checks.

### View Differences

Some views may need to display different content for partners vs admins. Solution: Use conditional logic in views based on the user's role:

```php
@if(Auth::guard('admin')->user()->hasRole('partner'))
    <!-- Partner-specific content -->
@else
    <!-- Admin-specific content -->
@endif
```

## Conclusion

This implementation provides a clean, maintainable solution that follows Laravel best practices and ensures partners have the appropriate access to deal functionality while staying within the partner interface.
