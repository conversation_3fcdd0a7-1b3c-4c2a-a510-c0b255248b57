<?php
    $kycContent = getContent('kyc_instruction.content', true);
?>
<?php $__env->startSection('content'); ?>

    <div class="notice"></div>


    <?php if($user->kv == Status::KYC_UNVERIFIED && $user->kyc_rejection_reason): ?>
        <div class="mb-4">
            <div class="alert alert--custom mb-0 alert--danger" role="alert">
                <h6 class="alert-heading"><?php echo app('translator')->get('KYC Document Rejected'); ?></h6>
                <p class="alert-text">
                    <?php echo e(__(@$kycContent->data_values->kyc_reject)); ?>

                </p>
                <div class="d-flex align-items-center mt-3">
                    <button class="alert-link custom-alert-link custom-alert-secondary-link m-0 me-2" data-bs-toggle="modal"
                        data-bs-target="#kycRejectionReason">
                        <?php echo app('translator')->get('Show Reason'); ?>
                    </button>
                    <a class="alert-link custom-alert-link custom-alert-danger-link m-0"
                        href="<?php echo e(route('user.kyc.data')); ?>">
                        <?php echo app('translator')->get('See Kyc Data'); ?>
                    </a>
                </div>
            </div>
        </div>
    <?php elseif($user->kv == Status::KYC_UNVERIFIED): ?>
        <div class="mb-4">
            <div class="alert alert--custom mb-0 alert--info" role="alert">
                <h6 class="alert-heading"><?php echo app('translator')->get('KYC Verification required'); ?></h6>
                <p class="alert-text">
                    <?php echo e(__(@$kycContent->data_values->kyc_required)); ?>

                </p>
                <a class="alert-link custom-alert-link custom-alert-info-link" href="<?php echo e(route('user.kyc.form')); ?>">
                    <?php echo app('translator')->get('Click Here to Submit Documents'); ?>
                </a>
            </div>
        </div>
    <?php elseif($user->kv == Status::KYC_PENDING): ?>
        <div class="mb-4">
            <div class="alert alert--custom mb-0 alert--warning" role="alert">
                <h6 class="alert-heading"><?php echo app('translator')->get('KYC Verification pending'); ?></h6>
                <p class="alert-text">
                    <?php echo e(__(@$kycContent->data_values->kyc_pending)); ?>

                </p>
                <a class="alert-link custom-alert-link custom-alert-warning-link"
                    href="<?php echo e(route('user.kyc.data')); ?>"><?php echo app('translator')->get('See KYC Data'); ?></a>
            </div>
        </div>
    <?php endif; ?>

    

    
    <div class="row gy-4 dashboard-widget-wrapper mb-4 justify-content-center">
        <div class="col-xl-4 col-lg-6 col-sm-6 ">
            <div class="dashboard-widget flex-align">
                <div class="dashboard-widget__icon flex-center">
                    <i class="fas fa-donate"></i>
                </div>
                <div class="dashboard-widget__content">
                    <span class="dashboard-widget__text"><?php echo app('translator')->get('Balance'); ?></span>
                    <h6 class="dashboard-widget__number">
                        <?php echo e(showAmount(@$widget['balance'])); ?>

                    </h6>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-lg-6 col-sm-6 ">
            <div class="dashboard-widget flex-align">
                <div class="dashboard-widget__icon flex-center">
                    <i class="fas fa-money-check-alt"></i>
                </div>
                <div class="dashboard-widget__content">
                    <span class="dashboard-widget__text"><?php echo app('translator')->get('Total Deposit'); ?></span>
                    <h6 class="dashboard-widget__number">
                        <?php echo e(showAmount(@$widget['total_deposit'])); ?>

                    </h6>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-lg-6 col-sm-6 ">
            <div class="dashboard-widget flex-align">
                <div class="dashboard-widget__icon flex-center">
                    <i class="far fa-credit-card"></i>
                </div>
                <div class="dashboard-widget__content">
                    <span class="dashboard-widget__text"><?php echo app('translator')->get('Total Withdraw'); ?></span>
                    <h6 class="dashboard-widget__number">
                        <?php echo e(showAmount(@$widget['total_withdraw'])); ?>

                    </h6>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-lg-6 col-sm-6 ">
            <div class="dashboard-widget flex-align">
                <div class="dashboard-widget__icon flex-center">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="dashboard-widget__content">
                    <span class="dashboard-widget__text"><?php echo app('translator')->get('Total Investment'); ?></span>
                    <h6 class="dashboard-widget__number">
                        <?php echo e(showAmount(@$widget['total_investment'])); ?>

                    </h6>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-lg-6 col-sm-6 ">
            <div class="dashboard-widget flex-align">
                <div class="dashboard-widget__icon flex-center">
                    <i class="fas fa-hand-holding-usd"></i>
                </div>
                <div class="dashboard-widget__content">
                    <span class="dashboard-widget__text"><?php echo app('translator')->get('Total Profit'); ?></span>
                    <h6 class="dashboard-widget__number">
                        <?php echo e(showAmount(@$widget['total_profit'])); ?>

                    </h6>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-lg-6 col-sm-6 ">
            <div class="dashboard-widget flex-align">
                <div class="dashboard-widget__icon flex-center">
                    <i class="fas fa-city"></i>
                </div>
                <div class="dashboard-widget__content">
                    <span class="dashboard-widget__text"><?php echo app('translator')->get('Total Invested Property'); ?></span>
                    <h6 class="dashboard-widget__number">
                        <?php echo e(@$widget['total_property']); ?>

                    </h6>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-lg-6 col-sm-6 ">
            <div class="dashboard-widget flex-align">
                <div class="dashboard-widget__icon flex-center">
                    <i class="fas fa-bezier-curve"></i>
                </div>
                <div class="dashboard-widget__content">
                    <span class="dashboard-widget__text"><?php echo app('translator')->get('My Referrals'); ?></span>
                    <h6 class="dashboard-widget__number">
                        <?php echo e(@$widget['referral']); ?>

                    </h6>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-lg-6 col-sm-6 ">
            <div class="dashboard-widget flex-align">
                <div class="dashboard-widget__icon flex-center">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="dashboard-widget__content">
                    <span class="dashboard-widget__text"><?php echo app('translator')->get('Referral Commission'); ?></span>
                    <h6 class="dashboard-widget__number">
                        <?php echo e(showAmount(@$widget['referral_commission'])); ?>

                    </h6>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-lg-6 col-sm-6 ">
            <div class="dashboard-widget flex-align">
                <div class="dashboard-widget__icon flex-center">
                    <i class="fa fa-ticket-alt"></i>
                </div>
                <div class="dashboard-widget__content">
                    <span class="dashboard-widget__text"><?php echo app('translator')->get('Total Ticket'); ?></span>
                    <h6 class="dashboard-widget__number">
                        <?php echo e($widget['total_ticket']); ?>

                    </h6>
                </div>
            </div>
        </div>
    </div>
    <?php if($nextInstallment): ?>
        <div class="mb-4">
            <div class="flex-end mb-3 breadcrumb-dashboard">
                <h6 class="page-title"><?php echo app('translator')->get('Next Installment'); ?></h6>
            </div>
            <div class="row dashboard-widget-wrapper">
                <div class="col-md-12">
                    <div class="table-responsive table--responsive--xl">
                        <table class="table custom--table">
                            <thead>
                                <tr>
                                    <th><?php echo app('translator')->get('Property'); ?></th>
                                    <th><?php echo app('translator')->get('Installment Amount'); ?></th>
                                    <th><?php echo app('translator')->get('Installment Date'); ?></th>
                                    <th><?php echo app('translator')->get('Status'); ?></th>
                                    <th><?php echo app('translator')->get('Action'); ?></th>
                                </tr>
                            </thead>
                            <tr>
                                <td>
                                    <?php echo e(@$nextInstallment->invest->property->title); ?>

                                </td>
                                <td>
                                    <?php echo e(showAmount(@$nextInstallment->invest->per_installment_amount)); ?>

                                </td>
                                <td><?php echo e(showDateTime(@$nextInstallment->next_time, 'Y-m-d')); ?></td>
                                <td>
                                    <?php if(@$nextInstallment->status == Status::ENABLE): ?>
                                        <?php echo app('translator')->get('Completed'); ?>
                                    <?php else: ?>
                                        <?php echo app('translator')->get('Due'); ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <button class="btn btn-outline--primary action--btn" id="installmentBtn"
                                        data-action="<?php echo e(route('user.invest.installment.pay', [encrypt(@$nextInstallment->invest->id), encrypt(@$nextInstallment->id)])); ?>"
                                        title="Pay Installment">
                                        <i class="las la-coins"></i>
                                    </button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if(@$nextProfitSchedule): ?>
        <div class="flex-end mb-3 breadcrumb-dashboard">
            <h6 class="page-title"><?php echo app('translator')->get('Next Profit Schedule'); ?></h6>
        </div>
        <div class="row dashboard-widget-wrapper">
            <div class="col-md-12">
                <div class="table-responsive table--responsive--xl">
                    <table class="table custom--table">
                        <thead>
                            <tr>
                                <th><?php echo app('translator')->get('Property'); ?></th>
                                <th><?php echo app('translator')->get('Total Profit'); ?></th>
                                <th><?php echo app('translator')->get('Next Profit Date'); ?></th>
                            </tr>
                        </thead>
                        <tr>
                            <td>
                                <?php echo e(@$nextProfitSchedule->property->title); ?>

                            </td>
                            <td>
                                <?php echo e(showAmount(@$nextProfitSchedule->total_profit)); ?>

                            </td>
                            <td>
                                <div>
                                    <?php echo e(showDateTime(@$nextProfitSchedule->next_profit_date, 'Y-m-d')); ?><br>
                                    <span class="small"><?php echo e(diffForHumans($nextProfitSchedule->next_profit_date)); ?></span>
                                </div>
                            </td>
                        </tr>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php endif; ?>
    <?php if(@$nextInstallment): ?>
        <?php echo $__env->make($activeTemplate . 'partials.installment_modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php endif; ?>

    <?php if(auth()->user()->kv == Status::KYC_UNVERIFIED && auth()->user()->kyc_rejection_reason): ?>
        <div id="kycRejectionReason" class="modal fade custom--modal invest-modal">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header border-0 mb-2">
                        <div>
                            <h6 class="modal-title"><?php echo app('translator')->get('KYC Document Rejection Reason'); ?></h6>
                        </div>
                        <button class="close-btn" type="button" data-bs-dismiss="modal">
                            <i class="las fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p><?php echo e(auth()->user()->kyc_rejection_reason); ?></p>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    

<?php $__env->stopSection(); ?>

<?php $__env->startPush('style'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('assets/admin/css/stepper.css')); ?>">
<?php $__env->stopPush(); ?>


<?php echo $__env->make($activeTemplate . 'layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\PLATFORM\resources\views/templates/basic/user/dashboard.blade.php ENDPATH**/ ?>