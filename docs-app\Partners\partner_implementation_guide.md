# Partner Interface Implementation Guide

## Overview

This guide provides a comprehensive roadmap for implementing the Partner interface in the CRE Investment Platform. The implementation allows partners to manage their own deals through a dedicated interface while leveraging existing admin functionality.

## Core Principles

1. **Roles and permissions adapt to existing modules** - Not the other way around
2. **Minimal changes to existing code** - Reuse admin controllers and functionality
3. **Clear separation of concerns** - Partner-specific routes, middleware, and views
4. **Simple access control** - Partners can only access their own deals

## Implementation Timeline (One Day)

| Time | Task | Description |
|------|------|-------------|
| 9:00 - 9:30 | Database Setup | Create partner_deals table |
| 9:30 - 10:00 | Model Creation | Create PartnerDeal model |
| 10:00 - 11:00 | Middleware Implementation | Create PartnerDealAccess middleware |
| 11:00 - 12:00 | Route Configuration | Update RouteServiceProvider and create partner.php |
| 12:00 - 14:00 | Controller Modifications | Modify DealController to handle partner routes |
| 14:00 - 16:00 | View Templates | Create partner-specific views |
| 16:00 - 17:00 | Testing and Bug Fixing | Test the implementation and fix any issues |

## Step-by-Step Implementation

### 1. Database Setup

```bash
# Create migration for partner_deals table
php artisan make:migration create_partner_deals_table
```

Edit the migration file to define the table structure:

```php
Schema::create('partner_deals', function (Blueprint $table) {
    $table->id();
    $table->unsignedBigInteger('admin_id'); // Partner ID
    $table->unsignedBigInteger('deal_id');  // Deal ID
    $table->timestamps();
    
    $table->foreign('admin_id')->references('id')->on('admins');
    $table->foreign('deal_id')->references('id')->on('deals');
});
```

Run the migration:

```bash
php artisan migrate
```

### 2. Model Creation

Create the PartnerDeal model:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PartnerDeal extends Model
{
    protected $fillable = ['admin_id', 'deal_id'];
    
    public function admin()
    {
        return $this->belongsTo(Admin::class);
    }
    
    public function deal()
    {
        return $this->belongsTo(Deal::class);
    }
}
```

### 3. Middleware Implementation

Create the PartnerDealAccess middleware:

```php
<?php

namespace App\Http\Middleware;

use App\Models\PartnerDeal;
use Closure;
use Illuminate\Support\Facades\Auth;

class PartnerDealAccess
{
    public function handle($request, Closure $next)
    {
        $user = Auth::guard('admin')->user();
        
        // Only apply to partners
        if (!$user->hasRole('Partner')) {
            return $next($request);
        }
        
        // For index/list views, filter in the controller
        if ($request->route()->getName() == 'partner.deals.index' || 
            $request->route()->getName() == 'partner.dashboard') {
            return $next($request);
        }
        
        // For specific deal routes, check access
        $dealId = $request->route('deal');
        if ($dealId) {
            $hasDealAccess = PartnerDeal::where('admin_id', $user->id)
                ->where('deal_id', $dealId)
                ->exists();
                
            if (!$hasDealAccess) {
                return abort(403, 'You do not have access to this deal');
            }
        }
        
        return $next($request);
    }
}
```

Register the middleware in Kernel.php:

```php
protected $routeMiddleware = [
    // Other middleware...
    'partner.deal.access' => \App\Http\Middleware\PartnerDealAccess::class,
];
```

### 4. Route Configuration

Update RouteServiceProvider.php:

```php
// Partner routes
Route::prefix("partner")
    ->middleware(["web", "auth:admin", "role:partner"])
    ->namespace($this->namespace . "\\Admin")  // Use existing Admin controllers
    ->name("partner.")
    ->group(base_path("routes/partner.php"));
```

Create routes/partner.php:

```php
<?php

use App\Http\Controllers\Admin\DealController;
use Illuminate\Support\Facades\Route;

// Dashboard (uses the deals index as the dashboard)
Route::get('/dashboard', [DealController::class, 'index'])->name('dashboard');

// Deal routes with access control middleware
Route::group(['middleware' => ['partner.deal.access']], function () {
    // Deals
    Route::get('/deals', [DealController::class, 'index'])->name('deals.index');
    Route::get('/deals/create', [DealController::class, 'create'])->name('deals.create');
    Route::post('/deals', [DealController::class, 'store'])->name('deals.store');
    Route::get('/deals/{deal}', [DealController::class, 'show'])->name('deals.show');
    Route::get('/deals/{deal}/edit', [DealController::class, 'edit'])->name('deals.edit');
    Route::put('/deals/{deal}', [DealController::class, 'update'])->name('deals.update');
    Route::delete('/deals/{deal}', [DealController::class, 'destroy'])->name('deals.destroy');
});

// Profile routes
Route::get('/profile', [DealController::class, 'index'])->name('profile.index');
```

### 5. Controller Modifications

Modify DealController.php to handle partner routes:

```php
// Add these imports at the top of the file
use App\Models\PartnerDeal;
use Illuminate\Support\Facades\Auth;

// Modify the index method
public function index(Request $request)
{
    $user = Auth::guard('admin')->user();
    $isPartner = $user->hasRole('Partner');
    
    // For partners, only show their deals
    if ($isPartner) {
        $dealIds = PartnerDeal::where('admin_id', $user->id)->pluck('deal_id');
        $deals = Deal::whereIn('id', $dealIds)->get();
    } else {
        // For admins, show all deals
        $deals = Deal::all();
    }
    
    // Determine which view to use based on the route
    $routeName = $request->route()->getName();
    if (strpos($routeName, 'partner.') === 0) {
        return view('partner.deals.index', compact('deals'));
    }
    
    return view('admin.deals.index', compact('deals'));
}

// Modify the store method
public function store(Request $request)
{
    // Existing validation and deal creation
    $deal = Deal::create($request->validated());
    
    // If user is a partner, associate the deal with them
    $user = Auth::guard('admin')->user();
    if ($user->hasRole('Partner')) {
        PartnerDeal::create([
            'admin_id' => $user->id,
            'deal_id' => $deal->id
        ]);
    }
    
    // Redirect based on route name
    $routeName = $request->route()->getName();
    if (strpos($routeName, 'partner.') === 0) {
        return redirect()->route('partner.deals.index')
            ->with('success', 'Deal created successfully');
    }
    
    return redirect()->route('admin.deals.index')
        ->with('success', 'Deal created successfully');
}

// Similarly modify show, edit, update, and destroy methods
```

### 6. View Templates

Create the following view files:

- `resources/views/partner/layouts/app.blade.php`
- `resources/views/partner/layouts/partials/sidenav.blade.php`
- `resources/views/partner/layouts/partials/topnav.blade.php`
- `resources/views/partner/layouts/partials/footer.blade.php`
- `resources/views/partner/deals/index.blade.php`
- `resources/views/partner/deals/create.blade.php`
- `resources/views/partner/deals/edit.blade.php`
- `resources/views/partner/deals/show.blade.php`
- `resources/views/partner/profile/index.blade.php`

See the [Partner View Templates Guide](partner_view_templates.md) for detailed instructions.

### 7. Testing and Bug Fixing

Test the implementation thoroughly:

1. **Partner Login**: Verify that partners can log in and access the dashboard.
2. **Deal Listing**: Ensure that partners can only see their own deals.
3. **Deal Creation**: Test that new deals are automatically associated with the partner.
4. **Deal Editing**: Verify that partners can only edit their own deals.
5. **Access Control**: Test that partners cannot access deals they don't own.

## Troubleshooting Common Issues

### Route Not Found

- Clear the route cache: `php artisan optimize:clear`
- Verify that the partner routes are registered in RouteServiceProvider.php
- Check that the partner.php file exists and has the correct routes

### Middleware Not Applied

- Verify that the middleware is registered in Kernel.php
- Check that the middleware is applied to the routes in partner.php
- Ensure that the middleware logic is correct

### View Not Found

- Verify that all required view files exist in the correct locations
- Check that the controller is returning the correct view
- Ensure that the view names match the file paths

### Access Control Issues

- Verify that the PartnerDealAccess middleware is working correctly
- Check that the partner_deals table has the correct associations
- Ensure that the user has the Partner role

## Conclusion

This implementation follows the principle that "roles and permissions are there to interact with existing modules and not the other way around" by adapting the existing admin functionality for partner use. The approach minimizes changes to existing code while providing a clear separation of concerns between admin and partner interfaces.

By following this guide, you should be able to implement the Partner interface in a single day, allowing partners to manage their own deals through a dedicated interface.
