<?php

namespace App\Http\Controllers\User;


use App\Services\StripeACHService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\Controller;
use App\Models\Investment;
use App\Models\User; // Assuming User model is used for admin and investor
use Illuminate\Support\Facades\Http;
use App\Models\InvestmentTransaction;

class StripeACHController extends Controller
{
    protected $stripeService;

    public function __construct(StripeACHService $stripeService)
    {
        $this->stripeService = $stripeService;
    }

    public function initiateACH(Request $request, $id)
    {
        $entity = auth()->user() ?? auth('admin')->user();

        if ($entity->stripe_customer_id === null || $entity->stripe_customer_id === '') {
            $validate = Validator::make($request->all(), [
                'achForm.name' => 'required|string',
                'achForm.routing_number' => 'required|string',
                'achForm.account_number' => 'required|string',
                'achForm.account_type' => 'required|string',
            ]);
        }else{
            $validate = Validator::make($request->all(), [
                
            ]);
        }

        if ($validate->fails()) {
            return response()->json(['errors' => $validate->errors()], 422);
        }
        $investment = Investment::find($id);
        $offering = $investment->offering;
        if (!$offering) {
            return response()->json(['error' => 'Offering not found for this investment'], 404);
        }

        if (!$investment) {
            return response()->json(['error' => 'Investment not found'], 404);
        }
        if ($entity->stripe_customer_id === null || $entity->stripe_account_id === '') {
            $data = $validate->validated()['achForm']; // ✅ extract validated input
            $result = $this->stripeService->createACHPaymentMethod($data, null, $investment, $offering);

            $amountInDollars = $result['data']['amount'] / 100;
            $InvestmentTransactionConfirmation = InvestmentTransaction::create([
                'investment_id' => $investment->id,
                'stripe_transaction_id' => $result['data']['id'],
                'amount' => $amountInDollars,
                'status' => 'completed',
                'payment_method' => $investment->contribution_method,
            ]);
        } else {
            $rawAmount = $investment->getRawOriginal('investment_amount');
            $amountInCents = (int) round($rawAmount * 100);

            if ($amountInCents < 50) {
                throw new \Exception("Minimum investment amount must be at least $0.50");
            }

            $options = [
                'idempotency_key' => 'investor_charge_' . $entity->id . '_' . time(),
            ];
            $this->stripeService->chargeInvestor($amountInCents, $options = [], $entity);
            $AdminAccountId = $offering->deal->achsettings->stripe_account_id;
            //when payment intent is created then transfer funds to connected account
            // dd($AdminAccountId, $amountInCents);
            $result = $this->stripeService->transferToConnectedAccount($amountInCents, $AdminAccountId);

            $amountInDollars = $result['amount'] / 100;
            $InvestmentTransactionConfirmation = InvestmentTransaction::create([
                'investment_id' => $investment->id,
                'stripe_transaction_id' => $result['id'],
                'amount' => $amountInDollars,
                'status' => 'completed',
                'payment_method' => $investment->contribution_method,
            ]);
        }
        

        return response()->json(['status' => 'pending', 'data' => $result, 'investment_transaction' => $InvestmentTransactionConfirmation], 200);
    }





}
