# Commercial Real Estate (CRE) Investment Platform Documentation

## 1. Project Overview

### Project Type
- Laravel Investment Platform for CRE investments
- Environment: Development & Production
- Similar functionality to CashFlowPortal.com

### Core Components

1. **Deal Management System**
   - Location: app/Http/Controllers/Admin/DealController.php
   - Views: resources/views/admin/deals/
   - Features:
     - Deal creation and management
     - Class management
     - Offerings management
     - Document management

2. **Investment Management System**
   - Controllers:
     - InvestController.php
     - InvestmentController.php
     - InvestmentReportController.php
   - Features:
     - Investment tracking
     - Profit management
     - Reporting system

3. **Waterfall & Distribution System**
   - Controllers:
     - WaterFallController.php
     - DistributionsController.php
   - Features:
     - Distribution logic
     - Payment management
     - Deal integration

4. **Additional Core Systems**
   - Asset Management (AssetsController.php)
   - Property Management (PropertyController.php)
   - User Management (ManageUsersController.php)
   - Withdrawal System (WithdrawalController.php)
   - System Management (SystemController.php)

## 2. Technical Stack

### Framework & Frontend
- Laravel Framework
- Bootstrap
- Alpine.js (Main UI interactivity)
- Vue.js (Waterfall Distribution System)
  - Component-based architecture
  - Used in /admin/deals/waterfall/**
  - Vue 3 Composition API
- jQuery

### JavaScript Architecture
- Alpine.js: Main application interactivity
- Vue.js: Complex waterfall system components
  - Located in resources/js/components/
  - Core components:
    - Waterfall.vue (Main distribution management)
    - HurdleComponent.vue (Hurdle configuration)
    - Gp_Provision.vue (GP provisions handling)

### Asset Structure
- CSS: assets/admin/css/, assets/global/css/
- JS: assets/admin/js/, assets/global/js/
- Custom styles: custom.css and app.css

### Template Structure
- Base: resources/views/admin/layouts/master.blade.php
- Admin: resources/views/admin/layouts/app.blade.php
- Partials: sidenav.json, breadcrumb.blade.php, topnav.blade.php

## 3. Module Organization

### Admin Categories
- Dashboard
- Deals
- Offerings
- Investment
- Assets
- Manage Properties
- Manage Users
- Deposits
- Withdrawals
- Documents
- Emails
- Updates
- Support Ticket
- Report
- Subscribers
- Manage Referral
- System Settings
- Extra
- Report & Request

## 4. Protected Areas

### Core Directories (NEVER MODIFY WITHOUT PERMISSION)
- /admin/deals/**
- /admin/deals/waterfall/**
- Authentication system files
- Payment processing systems
- Custom middleware implementations

### Critical Components
- Onumoti functionality
- Authentication mechanisms
- Middleware configurations
- Constructor methods in base classes

## 5. Development Protocol

### Pre-Change Verification
- CHECK git history for last 24-48 hours
- VERIFY file is not in protected directories
- REVIEW all dependencies
- TEST complete authentication flow
- DOCUMENT proposed changes

### Code Modification Rules
- PRESERVE existing template layout
- MAINTAIN existing structure
- NEVER assume Laravel defaults are better than custom implementations
- KEEP all custom functionality (Onumoti, etc.)
- TEST complete user flows before/after changes

### Version Control Requirements
- SHOW git diff before committing
- REMOVE unintended merge commits
- VERIFY no protected directories affected
- CLEAN commits to include only relevant files
- CHECK recent commit history

### Authentication Guidelines
- PRESERVE Onumoti constructor
- MAINTAIN existing middleware
- VERIFY login/logout flow
- DOCUMENT all changes
- TEST complete authentication cycle

## 6. Best Practices

### Code Review
- Examine blade templates
- Check routes/admin.php
- Review controllers
- Verify visibility
- Document dependencies

### Error Prevention
- Create backups before changes
- Test page loading
- Document all changes
- Clean up unintended modifications
- Remove problematic merge commits

## 7. Reference & Support

### Best Practices
- Error Resolution: Request chrome access after three attempts
- Development Protocol: Create backups before major changes
- Quality Assurance: Test page loading after changes
- Documentation: Keep all changes documented
- Version Control: Always check 24-hour git history
- Protected Areas: Never modify without explicit permission
- Authentication: Always test complete flow
- Commits: Clean up unintended changes

### Access Points
```
Admin Portal: http://localhost:8000/admin/
  Username: admin
  Password: NXkLKbLsfWz6

User Portal: http://localhost:8000/user/login
  Username: pinecrest
  Password: NXkLKbLsfWz6

Live Reference: https://mycompanyllc.cashflowportal.com/app/underwritings/1b9e47e6-00f2-437a-8567-93e2d95056af
  Email: <EMAIL>
  Password: NXkLKbLsfWz6
```

### Reference URLs
```
Data Sources: https://www.cashflowportal.com/app/
Deals: https://www.cashflowportal.com/app
Offerings: https://www.cashflowportal.com/app/deals/{id}?tab=offerings&view=table
Classes: https://www.cashflowportal.com/app/deals/{id}?tab=classes&view=table
Investments: https://www.cashflowportal.com/app/deals/{id}?tab=investments&view=table
Assets: https://www.cashflowportal.com/app/deals/{id}?tab=assets&view=table
Distributions: https://www.cashflowportal.com/app/deals/{id}?tab=distributions&view=table
Distribution Settings: https://www.cashflowportal.com/app/deals/{id}/entities/{entity_id}/classes/edit?tab=distributionwaterfalls
Documents: https://www.cashflowportal.com/app/deals/{id}?tab=documents&view=table
Valuation Forms: https://www.cashflowportal.com/app/deals/{id}?tab=valuationforms&view=table
Updates: https://www.cashflowportal.com/app/deals/{id}?tab=updates&view=table
Members: https://www.cashflowportal.com/app/deals/{id}?tab=members&view=table
KPIs: https://www.cashflowportal.com/app/deals/{id}?tab=kpis&view=table
Manage Deal: https://www.cashflowportal.com/app/deals/{id}/edit
