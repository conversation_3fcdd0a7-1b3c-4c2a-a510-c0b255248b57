# Testing Plan for Organizational Roles & Permissions Implementation

## Overview

This document outlines the testing strategy for verifying that the organizational roles and permissions system works as expected after implementation. The testing plan ensures that all roles have appropriate access levels and that the hierarchical permission structure is properly enforced.

## Test Environment Setup

1. Create a clean testing environment with the updated code
2. Run database migrations and seeders:
   ```bash
   php artisan migrate:fresh --seed
   php artisan db:seed --class=RoleAndPermissionSeeder
   ```
3. Create test users for each organizational role:
   - Super Admin
   - CEO
   - CFO, COO, CTO, CLO (C-Suite)
   - Department Managers (one for each department)
   - Regular Staff

## Test Cases

### 1. Role Creation and Assignment

| Test ID | Description | Expected Result |
|---------|-------------|----------------|
| RC-01 | Create a new role with specific permissions | Role is created with selected permissions |
| RC-02 | Assign a role to a staff member | Staff member receives all permissions associated with the role |
| RC-03 | Attempt to delete a protected role (Super <PERSON><PERSON>, CEO, etc.) | Operation is blocked with appropriate error message |
| RC-04 | Attempt to edit a protected role as non-Super Admin | Operation is blocked with appropriate error message |
| RC-05 | Create a new staff member with an organizational role | Staff member is created with the correct role and permissions |

### 2. Permission Middleware Tests

| Test ID | Description | Expected Result |
|---------|-------------|----------------|
| PM-01 | Access a protected route as Super Admin | Access is granted without permission check |
| PM-02 | Access a protected route as CEO | Access is granted for all non-system routes |
| PM-03 | Access a department-specific route as department executive | Access is granted for routes in their department |
| PM-04 | Access a department-specific route as department manager | Access is granted for routes in their department |
| PM-05 | Access a department-specific route as staff with specific permission | Access is granted only for routes with explicit permission |
| PM-06 | Access a route without required permission | Access is denied with 403 error |

### 3. Hierarchical Permission Tests

| Test ID | Description | Expected Result |
|---------|-------------|----------------|
| HP-01 | CFO accessing accounting resources | Access is granted |
| HP-02 | CFO accessing marketing resources | Access is denied |
| HP-03 | COO accessing deals resources | Access is granted |
| HP-04 | CTO accessing system resources | Access is granted |
| HP-05 | CLO accessing compliance resources | Access is granted |
| HP-06 | Department manager accessing their department resources | Access is granted |
| HP-07 | Department manager accessing another department's resources | Access is denied |

### 4. UI Tests

| Test ID | Description | Expected Result |
|---------|-------------|----------------|
| UI-01 | View roles page with organizational hierarchy | Roles are displayed grouped by organizational category |
| UI-02 | Create a new role with categorized permissions | UI shows permissions grouped by department |
| UI-03 | Edit a role with categorized permissions | UI shows current permissions and allows changes |
| UI-04 | Create a new staff member with role selection | UI shows roles grouped by organizational hierarchy |

### 5. Edge Cases

| Test ID | Description | Expected Result |
|---------|-------------|----------------|
| EC-01 | User with multiple roles (e.g., CFO and Accounting Manager) | User has combined permissions from both roles |
| EC-02 | Permission with no department prefix (e.g., 'view_dashboard') | Permission is properly handled in UI and middleware |
| EC-03 | Role with no permissions | Role is created but has no access to protected routes |
| EC-04 | Attempt to access a route with non-existent permission | 403 error is returned |

## Test Execution

### Manual Testing Procedure

1. Log in as each test user
2. Attempt to access various routes based on the test cases
3. Verify that access is granted or denied as expected
4. Test the role management UI for proper display and functionality
5. Test the staff creation UI for proper role selection

### Automated Testing

Create feature tests for critical functionality:

```php
// Example test for Super Admin access
public function test_super_admin_can_access_all_routes()
{
    $superAdmin = Admin::factory()->create();
    $superAdminRole = Role::where('name', 'Super Admin')->first();
    $superAdmin->assignRole($superAdminRole);
    
    $this->actingAs($superAdmin, 'admin')
         ->get(route('admin.staff.index'))
         ->assertStatus(200);
         
    $this->actingAs($superAdmin, 'admin')
         ->get(route('admin.settings.index'))
         ->assertStatus(200);
         
    // Test additional routes...
}

// Example test for department manager access
public function test_department_manager_can_only_access_their_department()
{
    $accountingManager = Admin::factory()->create();
    $accountingManagerRole = Role::where('name', 'Accounting Manager')->first();
    $accountingManager->assignRole($accountingManagerRole);
    
    // Should have access to accounting routes
    $this->actingAs($accountingManager, 'admin')
         ->get(route('admin.accounting.index'))
         ->assertStatus(200);
         
    // Should not have access to marketing routes
    $this->actingAs($accountingManager, 'admin')
         ->get(route('admin.marketing.index'))
         ->assertStatus(403);
}
```

## Validation Criteria

The implementation is considered successful when:

1. All test cases pass
2. Super Admin has access to all resources
3. C-Suite executives have access to their departmental resources
4. Department managers have access to their department's resources
5. Staff members have access only to resources they're explicitly granted permission for
6. The UI properly displays roles and permissions by organizational hierarchy
7. Protected roles cannot be edited or deleted by unauthorized users

## Troubleshooting Common Issues

1. **Permission Cache**: If permissions aren't working as expected, clear the permission cache:
   ```bash
   php artisan cache:clear
   php artisan permission:cache-reset
   ```

2. **Guard Name Mismatch**: Ensure all permissions and roles use the correct guard name ('admin'):
   ```php
   // Check guard names in database
   $roles = Role::all();
   $permissions = Permission::all();
   foreach ($roles as $role) {
       echo $role->name . ': ' . $role->guard_name . '\n';
   }
   ```

3. **Middleware Registration**: Verify middleware is properly registered in bootstrap/app.php:
   ```php
   // Check middleware registration
   $app->alias('permission', \App\Http\Middleware\PermissionMiddleware::class);
   ```

4. **Route Protection**: Ensure routes have the correct middleware applied:
   ```php
   // Example of correctly protected route
   Route::get('/staff', [StaffController::class, 'index'])->middleware('permission:staff.view');
   ```
