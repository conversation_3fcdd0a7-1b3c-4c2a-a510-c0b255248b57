# Blade Section Naming Issue in Admin Panel

## Problem Description

When implementing the Offering Questionnaire module, we encountered an issue where the pages would load without the sidebar and top navigation bar. The content was visible, but it appeared as a blank page without the admin panel layout structure.

This issue occurred despite:
- Correctly defining routes in `routes/admin.php`
- Adding the appropriate middleware permissions
- Creating the necessary database permissions
- Correctly adding entries to `resources/views/admin/partials/sidenav.json`

## Root Cause

The issue stemmed from a mismatch between how the admin layout system defines content sections and how they were being used in the new module:

1. In the admin layout system (`resources/views/admin/layouts/app.blade.php`), the main content area is defined using:
   ```php
   @yield('panel')
   ```

2. However, the Offering Questionnaire views were incorrectly using:
   ```php
   @section('content')
   ```

3. Because of this mismatch, the content from the Offering Questionnaire views was never being rendered in the layout. <PERSON><PERSON>'s Blade templating engine was looking for content in a section called 'panel', but the views were defining their content in a section called 'content'.

## Solution

The solution was to update all view files in the Offering Questionnaire module to use the correct section name:

```php
@section('panel')
```

instead of:

```php
@section('content')
```

This change was needed in:
- `resources/views/admin/offering-questionnaires/index.blade.php`
- `resources/views/admin/offering-questionnaires/create.blade.php`
- `resources/views/admin/offering-questionnaires/edit.blade.php`
- `resources/views/admin/offering-questionnaires/preview.blade.php`

## Lessons Learned

1. **Understand Layout Structure**: Before creating new views, understand how the layout system is structured and which section names are being used.

2. **Follow Existing Patterns**: Look at existing views in the codebase to understand the template structure and naming conventions being used.

3. **Check Section Names**: When a view appears to be loading without the proper layout, one of the first things to check is whether the section names match between the layout file and the view files.

4. **Template Debugging**: When facing layout issues, add debugging statements in the layout file to verify which sections are being yielded and which sections are being defined.

## Prevention Strategy

To prevent this issue in the future:

1. Create a template documentation that clearly specifies which section names should be used in different parts of the application.

2. Consider using a starter template or scaffolding command when creating new modules to ensure consistency.

3. Implement a code review process that specifically checks for template structure consistency.

4. Add comments in the layout files indicating which section names should be used.
