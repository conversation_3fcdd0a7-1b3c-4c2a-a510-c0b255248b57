# Partner Interface Implementation Plan

## Overview

This document outlines a streamlined approach to implement the Partner interface in the CRE Investment Platform. The implementation will allow partners to manage their own deals through a dedicated partner interface while leveraging existing admin functionality.

## Implementation Principles

1. **Roles and permissions adapt to existing modules** - Not the other way around
2. **Reuse existing admin controllers** - Minimize code duplication
3. **Separate partner-specific routes and views** - Clear separation of concerns
4. **Simple middleware for access control** - Partners can only access their own deals

## Implementation Steps

### 1. Database Setup

#### Create partner_deals table

```php
Schema::create('partner_deals', function (Blueprint $table) {
    $table->id();
    $table->unsignedBigInteger('admin_id'); // Partner ID
    $table->unsignedBigInteger('deal_id');  // Deal ID
    $table->timestamps();
    
    $table->foreign('admin_id')->references('id')->on('admins');
    $table->foreign('deal_id')->references('id')->on('deals');
});
```

### 2. Model Creation

#### PartnerDeal Model

```php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PartnerDeal extends Model
{
    protected $fillable = ['admin_id', 'deal_id'];
    
    public function admin()
    {
        return $this->belongsTo(Admin::class);
    }
    
    public function deal()
    {
        return $this->belongsTo(Deal::class);
    }
}
```

### 3. Middleware Implementation

#### PartnerDealAccess Middleware

```php
namespace App\Http\Middleware;

use App\Models\PartnerDeal;
use Closure;
use Illuminate\Support\Facades\Auth;

class PartnerDealAccess
{
    public function handle($request, Closure $next)
    {
        $user = Auth::guard('admin')->user();
        
        // Only apply to partners
        if (!$user->hasRole('Partner')) {
            return $next($request);
        }
        
        // For index/list views, filter in the controller
        if ($request->route()->getName() == 'partner.deals.index' || 
            $request->route()->getName() == 'partner.dashboard') {
            return $next($request);
        }
        
        // For specific deal routes, check access
        $dealId = $request->route('deal');
        if ($dealId) {
            $hasDealAccess = PartnerDeal::where('admin_id', $user->id)
                ->where('deal_id', $dealId)
                ->exists();
                
            if (!$hasDealAccess) {
                return abort(403, 'You do not have access to this deal');
            }
        }
        
        return $next($request);
    }
}
```

### 4. Route Configuration

#### RouteServiceProvider.php Update

```php
// In app/Providers/RouteServiceProvider.php

// Partner routes
Route::prefix("partner")
    ->middleware(["web", "auth:admin", "role:partner"])
    ->namespace($this->namespace . "\\Admin")  // Use existing Admin controllers
    ->name("partner.")
    ->group(base_path("routes/partner.php"));
```

#### partner.php Routes File

```php
<?php

use App\Http\Controllers\Admin\DealController;
use Illuminate\Support\Facades\Route;

Route::get('/dashboard', [DealController::class, 'index'])->name('dashboard');

Route::group(['middleware' => ['partner.deal.access']], function () {
    // Deals
    Route::get('/deals', [DealController::class, 'index'])->name('deals.index');
    Route::get('/deals/create', [DealController::class, 'create'])->name('deals.create');
    Route::post('/deals', [DealController::class, 'store'])->name('deals.store');
    Route::get('/deals/{deal}', [DealController::class, 'show'])->name('deals.show');
    Route::get('/deals/{deal}/edit', [DealController::class, 'edit'])->name('deals.edit');
    Route::put('/deals/{deal}', [DealController::class, 'update'])->name('deals.update');
    Route::delete('/deals/{deal}', [DealController::class, 'destroy'])->name('deals.destroy');
});
```

### 5. Controller Modifications

#### DealController Modifications

```php
// In app/Http/Controllers/Admin/DealController.php

public function index(Request $request)
{
    $user = Auth::guard('admin')->user();
    $isPartner = $user->hasRole('Partner');
    
    // For partners, only show their deals
    if ($isPartner) {
        $dealIds = PartnerDeal::where('admin_id', $user->id)->pluck('deal_id');
        $deals = Deal::whereIn('id', $dealIds)->get();
    } else {
        // For admins, show all deals
        $deals = Deal::all();
    }
    
    // Determine which view to use based on the route
    $routeName = $request->route()->getName();
    if (strpos($routeName, 'partner.') === 0) {
        return view('partner.deals.index', compact('deals'));
    }
    
    return view('admin.deals.index', compact('deals'));
}

public function store(Request $request)
{
    // Existing validation and deal creation
    $deal = Deal::create($request->validated());
    
    // If user is a partner, associate the deal with them
    $user = Auth::guard('admin')->user();
    if ($user->hasRole('Partner')) {
        PartnerDeal::create([
            'admin_id' => $user->id,
            'deal_id' => $deal->id
        ]);
    }
    
    $routeName = $request->route()->getName();
    if (strpos($routeName, 'partner.') === 0) {
        return redirect()->route('partner.deals.index');
    }
    
    return redirect()->route('admin.deals.index');
}
```

### 6. View Templates

#### Partner Layout Template

Create a partner-specific layout by copying and modifying the admin layout:

```
resources/views/partner/layouts/app.blade.php
```

#### Partner Sidebar Navigation

```
resources/views/partner/layouts/sidebar.blade.php
```

#### Partner Deal Views

Copy and modify the admin deal views:

```
resources/views/partner/deals/index.blade.php
resources/views/partner/deals/create.blade.php
resources/views/partner/deals/edit.blade.php
resources/views/partner/deals/show.blade.php
```

## Implementation Timeline

1. **Database Setup** - 30 minutes
2. **Model Creation** - 30 minutes
3. **Middleware Implementation** - 1 hour
4. **Route Configuration** - 1 hour
5. **Controller Modifications** - 2 hours
6. **View Templates** - 2 hours
7. **Testing and Bug Fixing** - 1 hour

## Expected Outcome

Partners will be able to:
1. Log in to a dedicated partner interface at `/partner/dashboard`
2. View only their own deals
3. Create new deals (automatically associated with them)
4. Edit and manage their own deals

This implementation follows the principle that "roles and permissions are there to interact with existing modules and not the other way around" by adapting the existing admin functionality for partner use.
