# Extension System Testing Guide

## 1. Overview

This document provides guidelines and procedures for testing the Extension System and extensions built for it. It covers unit testing, integration testing, and end-to-end testing approaches.

## 2. Testing Framework

The Extension System uses PHPUnit for testing, which is the standard testing framework for Laravel applications. Each extension should include its own test suite to ensure proper functionality.

## 3. Test Types

### 3.1 Unit Tests

Unit tests focus on testing individual components in isolation. For the Extension System, unit tests should cover:

- Core classes (ExtensionRegistry, HookSystem, ExtensionLoader, etc.)
- Extension models
- Service classes
- Utility functions

### 3.2 Integration Tests

Integration tests verify that different components work together correctly. For the Extension System, integration tests should cover:

- Extension installation and activation
- Hook registration and execution
- Database interactions
- Service provider registration

### 3.3 Feature Tests

Feature tests simulate user interactions with the system. For the Extension System, feature tests should cover:

- Admin interface for managing extensions
- Extension-specific features (e.g., Loopnet search and import)
- API endpoints

## 4. Test Environment Setup

### 4.1 PHPUnit Configuration

Create a `phpunit.xml` file in your extension directory with the following configuration:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="./vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
>
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
        <testsuite name="Integration">
            <directory suffix="Test.php">./tests/Integration</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>
    </testsuites>
    <coverage processUncoveredFiles="true">
        <include>
            <directory suffix=".php">./src</directory>
        </include>
    </coverage>
    <php>
        <server name="APP_ENV" value="testing"/>
        <server name="BCRYPT_ROUNDS" value="4"/>
        <server name="CACHE_DRIVER" value="array"/>
        <server name="DB_CONNECTION" value="sqlite"/>
        <server name="DB_DATABASE" value=":memory:"/>
        <server name="MAIL_MAILER" value="array"/>
        <server name="QUEUE_CONNECTION" value="sync"/>
        <server name="SESSION_DRIVER" value="array"/>
        <server name="TELESCOPE_ENABLED" value="false"/>
    </php>
</phpunit>
```

### 4.2 Test Database Setup

For integration and feature tests, you'll need to set up a test database. The configuration above uses an in-memory SQLite database, which is ideal for testing.

### 4.3 Test Case Base Class

Create a base test case class for your extension tests:

```php
namespace Tests;

use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use App\Extensions\ExtensionServiceProvider;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Run migrations
        $this->artisan('migrate');
        
        // Register your extension service provider
        $this->app->register(YourExtensionServiceProvider::class);
    }
    
    protected function tearDown(): void
    {
        // Clean up after tests
        $this->artisan('migrate:reset');
        
        parent::tearDown();
    }
}
```

## 5. Testing Core Components

### 5.1 Testing ExtensionRegistry

```php
namespace Tests\Unit;

use Tests\TestCase;
use App\Extensions\ExtensionRegistry;
use App\Extensions\Extension;

class ExtensionRegistryTest extends TestCase
{
    protected ExtensionRegistry $registry;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->registry = app(ExtensionRegistry::class);
    }
    
    public function testRegisterExtension()
    {
        // Create a mock extension
        $extension = new Extension([
            'name' => 'Test Extension',
            'code' => 'test-extension',
            'version' => '1.0.0',
            'is_enabled' => true
        ]);
        
        // Register the extension
        $this->registry->register($extension);
        
        // Verify the extension was registered
        $this->assertTrue($this->registry->has('test-extension'));
        $this->assertSame($extension, $this->registry->get('test-extension'));
    }
    
    public function testGetEnabledExtensions()
    {
        // Create and register mock extensions
        $extension1 = new Extension([
            'name' => 'Test Extension 1',
            'code' => 'test-extension-1',
            'version' => '1.0.0',
            'is_enabled' => true
        ]);
        
        $extension2 = new Extension([
            'name' => 'Test Extension 2',
            'code' => 'test-extension-2',
            'version' => '1.0.0',
            'is_enabled' => false
        ]);
        
        $this->registry->register($extension1);
        $this->registry->register($extension2);
        
        // Get enabled extensions
        $enabled = $this->registry->enabled();
        
        // Verify only the enabled extension is returned
        $this->assertCount(1, $enabled);
        $this->assertSame($extension1, $enabled[0]);
    }
}
```

### 5.2 Testing HookSystem

```php
namespace Tests\Unit;

use Tests\TestCase;
use App\Extensions\HookSystem;

class HookSystemTest extends TestCase
{
    protected HookSystem $hookSystem;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->hookSystem = app(HookSystem::class);
    }
    
    public function testRegisterAndExecuteHook()
    {
        // Register a hook handler
        $this->hookSystem->register('test.hook', function($data) {
            $data['value'] += 1;
            return $data;
        }, 10, 'test-extension');
        
        // Execute the hook
        $data = ['value' => 1];
        $result = $this->hookSystem->execute('test.hook', $data);
        
        // Verify the hook modified the data
        $this->assertEquals(2, $result['value']);
    }
    
    public function testHookPriority()
    {
        // Register hooks with different priorities
        $this->hookSystem->register('test.hook', function($data) {
            $data['order'][] = 'second';
            return $data;
        }, 20, 'test-extension');
        
        $this->hookSystem->register('test.hook', function($data) {
            $data['order'][] = 'first';
            return $data;
        }, 10, 'test-extension');
        
        $this->hookSystem->register('test.hook', function($data) {
            $data['order'][] = 'third';
            return $data;
        }, 30, 'test-extension');
        
        // Execute the hook
        $data = ['order' => []];
        $result = $this->hookSystem->execute('test.hook', $data);
        
        // Verify the hooks executed in priority order
        $this->assertEquals(['first', 'second', 'third'], $result['order']);
    }
}
```

## 6. Testing Extension Installation

```php
namespace Tests\Integration;

use Tests\TestCase;
use App\Extensions\ExtensionManager;
use App\Extensions\ExtensionRegistry;

class ExtensionInstallationTest extends TestCase
{
    protected ExtensionManager $manager;
    protected ExtensionRegistry $registry;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->manager = app(ExtensionManager::class);
        $this->registry = app(ExtensionRegistry::class);
    }
    
    public function testInstallExtension()
    {
        // Create a test extension package
        $packagePath = $this->createTestExtensionPackage();
        
        // Install the extension
        $result = $this->manager->install($packagePath);
        
        // Verify the extension was installed
        $this->assertTrue($result);
        $this->assertTrue($this->registry->has('test-extension'));
        
        // Verify the extension is in the database
        $this->assertDatabaseHas('extensions', [
            'code' => 'test-extension',
            'name' => 'Test Extension',
            'version' => '1.0.0'
        ]);
    }
    
    public function testEnableExtension()
    {
        // Install a test extension
        $packagePath = $this->createTestExtensionPackage();
        $this->manager->install($packagePath);
        
        // Enable the extension
        $result = $this->manager->enable('test-extension');
        
        // Verify the extension was enabled
        $this->assertTrue($result);
        $this->assertTrue($this->registry->get('test-extension')->isEnabled());
        
        // Verify the extension is enabled in the database
        $this->assertDatabaseHas('extensions', [
            'code' => 'test-extension',
            'is_enabled' => true
        ]);
    }
    
    protected function createTestExtensionPackage()
    {
        // Create a temporary directory for the test extension
        $tempDir = sys_get_temp_dir() . '/test-extension';
        mkdir($tempDir, 0777, true);
        
        // Create a manifest.json file
        file_put_contents($tempDir . '/manifest.json', json_encode([
            'name' => 'Test Extension',
            'code' => 'test-extension',
            'version' => '1.0.0',
            'description' => 'A test extension',
            'author' => 'Test Author',
            'author_url' => 'https://example.com',
            'providers' => [
                'Tests\\TestExtension\\TestExtensionServiceProvider'
            ]
        ]));
        
        // Create a service provider file
        mkdir($tempDir . '/src', 0777, true);
        file_put_contents($tempDir . '/src/TestExtensionServiceProvider.php', <<<'PHP'
<?php

namespace Tests\TestExtension;

use App\Extensions\ExtensionServiceProvider;

class TestExtensionServiceProvider extends ExtensionServiceProvider
{
    protected $extensionCode = 'test-extension';
    
    public function register()
    {
        // Register services
    }
    
    public function boot()
    {
        // Boot services
    }
}
PHP
        );
        
        // Create a zip file
        $zipFile = sys_get_temp_dir() . '/test-extension.zip';
        $zip = new \ZipArchive();
        $zip->open($zipFile, \ZipArchive::CREATE | \ZipArchive::OVERWRITE);
        
        $files = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($tempDir),
            \RecursiveIteratorIterator::LEAVES_ONLY
        );
        
        foreach ($files as $file) {
            if (!$file->isDir()) {
                $filePath = $file->getRealPath();
                $relativePath = substr($filePath, strlen($tempDir) + 1);
                $zip->addFile($filePath, $relativePath);
            }
        }
        
        $zip->close();
        
        return $zipFile;
    }
}
```

## 7. Testing Hooks Integration

```php
namespace Tests\Integration;

use Tests\TestCase;
use App\Extensions\HookSystem;
use App\Models\Deal;

class HooksIntegrationTest extends TestCase
{
    protected HookSystem $hookSystem;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->hookSystem = app(HookSystem::class);
    }
    
    public function testDealCreateHooks()
    {
        // Register hooks
        $this->hookSystem->register('deal.create.before', function($data) {
            $data['name'] = 'Modified ' . $data['name'];
            return $data;
        }, 10, 'test-extension');
        
        $this->hookSystem->register('deal.create.after', function($deal) {
            $deal->description = 'Modified description';
            $deal->save();
            return $deal;
        }, 10, 'test-extension');
        
        // Create a deal with hooks
        $data = ['name' => 'Test Deal', 'description' => 'Original description'];
        $data = $this->hookSystem->execute('deal.create.before', $data);
        
        $deal = Deal::create($data);
        $deal = $this->hookSystem->execute('deal.create.after', $deal);
        
        // Verify the hooks modified the deal
        $this->assertEquals('Modified Test Deal', $deal->name);
        $this->assertEquals('Modified description', $deal->description);
    }
}
```

## 8. Testing Admin Interface

```php
namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Extensions\Extension;
use Illuminate\Foundation\Testing\WithFaker;

class AdminExtensionInterfaceTest extends TestCase
{
    use WithFaker;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Create an admin user
        $this->admin = User::factory()->create([
            'role' => 'admin'
        ]);
    }
    
    public function testExtensionListPage()
    {
        // Create some test extensions
        Extension::create([
            'name' => 'Test Extension 1',
            'code' => 'test-extension-1',
            'version' => '1.0.0',
            'is_enabled' => true,
            'manifest' => json_encode([
                'name' => 'Test Extension 1',
                'code' => 'test-extension-1',
                'version' => '1.0.0'
            ])
        ]);
        
        Extension::create([
            'name' => 'Test Extension 2',
            'code' => 'test-extension-2',
            'version' => '1.0.0',
            'is_enabled' => false,
            'manifest' => json_encode([
                'name' => 'Test Extension 2',
                'code' => 'test-extension-2',
                'version' => '1.0.0'
            ])
        ]);
        
        // Visit the extension list page
        $response = $this->actingAs($this->admin)
                         ->get('/admin/extensions');
        
        // Verify the page loads and shows the extensions
        $response->assertStatus(200);
        $response->assertSee('Test Extension 1');
        $response->assertSee('Test Extension 2');
        $response->assertSee('Enabled');
        $response->assertSee('Disabled');
    }
    
    public function testEnableExtension()
    {
        // Create a test extension
        $extension = Extension::create([
            'name' => 'Test Extension',
            'code' => 'test-extension',
            'version' => '1.0.0',
            'is_enabled' => false,
            'manifest' => json_encode([
                'name' => 'Test Extension',
                'code' => 'test-extension',
                'version' => '1.0.0'
            ])
        ]);
        
        // Enable the extension
        $response = $this->actingAs($this->admin)
                         ->post('/admin/extensions/test-extension/enable');
        
        // Verify the extension was enabled
        $response->assertStatus(302); // Redirect
        $this->assertDatabaseHas('extensions', [
            'code' => 'test-extension',
            'is_enabled' => true
        ]);
    }
}
```

## 9. Testing Loopnet Extension

### 9.1 Testing Loopnet API Client

```php
namespace Tests\Unit;

use Tests\TestCase;
use Acme\Loopnet\Services\LoopnetApiClient;
use Illuminate\Support\Facades\Http;

class LoopnetApiClientTest extends TestCase
{
    protected LoopnetApiClient $client;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock the HTTP client
        Http::fake([
            'api.loopnet.com/*' => Http::response([
                'listings' => [
                    [
                        'id' => '12345',
                        'title' => 'Test Property',
                        'price' => 1000000,
                        'address' => '123 Main St',
                        'city' => 'New York',
                        'state' => 'NY',
                        'zip' => '10001'
                    ]
                ],
                'total' => 1,
                'page' => 1,
                'per_page' => 10
            ], 200)
        ]);
        
        // Create the API client
        $this->client = new LoopnetApiClient([
            'api_key' => 'test_api_key',
            'api_secret' => 'test_api_secret'
        ]);
    }
    
    public function testSearch()
    {
        // Perform a search
        $results = $this->client->search([
            'location' => 'New York, NY',
            'property_type' => 'office',
            'min_price' => 500000,
            'max_price' => 2000000
        ]);
        
        // Verify the search results
        $this->assertCount(1, $results['listings']);
        $this->assertEquals('Test Property', $results['listings'][0]['title']);
        $this->assertEquals(1000000, $results['listings'][0]['price']);
    }
}
```

### 9.2 Testing Loopnet Import

```php
namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Deal;
use App\Models\Asset;
use Acme\Loopnet\Models\LoopnetListing;
use Illuminate\Foundation\Testing\WithFaker;

class LoopnetImportTest extends TestCase
{
    use WithFaker;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Create an admin user
        $this->admin = User::factory()->create([
            'role' => 'admin'
        ]);
    }
    
    public function testImportListing()
    {
        // Create a test listing
        $listing = LoopnetListing::create([
            'loopnet_id' => '12345',
            'title' => 'Test Property',
            'description' => 'A test property',
            'property_type' => 'office',
            'status' => 'active',
            'price' => 1000000,
            'price_per_sqft' => 250,
            'building_size' => 4000,
            'lot_size' => 10000,
            'address' => '123 Main St',
            'city' => 'New York',
            'state' => 'NY',
            'zip' => '10001',
            'latitude' => 40.7128,
            'longitude' => -74.0060,
            'features' => json_encode([
                'floors' => 2,
                'year_built' => 2000,
                'parking_spaces' => 10
            ]),
            'images' => json_encode([
                ['url' => 'https://example.com/image1.jpg', 'caption' => 'Front view'],
                ['url' => 'https://example.com/image2.jpg', 'caption' => 'Interior']
            ])
        ]);
        
        // Import the listing as a deal
        $response = $this->actingAs($this->admin)
                         ->post('/admin/loopnet/listings/12345/import', [
                             'import_type' => 'deal',
                             'deal_name' => 'Test Deal',
                             'create_asset' => true
                         ]);
        
        // Verify the deal was created
        $response->assertStatus(302); // Redirect
        $this->assertDatabaseHas('deals', [
            'name' => 'Test Deal'
        ]);
        
        // Verify the asset was created
        $this->assertDatabaseHas('assets', [
            'address' => '123 Main St',
            'city' => 'New York',
            'state' => 'NY',
            'zip' => '10001'
        ]);
        
        // Verify the listing was updated with the deal ID
        $this->assertDatabaseHas('ext_loopnet_listings', [
            'loopnet_id' => '12345',
            'deal_id' => Deal::where('name', 'Test Deal')->first()->id
        ]);
    }
}
```

## 10. Code Coverage

### 10.1 Generating Code Coverage Reports

To generate code coverage reports, you need to have Xdebug installed and configured. Then, run the following command:

```bash
phpunit --coverage-html coverage
```

This will generate an HTML coverage report in the `coverage` directory.

### 10.2 Coverage Targets

Aim for the following code coverage targets:

- Core Extension System: 90%+ coverage
- Extension models and services: 80%+ coverage
- Controllers and views: 70%+ coverage

## 11. Continuous Integration

### 11.1 GitHub Actions Workflow

Create a `.github/workflows/tests.yml` file in your extension repository:

```yaml
name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.0'
        extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite
        coverage: xdebug
    
    - name: Install Dependencies
      run: composer install --prefer-dist --no-interaction
    
    - name: Execute tests
      run: vendor/bin/phpunit --coverage-clover=coverage.xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v1
      with:
        file: ./coverage.xml
```

## 12. Best Practices

### 12.1 Test Isolation

- Each test should be independent and not rely on the state from other tests
- Use database transactions to reset the database state between tests
- Mock external services to avoid making real API calls

### 12.2 Test Naming

- Use descriptive test method names that explain what is being tested
- Follow the pattern `testShouldDoSomethingWhenSomethingHappens`

### 12.3 Test Organization

- Group related tests in the same test class
- Use test suites to organize tests by type (unit, integration, feature)
- Keep test files in a directory structure that mirrors the source code

### 12.4 Test Data

- Use factories to create test data
- Avoid hardcoding test data values
- Use the WithFaker trait for generating random test data

## 13. Troubleshooting

### 13.1 Common Issues

- **Database connection errors**: Ensure your test database configuration is correct
- **Missing dependencies**: Make sure all required packages are installed
- **Incomplete test coverage**: Use PHPUnit's coverage reports to identify untested code

### 13.2 Debugging Tests

- Use `$this->dump()` or `dd()` to output debug information
- Enable verbose output with `phpunit -v`
- Check the Laravel log file for errors

## 14. Conclusion

Testing is a critical part of extension development. By following the guidelines in this document, you can ensure that your extensions are reliable, maintainable, and integrate properly with the platform.
