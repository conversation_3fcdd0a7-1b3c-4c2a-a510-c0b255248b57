<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Deal;
use App\Models\KpisCollection;
use App\Models\KpiName;
use App\Models\Kpi;
use App\Models\KpiValue;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;
use Carbon\CarbonPeriod;

class KpiController extends Controller
{
    public function store(Request $request, Deal $deal)
    {
        $validate = Validator::make($request->all(), [
            'kpi_collection_name' => 'required',
            'kpi_collection_type' => 'required',

        ], [
            'kpi_collection_name' => 'This field is required.',
            'kpi_collection_type' => 'This field is required.',
        ]);


        if ($validate->fails()) {
            return response()->json(['errors' => $validate->errors()], 422);
        }
        // dd($request);
        $kpiscollection = KpisCollection::create($request->all());
        // $kpiJson = $kpiscollection->toJsonWithKpiValues();

        return response()->json(['success' => true, 'kpi' => $kpiscollection], 200);
    }

    public function detailPage(Deal $deal, KpisCollection $kpicollection)
    {
        $kpicollection->load('kpis.kpi_values', 'kpi_names', 'deal');

        // Get unique dates from kpi_values' JSON
        $dates = $kpicollection->kpis->flatMap(function ($kpi) {
            $values = json_decode($kpi->kpi_values->first()->values ?? '[]', true);
            return collect($values)->pluck('date');
        })->map(function ($date) {
            return Carbon::parse($date);
        })->unique()->sort();
        // dd($kpicollection->kpis);
        return view('admin.deals.kpi.detail', [
            'kpicollection' => $kpicollection,
            'deal' => $kpicollection->deal,
            'dates' => $dates,
            'prefix' => auth('admin')->user()->hasRole('partner') ? 'partner' : 'admin',
        ]);
    }

    public function storeName(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'kpi_name' => 'required',

        ], [
            'kpi_name' => 'This field is required.',
        ]);

        if ($validate->fails()) {
            return response()->json(['errors' => $validate->errors()], 422);
        }
        // dd($request);
        $kpisname = KpiName::create($request->all());
        // dd($kpisname);
        return response()->json(['success' => true, 'kpi' => $kpisname], 200);
    }

    public function storeKpi(Request $request, KpisCollection $kpicollection)
    {
        $validate = Validator::make($request->all(), [
            'kpi_name' => 'required',
            'kpi_collection_format' => 'required',

        ], [
            'kpi_name' => 'This field is required.',
            'kpi_collection_format' => 'This field is required.',

        ]);


        if ($validate->fails()) {
            return response()->json(['errors' => $validate->errors()], 422);
        }
        // dd($request);
        $kpi = Kpi::create($request->all());

        return response()->json(['success' => true, 'kpi' => $kpi], 200);
    }

    public function deleteKpi(Kpi $kpi)
    {
        $kpi->kpi_values()->delete();
        $kpi->delete();

        return response()->json(['success' => true, 'message' => 'KPI deleted successfully']);
    }
    public function storeKpiValue(Request $request, KpisCollection $kpicollection)
    {
        $request->validate([
            'values' => 'required|array',
            'values.*.kpi_id' => 'required|exists:kpis,id',
            'values.*.date' => 'required|date',
            'values.*.profit' => 'nullable|numeric',
        ]);

        foreach ($request->values as $value) {
            $kpiValue = KpiValue::firstOrCreate(
                ['kpi_id' => $value['kpi_id']],
                ['values' => json_encode([])]
            );

            $values = json_decode($kpiValue->values, true) ?: [];
            // Update or add the date-profit pair
            $existingIndex = collect($values)->search(function ($item) use ($value) {
                return $item['date'] === $value['date'];
            });

            if ($existingIndex !== false) {
                $values[$existingIndex]['profit'] = $value['profit'];
            } else {
                $values[] = ['date' => $value['date'], 'profit' => $value['profit']];
            }

            $kpiValue->values = json_encode($values);
            $kpiValue->save();
        }

        return response()->json(['message' => 'KPI values saved successfully']);
    }

    public function toJsonWithKpiValues()
    {
        $kpis = $this->kpis->map(function ($kpi) {
            $values = json_decode($kpi->kpi_values->first()->values ?? '[]', true);
            return [
                'id' => $kpi->id,
                'kpi_name' => $kpi->kpi_name,
                'kpi_collection_format' => $kpi->kpi_collection_format,
                'values' => $values,
            ];
        });

        return [
            'kpi_collection_id' => $this->id,
            'kpi_collection_name' => $this->kpi_collection_name,
            'kpi_collection_type' => $this->kpi_collection_type,
            'deal_id' => $this->deal_id,
            'kpis' => $kpis->toArray(),
        ];
    }

    public function addDate(Request $request, KpisCollection $kpicollection)
    {
        $request->validate([
            'date' => 'required|date',
        ]);

        $date = Carbon::parse($request->date);

        // Validate date based on kpi_collection_type
        $isValid = match ($kpicollection->kpi_collection_type) {
            'yearly' => $date->month === 1 && $date->day === 1,
            'monthly' => $date->day === 1,
            'daily' => true,
            default => false,
        };

        if (!$isValid) {
            return response()->json(['message' => 'Invalid date for collection type'], 422);
        }

        $dateStr = $date->format('Y-m-d');

        // Update each KPI's kpi_values record
        $kpicollection->kpis->each(function ($kpi) use ($dateStr) {
            $kpiValue = KpiValue::firstOrCreate(
                ['kpi_id' => $kpi->id],
                ['values' => json_encode([])] // Initialize empty JSON array
            );

            $values = json_decode($kpiValue->values, true) ?: [];
            // Add new date if not already present
            if (!collect($values)->contains('date', $dateStr)) {
                $values[] = ['date' => $dateStr, 'profit' => null];
                $kpiValue->values = json_encode($values);
                $kpiValue->save();
            }
        });

        return response()->json(['message' => 'Date added successfully']);
    }
}
