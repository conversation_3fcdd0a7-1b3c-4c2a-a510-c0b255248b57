<?php $__env->startSection('main-content'); ?>
    <a class="scroll-top"><i class="fas fa-angle-double-up"></i></a>
    <?php echo $__env->make($activeTemplate . 'partials.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->renderWhen(!request()->routeIs('home'), $activeTemplate . 'partials.breadcrumb', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1])); ?>
    <?php echo $__env->yieldContent('content'); ?>
    <?php echo $__env->make($activeTemplate . 'partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('style-lib'); ?>
    <link href="<?php echo e(asset($activeTemplateTrue . 'css/slick.css')); ?>" rel="stylesheet">
<?php $__env->stopPush(); ?>
<?php $__env->startPush('script-lib'); ?>
    <script src="<?php echo e(asset($activeTemplateTrue . 'js/slick.min.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        (function($) {
            "use strict";
            let elements = document.querySelectorAll('[data-s-break]');
            Array.from(elements).forEach(element => {
                let html = element.innerText;
                if (typeof html != 'string') {
                    return false;
                }
                html = html.split('');
                let lastValue = html.pop();
                let colorText = `<span>${lastValue}</span>`;
                html.push(colorText);
                html = html.join("");
                element.innerHTML = html;
            });
        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\PLATFORM\resources\views/templates/basic/layouts/frontend.blade.php ENDPATH**/ ?>