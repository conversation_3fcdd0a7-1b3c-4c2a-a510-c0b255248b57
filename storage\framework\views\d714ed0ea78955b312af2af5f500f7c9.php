<div x-data="showDeals()" x-init="init()">
    <div class="row g-3" x-show="view === 'grid'">
        <?php $__currentLoopData = $offerings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $offering): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-3">
                <article class="card property--card border-0">
                    <?php
                        $firstAsset = $offering->assets->first();
                        $assetImages =
                            $firstAsset && $firstAsset->assetMedia->isNotEmpty()
                                ? $firstAsset->assetMedia->pluck('media_url')->toArray()
                                : ['assets/images/default.png'];
                    ?>
                    <div class="d-flex justify-content-center align-items-center">
                        <a class="card-img-top" href="<?php echo e(route('offering', $offering->uuid)); ?>">
                            <img id="dealImage_<?php echo e($offering->uuid); ?>" src="<?php echo e(asset($assetImages[0])); ?>"
                                alt="<?php echo e($firstAsset->name ?? 'Deal Image'); ?>" class="img-fluid asset-hover-image"
                                data-images="<?php echo e(json_encode($assetImages)); ?>"
                                style="width: 100%; height: 180px; object-fit: cover; border-top-left-radius: 10px; border-top-right-radius: 10px;">
                        </a>
                    </div>
                    <div class="card-body px-2 py-3 p-md-3 p-xl-4">
                        <div class="card-body-top">
                            <h5 class="card-title mb-2">
                                <a href="<?php echo e(route('offering', $offering->uuid)); ?>"><?php echo e(\Illuminate\Support\Str::title($offering->name)); ?></a>
                            </h5>
                            <ul class="card-meta card-meta--one">
                                <li class="card-meta__item card-meta__item__location">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span
                                        class="text"><?php echo e(implode(
                                            ', ',
                                            array_filter([
                                                @$firstAsset->address,
                                                @$firstAsset->city,
                                                @$firstAsset->state,
                                                @$firstAsset->zip,
                                                @$firstAsset->country,
                                            ]),
                                        )); ?>

                                    </span>
                                </li>
                            </ul>
                        </div>
                        <div class="card-body-middle">
                            <div class="card-progress mb-4">
                                <div class="card-progress__bar">
                                    <div class="card-progress__thumb"
                                        style="width: <?php echo e(@$offering->invest_progress); ?>%;"></div>
                                </div>
                                <span class="card-progress__label fs-12">
                                    <?php echo e(@$offering->invests_count); ?> <?php echo app('translator')->get('Investors'); ?> |
                                    <?php echo e(showAmount(@$offering->invested_amount)); ?>

                                    (<?php echo e(getAmount(@$offering->invest_progress)); ?>%)
                                </span>
                            </div>
                            <ul class="card-meta card-meta--two">
                                <li class="card-meta__item">
                                    <span class="subtext"><?php echo app('translator')->get('Property Type'); ?></span>
                                    <div class="text">
                                        <?php echo e(@$firstAsset->property_type ?? '--'); ?>

                                    </div>
                                </li>
                                <li class="card-meta__item">
                                    <span class="subtext"><?php echo app('translator')->get('Profit Schedule'); ?></span>
                                    <div class="text">
                                        <?php echo e(@$firstAsset->number_of_units ?? '--'); ?>

                                    </div>
                                </li>
                                <li class="card-meta__item">
                                    <span class="subtext"><?php echo app('translator')->get('status'); ?></span>
                                    <div class="text">
                                        <?php echo e(@$offering->status_text ?? '--'); ?>

                                    </div>
                                </li>
                                <li class="card-meta__item">
                                    <span class="subtext"><?php echo app('translator')->get('Preferred Return'); ?></span>
                                    <div class="text"> 
                                        <?php echo e(@$offering->class->preferred_return_type ?? '--'); ?>

                                    </div>
                                </li>
                                <li class="card-meta__item">
                                    <span class="subtext"><?php echo app('translator')->get('Holding Period'); ?></span>
                                    <div class="text">
                                        <?php echo e(@$offering->asset->exit_date ?? '--'); ?>

                                    </div>
                                </li>
                                <li class="card-meta__item">
                                    <span class="subtext"><?php echo app('translator')->get('Annualized Return'); ?></span>
                                    <div class="text">
                                        <?php echo e(@$offering->preferred_return ?? '--'); ?>

                                    </div>
                                </li>
                            </ul>
                        </div>
                        <div class="card-body-bottom mb-4">
                            <a class="btn btn--sm btn--base" href="<?php echo e(route('offering', $offering->uuid)); ?>" role="button"><?php echo app('translator')->get('Details'); ?></a>
                            <span class="card-price"><?php echo e($offering->offering_size); ?></span>
                        </div>
                    </div>
                </article>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    <?php if($offerings->count() == 0): ?>
        <div class="col-12 text-center">
            <p><?php echo app('translator')->get('No Deals found'); ?></p>
        </div>
    <?php endif; ?>
</div>
<?php $__env->startPush('script'); ?>
    <script>
        function showDeals() {
            return {
                init() {
                }
            }
        }

        document.addEventListener("DOMContentLoaded", function() {
            document.querySelectorAll(".asset-hover-image").forEach(img => {
                let images = JSON.parse(img.dataset.images);
                let index = 0;
                let interval;

                img.addEventListener("mouseenter", function() {
                    index = 1;
                    interval = setInterval(() => {
                        if (index >= images.length) index = 0;
                        img.src = images[index];
                        index++;
                    }, 1000);
                });

                img.addEventListener("mouseleave", function() {
                    clearInterval(interval);
                    img.src = images[0];
                });
            });
        });
    </script>
<?php $__env->stopPush(); ?><?php /**PATH C:\laragon\www\PLATFORM\resources\views/templates/basic/partials/explore_all_deals.blade.php ENDPATH**/ ?>