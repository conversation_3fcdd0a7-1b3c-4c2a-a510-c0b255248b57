<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Template;

class EmailController extends Controller
{
    public function index()
    {
        return view('admin.emails.index');
    }

    public function update(Request $request)
    {

        return redirect()->route('admin.email.index')->with('success', 'Email settings updated successfully.');
    }
    public function templates()
    {
        // here you can fetch templates from the database if needed
        $templates = Template::all();    

        return view('admin.emails.templates' , compact('templates'));
    }
    public function addTemplate()
    {
        return view('admin.emails.addTemplate');
    }
}
