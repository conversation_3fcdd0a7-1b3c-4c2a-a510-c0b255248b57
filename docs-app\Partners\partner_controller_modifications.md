# Partner Controller Modifications Guide

## Overview

This document provides detailed instructions for modifying the existing admin controllers to work with the Partner interface. The key principle is to make minimal changes to the controllers while ensuring partners can only access their own deals.

## Implementation Steps

### 1. Modify DealController

Open `app/Http/Controllers/Admin/DealController.php` and modify the following methods:

#### Index Method

```php
/**
 * Display a listing of the deals.
 *
 * @return \Illuminate\Http\Response
 */
public function index(Request $request)
{
    $user = Auth::guard('admin')->user();
    $isPartner = $user->hasRole('Partner');
    
    // For partners, only show their deals
    if ($isPartner) {
        $dealIds = PartnerDeal::where('admin_id', $user->id)->pluck('deal_id');
        $deals = Deal::whereIn('id', $dealIds)->get();
    } else {
        // For admins, show all deals
        $deals = Deal::all();
    }
    
    // Determine which view to use based on the route
    $routeName = $request->route()->getName();
    if (strpos($routeName, 'partner.') === 0) {
        return view('partner.deals.index', compact('deals'));
    }
    
    return view('admin.deals.index', compact('deals'));
}
```

#### Store Method

```php
/**
 * Store a newly created deal in storage.
 *
 * @param  \Illuminate\Http\Request  $request
 * @return \Illuminate\Http\Response
 */
public function store(Request $request)
{
    // Existing validation and deal creation
    $deal = Deal::create($request->validated());
    
    // If user is a partner, associate the deal with them
    $user = Auth::guard('admin')->user();
    if ($user->hasRole('Partner')) {
        PartnerDeal::create([
            'admin_id' => $user->id,
            'deal_id' => $deal->id
        ]);
    }
    
    // Redirect based on route name
    $routeName = $request->route()->getName();
    if (strpos($routeName, 'partner.') === 0) {
        return redirect()->route('partner.deals.index')
            ->with('success', 'Deal created successfully');
    }
    
    return redirect()->route('admin.deals.index')
        ->with('success', 'Deal created successfully');
}
```

#### Show Method

```php
/**
 * Display the specified deal.
 *
 * @param  \App\Models\Deal  $deal
 * @return \Illuminate\Http\Response
 */
public function show(Request $request, Deal $deal)
{
    // Determine which view to use based on the route
    $routeName = $request->route()->getName();
    if (strpos($routeName, 'partner.') === 0) {
        return view('partner.deals.show', compact('deal'));
    }
    
    return view('admin.deals.show', compact('deal'));
}
```

#### Edit Method

```php
/**
 * Show the form for editing the specified deal.
 *
 * @param  \App\Models\Deal  $deal
 * @return \Illuminate\Http\Response
 */
public function edit(Request $request, Deal $deal)
{
    // Determine which view to use based on the route
    $routeName = $request->route()->getName();
    if (strpos($routeName, 'partner.') === 0) {
        return view('partner.deals.edit', compact('deal'));
    }
    
    return view('admin.deals.edit', compact('deal'));
}
```

#### Update Method

```php
/**
 * Update the specified deal in storage.
 *
 * @param  \Illuminate\Http\Request  $request
 * @param  \App\Models\Deal  $deal
 * @return \Illuminate\Http\Response
 */
public function update(Request $request, Deal $deal)
{
    // Existing validation and deal update
    $deal->update($request->validated());
    
    // Redirect based on route name
    $routeName = $request->route()->getName();
    if (strpos($routeName, 'partner.') === 0) {
        return redirect()->route('partner.deals.index')
            ->with('success', 'Deal updated successfully');
    }
    
    return redirect()->route('admin.deals.index')
        ->with('success', 'Deal updated successfully');
}
```

#### Destroy Method

```php
/**
 * Remove the specified deal from storage.
 *
 * @param  \App\Models\Deal  $deal
 * @return \Illuminate\Http\Response
 */
public function destroy(Request $request, Deal $deal)
{
    // Existing deal deletion logic
    $deal->delete();
    
    // Also delete the partner deal association
    PartnerDeal::where('deal_id', $deal->id)->delete();
    
    // Redirect based on route name
    $routeName = $request->route()->getName();
    if (strpos($routeName, 'partner.') === 0) {
        return redirect()->route('partner.deals.index')
            ->with('success', 'Deal deleted successfully');
    }
    
    return redirect()->route('admin.deals.index')
        ->with('success', 'Deal deleted successfully');
}
```

### 2. Add Required Imports

Add these imports at the top of the controller file:

```php
use App\Models\PartnerDeal;
use Illuminate\Support\Facades\Auth;
```

## How the Controller Modifications Work

1. **Route-Based View Selection**: Each method checks the route name to determine which view to render (partner or admin).

2. **Partner-Specific Data Filtering**: The `index` method filters deals to only show those owned by the partner.

3. **Deal Association**: The `store` method automatically associates newly created deals with the partner who created them.

4. **Route-Based Redirects**: After actions like store, update, and destroy, the controller redirects to the appropriate route based on whether it was accessed via the partner or admin interface.

## Troubleshooting

### Common Issues

1. **View Not Found**: Ensure that the partner views exist in the correct location (`resources/views/partner/deals/`).

2. **Route Not Found**: Verify that the partner routes are registered correctly in `routes/partner.php`.

3. **Model Not Found**: Check that the `PartnerDeal` model is created and imported correctly.

### Debugging Tips

Add temporary logging to debug issues:

```php
Log::info('Route name: ' . $request->route()->getName());
Log::info('User role: ' . ($isPartner ? 'Partner' : 'Admin'));
Log::info('Deals count: ' . count($deals));
```

This implementation follows the principle that "roles and permissions are there to interact with existing modules and not the other way around" by making minimal changes to the existing controllers while adapting them to work with the Partner interface.
