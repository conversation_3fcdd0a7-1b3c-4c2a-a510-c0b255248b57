# Extension System for CRE Investment Platform

## 1. Overview

The Extension System is a modular framework designed to extend the functionality of the CRE Investment Platform without modifying core code. Unlike the "Other APIs" module which focuses on external API connections, the Extension System provides a comprehensive plugin architecture that can add new features, interfaces, and integrations to the platform.

## 2. Architecture

### 2.1 Core Components

- **Extension Registry**: Central system for managing installed extensions
- **Extension Loader**: Dynamically loads enabled extensions during application bootstrap
- **Extension Manager**: Admin interface for installing, configuring, and managing extensions
- **Hook System**: Allows extensions to integrate with existing platform functionality

### 2.2 Directory Structure

```
extensions/
  ├── {extension_code}/
  │   ├── Controllers/
  │   ├── Models/
  │   ├── Views/
  │   ├── Routes/
  │   ├── Database/
  │   │   ├── Migrations/
  │   │   └── Seeds/
  │   ├── Config/
  │   ├── Resources/
  │   │   ├── assets/
  │   │   └── lang/
  │   ├── Providers/
  │   │   └── {ExtensionName}ServiceProvider.php
  │   ├── extension.json
  │   └── composer.json
  └── ...
```

### 2.3 Database Schema

```php
Schema::create('extensions', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->string('code')->unique();
    $table->string('version');
    $table->text('description')->nullable();
    $table->string('author')->nullable();
    $table->string('author_url')->nullable();
    $table->text('permissions')->nullable(); // JSON
    $table->text('settings')->nullable(); // JSON
    $table->boolean('is_enabled')->default(false);
    $table->boolean('is_core')->default(false);
    $table->timestamps();
});
```

## 3. Extension Lifecycle

### 3.1 Installation

1. Upload extension package to `extensions/` directory
2. Register extension in database
3. Run extension migrations
4. Publish extension assets
5. Register extension service provider

### 3.2 Activation/Deactivation

- **Activation**: Enable extension in admin panel, load service provider
- **Deactivation**: Disable extension in admin panel, unload service provider

### 3.3 Uninstallation

1. Disable extension
2. Run extension down migrations
3. Remove extension assets
4. Remove extension from database
5. Delete extension files

## 4. Extension Development

### 4.1 Extension Manifest (extension.json)

```json
{
  "name": "Loopnet Integration",
  "code": "loopnet",
  "version": "1.0.0",
  "description": "Import property listings from Loopnet into deals and offerings",
  "author": "CRE Investment Platform",
  "author_url": "https://creinvestmentplatform.com",
  "requires": {
    "platform": ">=1.0.0",
    "php": ">=8.0.0"
  },
  "permissions": [
    "manage_deals",
    "manage_offerings",
    "manage_assets"
  ],
  "settings": {
    "api_key": {
      "type": "password",
      "label": "Loopnet API Key",
      "required": true
    },
    "api_secret": {
      "type": "password",
      "label": "Loopnet API Secret",
      "required": true
    },
    "search_limit": {
      "type": "number",
      "label": "Search Results Limit",
      "default": 50
    }
  },
  "menu": [
    {
      "name": "Loopnet",
      "icon": "fa-building",
      "route": "admin.extensions.loopnet.index",
      "permission": "manage_deals",
      "children": [
        {
          "name": "Search Listings",
          "route": "admin.extensions.loopnet.search"
        },
        {
          "name": "Imported Listings",
          "route": "admin.extensions.loopnet.listings"
        },
        {
          "name": "Settings",
          "route": "admin.extensions.loopnet.settings"
        }
      ]
    }
  ],
  "providers": [
    "Extensions\\Loopnet\\Providers\\LoopnetServiceProvider"
  ],
  "aliases": {
    "Loopnet": "Extensions\\Loopnet\\Facades\\Loopnet"
  }
}
```

### 4.2 Service Provider

```php
namespace Extensions\Loopnet\Providers;

use Illuminate\Support\ServiceProvider;

class LoopnetServiceProvider extends ServiceProvider
{
    public function register()
    {
        // Register extension services
        $this->app->singleton('loopnet', function ($app) {
            return new \Extensions\Loopnet\Services\LoopnetService();
        });
        
        // Merge extension config
        $this->mergeConfigFrom(
            __DIR__.'/../Config/loopnet.php', 'extensions.loopnet'
        );
    }
    
    public function boot()
    {
        // Load extension routes
        $this->loadRoutesFrom(__DIR__.'/../Routes/web.php');
        $this->loadRoutesFrom(__DIR__.'/../Routes/api.php');
        
        // Load extension views
        $this->loadViewsFrom(__DIR__.'/../Views', 'loopnet');
        
        // Load extension migrations
        $this->loadMigrationsFrom(__DIR__.'/../Database/Migrations');
        
        // Publish extension assets
        $this->publishes([
            __DIR__.'/../Resources/assets' => public_path('extensions/loopnet'),
        ], 'loopnet-assets');
        
        // Register extension hooks
        $this->registerHooks();
    }
    
    protected function registerHooks()
    {
        // Register hooks for extension integration points
        app('extensions.hooks')->register('deal.create.after', function ($deal) {
            // Extension logic here
        });
    }
}
```

## 5. Hook System

The Hook System allows extensions to integrate with the platform at specific points without modifying core code.

### 5.1 Available Hooks

- **deal.create.before**: Before a deal is created
- **deal.create.after**: After a deal is created
- **deal.update.before**: Before a deal is updated
- **deal.update.after**: After a deal is updated
- **offering.create.after**: After an offering is created
- **asset.create.after**: After an asset is created
- **admin.sidebar**: When rendering the admin sidebar
- **admin.dashboard**: When rendering the admin dashboard

### 5.2 Using Hooks

```php
// Register a hook handler
app('extensions.hooks')->register('deal.create.after', function ($deal) {
    // Hook handler logic
});

// Trigger a hook
app('extensions.hooks')->trigger('deal.create.after', $deal);
```

## 6. Loopnet Extension Example

### 6.1 Purpose

The Loopnet extension allows administrators to:

1. Search for commercial real estate listings on Loopnet
2. View detailed property information
3. Import selected listings as deals and offerings
4. Automatically create assets with property details
5. Attach property images to assets

### 6.2 Models

- **LoopnetListing**: Stores imported Loopnet listings
- **LoopnetSearch**: Stores saved search parameters

### 6.3 Controllers

- **LoopnetController**: Main controller for the extension
- **SearchController**: Handles Loopnet API search requests
- **ImportController**: Handles importing listings to deals/offerings

### 6.4 Views

- **index.blade.php**: Main extension dashboard
- **search.blade.php**: Search interface for Loopnet listings
- **listings.blade.php**: List of imported listings
- **view.blade.php**: Detailed view of a listing
- **import.blade.php**: Import wizard for creating deals/offerings

### 6.5 Routes

```php
Route::group([
    'prefix' => 'admin/extensions/loopnet',
    'as' => 'admin.extensions.loopnet.',
    'middleware' => ['admin', 'auth'],
], function () {
    Route::get('/', 'LoopnetController@index')->name('index');
    Route::get('/search', 'SearchController@index')->name('search');
    Route::post('/search', 'SearchController@search')->name('search.post');
    Route::get('/listings', 'LoopnetController@listings')->name('listings');
    Route::get('/listings/{id}', 'LoopnetController@view')->name('listings.view');
    Route::get('/listings/{id}/import', 'ImportController@wizard')->name('listings.import');
    Route::post('/listings/{id}/import', 'ImportController@process')->name('listings.import.process');
    Route::get('/settings', 'LoopnetController@settings')->name('settings');
    Route::post('/settings', 'LoopnetController@saveSettings')->name('settings.save');
});
```

## 7. Extension Manager Interface

### 7.1 Extension List

The Extension Manager provides an interface to view all available extensions, with options to:

- Install new extensions
- Enable/disable installed extensions
- Configure extension settings
- Update extensions
- Uninstall extensions

### 7.2 Extension Details

Each extension has a detail view showing:

- Extension name, version, and author
- Description and purpose
- Required permissions
- Configuration options
- Changelog and update history

### 7.3 Extension Settings

Extensions can define their own settings schema in the extension.json file, which the Extension Manager will render as a form interface.

## 8. Security Considerations

### 8.1 Permission System

Extensions declare required permissions in their manifest file, and the Extension Manager enforces these permissions.

### 8.2 Code Validation

All extension code should be validated before installation to prevent security vulnerabilities.

### 8.3 Data Isolation

Extensions should use their own database tables with proper prefixes to avoid conflicts with core tables.

### 8.4 API Security

Extensions that connect to external APIs should securely store credentials and use proper authentication methods.

## 9. Implementation Roadmap

### 9.1 Phase 1: Foundation

1. Create extension system database schema
2. Implement Extension Registry and Loader
3. Develop Hook System
4. Create basic Extension Manager interface

### 9.2 Phase 2: Developer Tools

1. Create extension scaffolding command
2. Implement extension packaging and distribution
3. Develop extension testing framework
4. Create extension documentation generator

### 9.3 Phase 3: Loopnet Extension

1. Implement Loopnet API client
2. Create search interface
3. Develop listing import wizard
4. Build mapping between Loopnet data and platform models

## 10. Conclusion

The Extension System provides a flexible, maintainable way to add new functionality to the CRE Investment Platform while keeping the core system clean and focused. The Loopnet integration demonstrates how external data sources can be seamlessly integrated into the platform using the extension framework.
