<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\StripeACHService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Distribution;
use App\Models\Waterfall;
use App\Models\WaterfallHurdle;
use App\Models\Deal;
use App\Models\Investment;
use App\Models\Investor;
use App\Models\DealClass;
use App\Models\Document;
use App\Notifications\DistributionNotification;
use App\Models\Gateway;
use App\Services\DistributionService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Schema;

class DistributionsController extends Controller
{
    protected $distributionService;
    protected $stripeACHService;

    public function __construct(DistributionService $distributionService, StripeACHService $stripeACHService)
    {
        $this->distributionService = $distributionService;
        $this->stripeACHService = $stripeACHService;
    }

    public function store(Request $request)
    {
        \Log::info('DistributionsController@store: Start', ['request' => $request->all()]);

        // Map frontend calculation methods to backend
        $methodMap = [
            'water_fall' => 'Waterfall',
            'simple_pro_rata' => 'SimpleProRata',
            'preferred_return' => 'PreferredReturn',
            'invested_payment' => 'Interest',
            'invested_amount' => 'ReturnInvested',
            'Custom' => 'Custom'
        ];

        // Update calculation_method in request
        $requestData = $request->all();
        $requestData['calculation_method'] = $methodMap[$requestData['calculation_method']] ?? $requestData['calculation_method'];

        $request->replace($requestData);

        \Log::info('DistributionsController@store: Normalized request', ['requestData' => $requestData]);

        // Validation rules
        $validate = Validator::make(
            $requestData,
            [
                'deal_id' => 'required|exists:deals,id',
                'source' => 'required|string',
                'count_toward' => 'required|string',
                'included_classes' => 'required|array|min:1',
                'included_classes.*' => 'exists:deal_classes,id',
                'start_date' => 'required|date',
                'end_date' => 'required|date|after_or_equal:start_date',
                'distribution_date' => 'required|date',
                'amount' => 'required_if:calculation_method,SimpleProRata,Custom,Waterfall|min:0',
                'is_visible' => 'required|boolean',
                'memo' => 'nullable|string',
                'day_count' => 'nullable',
                'custom_amounts' => 'required_if:calculation_method,Custom|array',
                'custom_amounts.*' => 'numeric|min:0',
            ],
            [
                'deal_id.required' => 'The deal ID is required.',
                'deal_id.exists' => 'The specified deal does not exist.',
                'source.required' => 'The source field is required.',
                'count_toward.required' => 'The deducts from field is required.',
                'included_classes.required' => 'At least one class must be selected.',
                'included_classes.*.exists' => 'One or more selected classes are invalid.',
                'start_date.required' => 'The period start date is required.',
                'end_date.required' => 'The period end date is required.',
                'end_date.after_or_equal' => 'The end date must be on or after the start date.',
                'distribution_date.required' => 'The distribution date is required.',
                'amount.required_if' => 'The amount is required for the selected calculation method.',
                'amount.min' => 'The amount cannot be negative.',
                'is_visible.required' => 'The visibility setting is required.',
                'custom_amounts.required_if' => 'Custom amounts are required for Custom calculations.',
                'custom_amounts.*.numeric' => 'Each custom amount must be a number.',
                'custom_amounts.*.min' => 'Custom amounts cannot be negative.',
            ]
        );

        if ($validate->fails()) {
            \Log::warning('DistributionsController@store: Validation failed', ['errors' => $validate->errors()]);
            return response()->json(['errors' => $validate->errors()], 422);
        }

        try {
            // Create the distribution record
            $distribution = Distribution::create([
                'deal_id' => $request->deal_id,
                'source' => $request->source,
                'distribution_type' => $request->distribution_type ?? 'default',
                'count_toward' => $request->count_toward,
                'amount' => $request->amount ? (float) str_replace([',', '$'], '', $request->amount) : null,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'distribution_date' => $request->distribution_date,
                'calculation_method' => $request->calculation_method,
                'distribution_waterfall' => $request->distribution_waterfall,
                'is_visible' => $request->is_visible,
                'memo' => $request->memo ?? null,
                'compounding_period' => $request->compounding_period ?? null,
                'day_count' => $request->day_count ?? 'Actual/365',
                'approved' => false,
                'included_classes' => json_encode($request->included_classes),
                'custom_amounts' => $request->calculation_method === 'Custom' ? json_encode($request->custom_amounts) : null,
            ]);

            \Log::info('DistributionsController@store: Created distribution', ['distribution_id' => $distribution->id]);

            $today = Carbon::today()->toDateString();
            if ($distribution->distribution_date <= $today) {
                \Log::info('DistributionsController@store: Processing same-day or past distribution', ['distribution_id' => $distribution->id]);
                $result = $this->processDistribution($distribution);
                if ($result['status'] === 'success') {
                    $distribution->update(['approved' => true]);
                    return response()->json([
                        'message' => $result['message'] ?? 'Distribution created and processed successfully',
                        'distribution_id' => $distribution->id,
                        'investments_to_distribute' => $result['investments_to_distribute'] ?? [],
                        'investor_count' => $result['investor_count'] ?? 0,
                    ], 200);
                } else {
                    $distribution->delete();
                    return response()->json(['error' => $result['message']], 422);
                }
            }

            return response()->json([
                'message' => 'Distribution created successfully',
                'distribution_id' => $distribution->id,
            ], 200);
        } catch (\Exception $e) {
            \Log::error('DistributionsController@store: Exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    private function processHurdle($hurdle, $investments, $applicableClasses, $remainingAmount, $distribution, $offering, $stripeAccSecret, &$investorDistributions, &$investmentIds, &$investorCount)
    {
        // Helper function to get standardized investment amount
        $getInvestmentAmount = function ($investment) {
            $amountValue = $investment->investment_amount;
            if (is_object($amountValue) && method_exists($amountValue, 'getAmount')) {
                return $amountValue->getAmount() / 100; // Convert cents to dollars
            }
            return (float) str_replace(['$', ','], '', $amountValue); // Sanitize string amount
        };

        // Helper function to process ACH transfer
        $processACHTransfer = function ($investment, $allocAmount, $offering, $distribution, $stripeAccSecret) {
            $transactionId = null;
            if ($investment->contribution_method === 'ach_payment') {
                $secret_key = $stripeAccSecret['value'];
                $investor = $investment->investor;
                $stripeTransactionId = $this->stripeACHService->transferToInvestor(
                    $secret_key,
                    $investor,
                    $allocAmount,
                    $offering->id,
                    $distribution->memo
                );
                $transactionId = $stripeTransactionId;
                \Log::info('DistributionsController@processDistribution: Stripe ACH transfer', [
                    'investment_id' => $investment->id,
                    'allocAmount' => $allocAmount,
                    'offering_id' => $offering->id,
                    'stripe_transaction_id' => $stripeTransactionId ?? null,
                ]);
            }
            return $transactionId;
        };

        // Helper function to allocate distribution
        $allocateDistribution = function ($investment, $allocAmount, $type, &$investorDistributions, &$investmentIds) {
            $allocAmount = round($allocAmount, 2); // Ensure two decimal places
            if ($allocAmount > 0) {
                $investorDistributions[$investment->investor_id][] = [
                    'investment_id' => $investment->id,
                    'amount' => $allocAmount,
                    'type' => $type,
                ];
                $investmentIds[] = $investment->id;
            }
            return $allocAmount;
        };

        // Helper function to save distribution details
        $saveDistributionDetail = function ($investment, $allocAmount, $distribution, $offering, $transactionId) {
            $allocAmount = round($allocAmount, 2); // Ensure two decimal places
            if ($allocAmount > 0) {
                \App\Models\DistributionDetail::create([
                    'deal_id' => $distribution->deal_id,
                    'offering_id' => $offering->id,
                    'investment_id' => $investment->id,
                    'distribution_id' => $distribution->id,
                    'amount' => $allocAmount,
                    'is_distributed' => true,
                    'is_transferred' => $transactionId ? 'transferred' : 'pending',
                    'transaction_id' => $transactionId,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        };

        // Prioritize return_of_capital
        if ($hurdle->hurdle_type === 'return_of_capital') {
            foreach ($investments as $investment) {
                if (!in_array($investment->deal_class_id, $applicableClasses)) {
                    continue;
                }

                $investorCount[$investment->investor_id] = true; // Track unique investor
                $investmentAmount = $getInvestmentAmount($investment);

                $paidCapitalQuery = Distribution::where('deal_id', $distribution->deal_id)
                    ->where('distribution_type', 'return_of_capital')
                    ->whereJsonContains('included_classes', $investment->deal_class_id);

                if (Schema::hasColumn('distributions', 'deal_class_id')) {
                    $paidCapitalQuery->where('deal_class_id', $investment->deal_class_id);
                }

                $paidCapital = $paidCapitalQuery->sum('amount') / 100;
                $remainingCapital = max(0, $investmentAmount - $paidCapital);

                $allocAmount = min($remainingCapital, $remainingAmount);
                $allocAmount = round($allocAmount, 2); // Ensure two decimal places
                $remainingAmount -= $allocateDistribution($investment, $allocAmount, 'return_of_capital', $investorDistributions, $investmentIds);

                $transactionId = $processACHTransfer($investment, $allocAmount, $offering, $distribution, $stripeAccSecret);
                $saveDistributionDetail($investment, $allocAmount, $distribution, $offering, $transactionId);
            }
            return $remainingAmount;
        }

        // Handle other hurdle types
        switch ($hurdle->hurdle_type) {
            case 'preferred_return':
                foreach ($investments as $investment) {
                    if (!in_array($investment->deal_class_id, $applicableClasses)) {
                        continue;
                    }

                    $investorCount[$investment->investor_id] = true; // Track unique investor
                    $class = $investment->class;
                    $prefReturnRate = $class->preferred_return / 100;
                    $investmentAmount = $getInvestmentAmount($investment);

                    $days = Carbon::parse($distribution->start_date)->diffInDays($distribution->end_date);
                    $dailyRate = $prefReturnRate / ($distribution->day_count === 'Actual/365' ? 365 : 360);
                    $prefReturn = $investmentAmount * $dailyRate * $days;

                    if ($distribution->compounding_period && $distribution->compounding_period !== 'No Compounding') {
                        $n = match ($distribution->compounding_period) {
                            'Monthly' => 12,
                            'Quarterly' => 4,
                            'Biyearly' => 2,
                            'Yearly' => 1,
                            default => 1,
                        };
                        $t = $days / 365;
                        $prefReturn = $investmentAmount * (pow(1 + $prefReturnRate / $n, $n * $t) - 1);
                    }

                    $allocAmount = min($prefReturn, $remainingAmount);
                    $allocAmount = round($allocAmount, 2); // Ensure two decimal places
                    $remainingAmount -= $allocateDistribution($investment, $allocAmount, 'preferred_return', $investorDistributions, $investmentIds);

                    $transactionId = $processACHTransfer($investment, $allocAmount, $offering, $distribution, $stripeAccSecret);
                    $saveDistributionDetail($investment, $allocAmount, $distribution, $offering, $transactionId);
                }
                break;

            case 'split':
                $splits = $hurdle->splits ? (is_string($hurdle->splits) ? json_decode($hurdle->splits, true) : $hurdle->splits) : ['lp' => 100, 'gp' => 0];
                $lpShare = ($splits['lp'] ?? 100) / 100;
                $classInvestments = $investments->whereIn('deal_class_id', $applicableClasses);
                $totalClassInvestment = $classInvestments->sum($getInvestmentAmount);

                if ($totalClassInvestment == 0) {
                    break;
                }

                foreach ($investments as $investment) {
                    if (!in_array($investment->deal_class_id, $applicableClasses)) {
                        continue;
                    }

                    $investorCount[$investment->investor_id] = true; // Track unique investor
                    $investmentAmount = $getInvestmentAmount($investment);
                    $allocAmount = ($investmentAmount / $totalClassInvestment) * $remainingAmount * $lpShare;
                    $allocAmount = round(min($allocAmount, $remainingAmount), 2); // Ensure two decimal places
                    $remainingAmount -= $allocateDistribution($investment, $allocAmount, 'split', $investorDistributions, $investmentIds);

                    $transactionId = $processACHTransfer($investment, $allocAmount, $offering, $distribution, $stripeAccSecret);
                    $saveDistributionDetail($investment, $allocAmount, $distribution, $offering, $transactionId);
                }
                break;

            case 'catch_up':
                $catchUpRate = $hurdle->catch_up_rate ?? 0;
                $targetClasses = $hurdle->target_classes ?? $applicableClasses;

                foreach ($investments as $investment) {
                    if (!in_array($investment->deal_class_id, $targetClasses)) {
                        continue;
                    }

                    $investorCount[$investment->investor_id] = true; // Track unique investor
                    $investmentAmount = $getInvestmentAmount($investment);
                    $totalReturnPaid = Distribution::where('investment_id', $investment->id)
                        ->whereIn('distribution_type', ['preferred_return', 'split'])
                        ->sum('amount') / 100;
                    $targetReturn = $investmentAmount * $catchUpRate;
                    $catchUpAmount = max(0, $targetReturn - $totalReturnPaid);

                    $allocAmount = min($catchUpAmount, $remainingAmount);
                    $allocAmount = round($allocAmount, 2); // Ensure two decimal places
                    $remainingAmount -= $allocateDistribution($investment, $allocAmount, 'catch_up', $investorDistributions, $investmentIds);

                    $transactionId = $processACHTransfer($investment, $allocAmount, $offering, $distribution, $stripeAccSecret);
                    $saveDistributionDetail($investment, $allocAmount, $distribution, $offering, $transactionId);
                }
                break;

            case 'promote':
                $promoteShare = $hurdle->promote_share ?? 20;
                $totalClassInvestment = $investments->whereIn('deal_class_id', $applicableClasses)
                    ->sum($getInvestmentAmount);

                if ($totalClassInvestment == 0) {
                    break;
                }

                foreach ($investments as $investment) {
                    if (!in_array($investment->deal_class_id, $applicableClasses)) {
                        continue;
                    }

                    $investorCount[$investment->investor_id] = true; // Track unique investor
                    $investmentAmount = $getInvestmentAmount($investment);
                    $allocAmount = ($investmentAmount / $totalClassInvestment) * $remainingAmount * ($promoteShare / 100);
                    $allocAmount = round(min($allocAmount, $remainingAmount), 2); // Ensure two decimal places
                    $remainingAmount -= $allocateDistribution($investment, $allocAmount, 'promote', $investorDistributions, $investmentIds);

                    $transactionId = $processACHTransfer($investment, $allocAmount, $offering, $distribution, $stripeAccSecret);
                    $saveDistributionDetail($investment, $allocAmount, $distribution, $offering, $transactionId);
                }
                break;

            case 'fixed_amount':
                $fixedAmount = $hurdle->fixed_amount ?? 0;
                $totalClassInvestment = $investments->whereIn('deal_class_id', $applicableClasses)
                    ->sum($getInvestmentAmount);

                if ($totalClassInvestment == 0) {
                    break;
                }

                $allocTotal = min($fixedAmount, $remainingAmount);
                $allocTotal = round($allocTotal, 2); // Ensure two decimal places
                $remainingAmount -= $allocTotal;

                foreach ($investments as $investment) {
                    if (!in_array($investment->deal_class_id, $applicableClasses)) {
                        continue;
                    }

                    $investorCount[$investment->investor_id] = true; // Track unique investor
                    $investmentAmount = $getInvestmentAmount($investment);
                    $allocAmount = ($investmentAmount / $totalClassInvestment) * $allocTotal;
                    $allocAmount = round($allocAmount, 2); // Ensure two decimal places
                    $allocateDistribution($investment, $allocAmount, 'fixed_amount', $investorDistributions, $investmentIds);

                    $transactionId = $processACHTransfer($investment, $allocAmount, $offering, $distribution, $stripeAccSecret);
                    $saveDistributionDetail($investment, $allocAmount, $distribution, $offering, $transactionId);
                }
                break;

            case 'cash_on_cash':
                foreach ($investments as $investment) {
                    if (!in_array($investment->deal_class_id, $applicableClasses)) {
                        continue;
                    }

                    $investorCount[$investment->investor_id] = true; // Track unique investor
                    $investmentAmount = $getInvestmentAmount($investment);
                    $cocRate = $hurdle->coc_rate ?? 0.08; // Default 8% cash-on-cash return
                    $allocAmount = $investmentAmount * $cocRate;
                    $allocAmount = round(min($allocAmount, $remainingAmount), 2); // Ensure two decimal places
                    $remainingAmount -= $allocateDistribution($investment, $allocAmount, 'cash_on_cash', $investorDistributions, $investmentIds);

                    $transactionId = $processACHTransfer($investment, $allocAmount, $offering, $distribution, $stripeAccSecret);
                    $saveDistributionDetail($investment, $allocAmount, $distribution, $offering, $transactionId);
                }
                break;

            case 'irr':
                foreach ($investments as $investment) {
                    if (!in_array($investment->deal_class_id, $applicableClasses)) {
                        continue;
                    }

                    $investorCount[$investment->investor_id] = true; // Track unique investor
                    $investmentAmount = $getInvestmentAmount($investment);
                    $irrRate = $hurdle->irr_rate ?? 0.1; // Default 10% IRR
                    $allocAmount = $investmentAmount * $irrRate; // Simplified; real IRR needs iterative calculation
                    $allocAmount = round(min($allocAmount, $remainingAmount), 2); // Ensure two decimal places
                    $remainingAmount -= $allocateDistribution($investment, $allocAmount, 'irr', $investorDistributions, $investmentIds);

                    $transactionId = $processACHTransfer($investment, $allocAmount, $offering, $distribution, $stripeAccSecret);
                    $saveDistributionDetail($investment, $allocAmount, $distribution, $offering, $transactionId);
                }
                break;

            case 'roi':
                foreach ($investments as $investment) {
                    if (!in_array($investment->deal_class_id, $applicableClasses)) {
                        continue;
                    }

                    $investorCount[$investment->investor_id] = true; // Track unique investor
                    $investmentAmount = $getInvestmentAmount($investment);
                    $roiRate = $hurdle->roi_rate ?? 0.15; // Default 15% ROI
                    $allocAmount = $investmentAmount * $roiRate;
                    $allocAmount = round(min($allocAmount, $remainingAmount), 2); // Ensure two decimal places
                    $remainingAmount -= $allocateDistribution($investment, $allocAmount, 'roi', $investorDistributions, $investmentIds);

                    $transactionId = $processACHTransfer($investment, $allocAmount, $offering, $distribution, $stripeAccSecret);
                    $saveDistributionDetail($investment, $allocAmount, $distribution, $offering, $transactionId);
                }
                break;

            case 'management_fee':
                $feeRate = $hurdle->fee_rate ?? 0.02; // Default 2% management fee
                $totalClassInvestment = $investments->whereIn('deal_class_id', $applicableClasses)
                    ->sum($getInvestmentAmount);

                if ($totalClassInvestment == 0) {
                    break;
                }

                foreach ($investments as $investment) {
                    if (!in_array($investment->deal_class_id, $applicableClasses)) {
                        continue;
                    }

                    $investorCount[$investment->investor_id] = true; // Track unique investor
                    $investmentAmount = $getInvestmentAmount($investment);
                    $allocAmount = ($investmentAmount / $totalClassInvestment) * $remainingAmount * $feeRate;
                    $allocAmount = round(min($allocAmount, $remainingAmount), 2); // Ensure two decimal places
                    $remainingAmount -= $allocateDistribution($investment, $allocAmount, 'management_fee', $investorDistributions, $investmentIds);

                    $transactionId = $processACHTransfer($investment, $allocAmount, $offering, $distribution, $stripeAccSecret);
                    $saveDistributionDetail($investment, $allocAmount, $distribution, $offering, $transactionId);
                }
                break;

            case 'cumulative_return':
                foreach ($investments as $investment) {
                    if (!in_array($investment->deal_class_id, $applicableClasses)) {
                        continue;
                    }

                    $investorCount[$investment->investor_id] = true; // Track unique investor
                    $investmentAmount = $getInvestmentAmount($investment);
                    $cumulativeRate = $hurdle->cumulative_rate ?? 0.1; // Default 10% cumulative return
                    $totalReturnPaid = Distribution::where('deal_id', $distribution->deal_id)
                        ->whereJsonContains('included_classes', $investment->deal_class_id)
                        ->whereIn('distribution_type', ['preferred_return', 'split', 'cash_on_cash', 'irr', 'roi'])
                        ->sum('amount') / 100;
                    $targetReturn = $investmentAmount * $cumulativeRate;
                    $allocAmount = max(0, $targetReturn - $totalReturnPaid);
                    $allocAmount = round(min($allocAmount, $remainingAmount), 2); // Ensure two decimal places
                    $remainingAmount -= $allocateDistribution($investment, $allocAmount, 'cumulative_return', $investorDistributions, $investmentIds);

                    $transactionId = $processACHTransfer($investment, $allocAmount, $offering, $distribution, $stripeAccSecret);
                    $saveDistributionDetail($investment, $allocAmount, $distribution, $offering, $transactionId);
                }
                break;

            case 'interest':
                foreach ($investments as $investment) {
                    if (!in_array($investment->deal_class_id, $applicableClasses)) {
                        continue;
                    }

                    $investorCount[$investment->investor_id] = true; // Track unique investor
                    $investmentAmount = $getInvestmentAmount($investment);
                    $interestRate = $hurdle->interest_rate ?? 0.06; // Default 6% interest
                    $days = Carbon::parse($distribution->start_date)->diffInDays($distribution->end_date);
                    $dailyRate = $interestRate / ($distribution->day_count === 'Actual/365' ? 365 : 360);
                    $allocAmount = $investmentAmount * $dailyRate * $days;
                    $allocAmount = round(min($allocAmount, $remainingAmount), 2); // Ensure two decimal places
                    $remainingAmount -= $allocateDistribution($investment, $allocAmount, 'interest', $investorDistributions, $investmentIds);

                    $transactionId = $processACHTransfer($investment, $allocAmount, $offering, $distribution, $stripeAccSecret);
                    $saveDistributionDetail($investment, $allocAmount, $distribution, $offering, $transactionId);
                }
                break;
        }

        return $remainingAmount;
    }

    protected function processDistribution(Distribution $distribution)
    {
        \Log::info('DistributionsController@processDistribution: Start', ['distribution_id' => $distribution->id]);
        $stripeAccSecret = $this->getStripeConfig()['secret_key'] ?? null;
        if (!$stripeAccSecret) {
            throw new \Exception('Stripe ACH gateway secret key not found in configuration.');
        }
        $this->stripeSecret = $stripeAccSecret['value'];

        try {
            $deal = $distribution->deal ?? Deal::find($distribution->deal_id);
            $investments = collect();
            $investorCount = []; // Track unique investors

            if ($deal && $deal->relationLoaded('offerings')) {
                $offerings = $deal->offerings;
            } else {
                $offerings = method_exists($deal, 'offerings') ? $deal->offerings()->get() : [];
            }

            \Log::info('DistributionsController@processDistribution: Offerings fetched', ['offerings_count' => count($offerings)]);

            $included_classes = json_decode($distribution->included_classes, true) ?? [];

            foreach ($offerings as $offering) {
                $offeringInvestments = $offering->investments()
                    ->whereIn('deal_class_id', $included_classes)
                    ->where('investment_status', 'fund_received')
                    ->with(['class', 'investor'])
                    ->get();
                $investments = $investments->merge($offeringInvestments);
            }
            $investments = $investments->unique('id')->values();

            \Log::info('DistributionsController@processDistribution: Investments fetched', ['investments_count' => $investments->count()]);

            if ($investments->isEmpty()) {
                \Log::warning('DistributionsController@processDistribution: No confirmed investments found');
                $distribution->delete();
                return [
                    'status' => 'error',
                    'message' => 'No confirmed investments found for the selected classes',
                ];
            }

            $investmentIds = [];
            $investmentsToDistribute = [];

            // Helper function to get standardized investment amount
            $getInvestmentAmount = function ($investment) {
                $amountValue = $investment->investment_amount;
                if (is_object($amountValue) && method_exists($amountValue, 'getAmount')) {
                    return $amountValue->getAmount() / 100; // Convert cents to dollars
                }
                return (float) str_replace(['$', ','], '', $amountValue); // Sanitize string amount
            };

            // Helper function to save distribution details
            $saveDistributionDetail = function ($investment, $allocAmount, $distribution, $offering, $transactionId) {
                $allocAmount = round($allocAmount, 2); // Ensure two decimal places
                if ($allocAmount > 0) {
                    \App\Models\DistributionDetail::create([
                        'deal_id' => $distribution->deal_id,
                        'offering_id' => $offering->id,
                        'investment_id' => $investment->id,
                        'distribution_id' => $distribution->id,
                        'amount' => $allocAmount,
                        'is_distributed' => true,
                        'is_transferred' => $transactionId ? 'transferred' : 'pending',
                        'transaction_id' => $transactionId,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            };

            if ($distribution->calculation_method === 'Waterfall') {
                \Log::info('DistributionsController@processDistribution: Waterfall calculation started');
                $waterfall = Waterfall::findOrFail($distribution->distribution_waterfall);
                $hurdles = $waterfall->hurdles()->orderBy('sort_order')->get();
                $remainingAmount = (float) str_replace([',', '$'], '', $distribution->amount);
                $investorDistributions = [];

                foreach ($hurdles as $hurdle) {
                    \Log::info('DistributionsController@processDistribution: Processing hurdle', ['hurdle_id' => $hurdle->id, 'type' => $hurdle->hurdle_type]);
                    if ($remainingAmount <= 0) {
                        break;
                    }

                    $applicableClasses = $hurdle->included_classes ?? $included_classes;
                    if (is_string($applicableClasses)) {
                        $applicableClasses = json_decode($applicableClasses, true);
                    }
                    if (!is_array($applicableClasses)) {
                        $applicableClasses = [];
                    }

                    foreach ($offerings as $offering) {
                        $offeringInvestments = $investments->where('offering_id', $offering->id);
                        $remainingAmount = $this->processHurdle(
                            $hurdle,
                            $offeringInvestments,
                            $applicableClasses,
                            $remainingAmount,
                            $distribution,
                            $offering,
                            $stripeAccSecret,
                            $investorDistributions,
                            $investmentIds,
                            $investorCount
                        );
                    }
                }

                foreach ($investorDistributions as $investorId => $allocations) {
                    foreach ($allocations as $alloc) {
                        $investmentsToDistribute[] = [
                            'investor_id' => $investorId,
                            'investment_id' => $alloc['investment_id'],
                            'amount' => $alloc['amount'],
                            'type' => $alloc['type'],
                        ];
                    }
                }

                // Send email notifications
                $this->sendDistributionEmails($distribution, $investmentsToDistribute);

                \Log::info('DistributionsController@processDistribution: Investments to distribute', [
                    'investments' => $investmentsToDistribute
                ]);
                return [
                    'status' => 'success',
                    'investments_to_distribute' => $investmentsToDistribute,
                    'investment_ids' => array_unique($investmentIds),
                    'investor_count' => count($investorCount),
                    'message' => 'Distribution processed successfully for ' . count($investorCount) . ' investors.'
                ];
            } elseif ($distribution->calculation_method === 'SimpleProRata') {
                \Log::info('DistributionsController@processDistribution: SimpleProRata calculation started');
                $totalAmount = (float) str_replace([',', '$'], '', $distribution->amount);
                $classShares = DealClass::whereIn('id', $included_classes)
                    ->pluck('distribution_share', 'id');

                foreach ($included_classes as $classId) {
                    $shareValue = $classShares[$classId];
                    if (is_string($shareValue) && str_contains($shareValue, '%')) {
                        $shareValue = floatval(str_replace('%', '', $shareValue)) / 100;
                    } else {
                        $shareValue = floatval($shareValue);
                        if ($shareValue > 1) {
                            $shareValue = $shareValue / 100;
                        }
                    }
                    $classShare = $shareValue;
                    $classAmount = $totalAmount * $classShare;
                    $classInvestments = $investments->where('deal_class_id', $classId);

                    $totalClassInvestment = $classInvestments->sum($getInvestmentAmount);

                    if ($totalClassInvestment == 0) {
                        continue;
                    }

                    foreach ($offerings as $offering) {
                        $offeringInvestments = $classInvestments->where('offering_id', $offering->id);
                        foreach ($offeringInvestments as $investment) {
                            $investorCount[$investment->investor_id] = true; // Track unique investor
                            $investmentAmount = $getInvestmentAmount($investment);
                            $allocAmount = ($investmentAmount / $totalClassInvestment) * $classAmount;
                            $allocAmount = round($allocAmount, 2); // Ensure two decimal places

                            if ($allocAmount > 0) {
                                $investmentsToDistribute[] = [
                                    'investor_id' => $investment->investor_id,
                                    'investment_id' => $investment->id,
                                    'amount' => $allocAmount,
                                    'type' => 'pro_rata',
                                ];
                                $investmentIds[] = $investment->id;

                                $transactionId = null;
                                if ($investment->contribution_method === 'ach_payment') {
                                    $secret_key = $stripeAccSecret['value'];
                                    $investor = $investment->investor;
                                    $transactionId = $this->stripeACHService->transferToInvestor(
                                        $secret_key,
                                        $investor,
                                        $allocAmount,
                                        $offering->id,
                                        $distribution->memo
                                    );
                                    \Log::info('DistributionsController@processDistribution: Stripe ACH transfer', [
                                        'investment_id' => $investment->id,
                                        'allocAmount' => $allocAmount,
                                        'offering_id' => $offering->id,
                                        'stripe_transaction_id' => $transactionId ?? null,
                                    ]);
                                }
                                $saveDistributionDetail($investment, $allocAmount, $distribution, $offering, $transactionId);
                            }
                        }
                    }
                }

                // Send email notifications
                $this->sendDistributionEmails($distribution, $investmentsToDistribute);

                \Log::info('DistributionsController@processDistribution: Investments to distribute', [
                    'investments' => $investmentsToDistribute
                ]);
                return [
                    'status' => 'success',
                    'investments_to_distribute' => $investmentsToDistribute,
                    'investment_ids' => array_unique($investmentIds),
                    'investor_count' => count($investorCount),
                    'message' => 'Distribution processed successfully for ' . count($investorCount) . ' investors.'
                ];
            } elseif ($distribution->calculation_method === 'PreferredReturn') {
                \Log::info('DistributionsController@processDistribution: PreferredReturn calculation started');
                foreach ($offerings as $offering) {
                    $offeringInvestments = $investments->where('offering_id', $offering->id);
                    foreach ($offeringInvestments as $investment) {
                        \Log::info('Processing investment', [
                            'investment_id' => $investment->id,
                            'deal_class_id' => $investment->deal_class_id,
                            'investor_id' => $investment->investor_id
                        ]);

                        $investorCount[$investment->investor_id] = true; // Track unique investor
                        $class = $investment->class;
                        $prefReturnRate = $class->preferred_return ? (float) $class->preferred_return / 100 : 0;
                        \Log::info('Preferred return rate', ['rate' => $prefReturnRate, 'class_id' => $class->id]);

                        if ($prefReturnRate <= 0) {
                            \Log::warning('Invalid or zero preferred return rate for investment', [
                                'investment_id' => $investment->id,
                                'class_id' => $class->id
                            ]);
                            continue;
                        }

                        $investmentAmount = $getInvestmentAmount($investment);

                        if ($investmentAmount <= 0) {
                            \Log::warning('Invalid or zero investment amount', [
                                'investment_id' => $investment->id
                            ]);
                            continue;
                        }

                        $days = Carbon::parse($distribution->start_date)->diffInDays($distribution->end_date);
                        \Log::info('Period days', ['days' => $days]);

                        $dayCountValue = 365;
                        $dayCountMethod = strtolower(trim($distribution->day_count));

                        if ($dayCountMethod === '30/360') {
                            $dayCountValue = 360;
                        } elseif ($dayCountMethod === '30/365') {
                            $dayCountValue = 365;
                        } elseif ($dayCountMethod === 'actual/360') {
                            $dayCountValue = 360;
                        } elseif ($dayCountMethod === 'actual/365' || $dayCountMethod === 'actual/365 (most common)') {
                            $dayCountValue = 365;
                        } elseif ($dayCountMethod === 'actual/actual') {
                            $year = Carbon::parse($distribution->start_date)->year;
                            $dayCountValue = Carbon::parse("$year-12-31")->dayOfYear;
                        } elseif (is_numeric($distribution->day_count)) {
                            $dayCountValue = (float) $distribution->day_count;
                        } elseif (preg_match('/(\d+)\s*\/\s*(\d+)/', $distribution->day_count, $matches)) {
                            $dayCountValue = (float) $matches[2] ?: 365;
                        }

                        \Log::info('Day count value', ['day_count' => $dayCountValue]);

                        $dailyRate = $prefReturnRate / $dayCountValue;
                        $allocAmount = $investmentAmount * $dailyRate * $days;
                        \Log::info('Simple interest calculation', [
                            'investment_id' => $investment->id,
                            'allocAmount' => $allocAmount
                        ]);

                        if ($distribution->compounding_period && $distribution->compounding_period !== 'No Compounding') {
                            $n = match ($distribution->compounding_period) {
                                'Monthly' => 12,
                                'Quarterly' => 4,
                                'Biyearly' => 2,
                                'Yearly' => 1,
                                default => 1,
                            };
                            $t = $days / 365;
                            $allocAmount = $investmentAmount * (pow(1 + $prefReturnRate / $n, $n * $t) - 1);
                            \Log::info('Compounded interest calculation', [
                                'investment_id' => $investment->id,
                                'compounding_period' => $distribution->compounding_period,
                                'n' => $n,
                                't' => $t,
                                'allocAmount' => $allocAmount
                            ]);
                        }

                        $allocAmount = round($allocAmount, 2); // Ensure two decimal places

                        if ($allocAmount > 0) {
                            $investmentsToDistribute[] = [
                                'investor_id' => $investment->investor_id,
                                'investment_id' => $investment->id,
                                'amount' => $allocAmount,
                                'type' => 'preferred_return',
                            ];
                            $investmentIds[] = $investment->id;

                            $transactionId = null;
                            if ($investment->contribution_method === 'ach_payment') {
                                $secret_key = $stripeAccSecret['value'];
                                $investor = $investment->investor;
                                $transactionId = $this->stripeACHService->transferToInvestor(
                                    $secret_key,
                                    $investor,
                                    $allocAmount,
                                    $offering->id,
                                    $distribution->memo
                                );
                                \Log::info('DistributionsController@processDistribution: Stripe ACH transfer', [
                                    'investment_id' => $investment->id,
                                    'allocAmount' => $allocAmount,
                                    'offering_id' => $offering->id,
                                    'stripe_transaction_id' => $transactionId ?? null,
                                ]);
                            }
                            $saveDistributionDetail($investment, $allocAmount, $distribution, $offering, $transactionId);
                        }
                    }
                }

                // Send email notifications
                $this->sendDistributionEmails($distribution, $investmentsToDistribute);

                \Log::info('DistributionsController@processDistribution: Investments to distribute', [
                    'investments' => $investmentsToDistribute
                ]);
                return [
                    'status' => 'success',
                    'investments_to_distribute' => $investmentsToDistribute,
                    'investment_ids' => array_unique($investmentIds),
                    'investor_count' => count($investorCount),
                    'message' => 'Distribution processed successfully for ' . count($investorCount) . ' investors.'
                ];
            } elseif ($distribution->calculation_method === 'Interest') {
                \Log::info('DistributionsController@processDistribution: Interest calculation started');
                $investorsToNotify = [];
                foreach ($offerings as $offering) {
                    $offeringInvestments = $investments->where('offering_id', $offering->id);
                    foreach ($offeringInvestments as $investment) {
                        $investorCount[$investment->investor_id] = true; // Track unique investor
                        $class = $investment->class;
                        $interestRate = $class->interest_rate / 100;
                        $investmentAmount = $getInvestmentAmount($investment);

                        $days = Carbon::parse($distribution->start_date)->diffInDays($distribution->end_date);
                        $dailyRate = $interestRate / ($distribution->day_count === 'Actual/365' ? 365 : 360);
                        $allocAmount = $investmentAmount * $dailyRate * $days;

                        if ($distribution->compounding_period && $distribution->compounding_period !== 'No Compounding') {
                            $n = match ($distribution->compounding_period) {
                                'Monthly' => 12,
                                'Quarterly' => 4,
                                'Biyearly' => 2,
                                'Yearly' => 1,
                                default => 1,
                            };
                            $t = $days / 365;
                            $allocAmount = $investmentAmount * (pow(1 + $interestRate / $n, $n * $t) - 1);
                        }

                        $allocAmount = round($allocAmount, 2); // Ensure two decimal places

                        if ($allocAmount > 0) {
                            $investmentIds[] = $investment->id;
                            Distribution::create([
                                'deal_id' => $distribution->deal_id,
                                'investor_id' => $investment->investor_id,
                                'investment_id' => $investment->id,
                                'amount' => $allocAmount * 100,
                                'distribution_date' => $distribution->distribution_date,
                                'distribution_type' => 'interest',
                                'is_visible' => $distribution->is_visible,
                                'parent_distribution_id' => $distribution->id,
                                'source' => $distribution->source,
                                'count_toward' => $distribution->count_toward,
                                'calculation_method' => $distribution->calculation_method,
                            ]);
                            $investorsToNotify[] = [
                                'investor_id' => $investment->investor_id,
                                'investment_id' => $investment->id,
                                'amount' => $allocAmount,
                                'type' => 'interest',
                            ];

                            $transactionId = null;
                            if ($investment->contribution_method === 'ach_payment') {
                                $secret_key = $stripeAccSecret['value'];
                                $investor = $investment->investor;
                                $transactionId = $this->stripeACHService->transferToInvestor(
                                    $secret_key,
                                    $investor,
                                    $allocAmount,
                                    $offering->id,
                                    $distribution->memo
                                );
                                \Log::info('DistributionsController@processDistribution: Stripe ACH transfer', [
                                    'investment_id' => $investment->id,
                                    'allocAmount' => $allocAmount,
                                    'offering_id' => $offering->id,
                                    'stripe_transaction_id' => $transactionId ?? null,
                                ]);
                            }
                            $saveDistributionDetail($investment, $allocAmount, $distribution, $offering, $transactionId);
                        }
                    }
                }

                // Send email notifications
                $this->sendDistributionEmails($distribution, $investorsToNotify);

                \Log::info('DistributionsController@processDistribution: Interest calculation completed');
                return [
                    'status' => 'success',
                    'investment_ids' => array_unique($investmentIds),
                    'investor_count' => count($investorCount),
                    'message' => 'Distribution processed successfully for ' . count($investorCount) . ' investors.'
                ];
            } elseif ($distribution->calculation_method === 'ReturnInvested') {
                \Log::info('DistributionsController@processDistribution: ReturnInvested calculation started');
                $investorsToNotify = [];
                foreach ($offerings as $offering) {
                    $offeringInvestments = $investments->where('offering_id', $offering->id);
                    foreach ($offeringInvestments as $investment) {
                        $investorCount[$investment->investor_id] = true; // Track unique investor
                        $investmentAmount = $getInvestmentAmount($investment);

                        $paidCapital = Distribution::where('deal_id', $distribution->deal_id)
                            ->where('distribution_type', 'return_of_capital')
                            ->whereJsonContains('included_classes', $investment->deal_class_id)
                            ->sum('amount') / 100;
                        $allocAmount = max(0, $investmentAmount - $paidCapital);
                        $allocAmount = round($allocAmount, 2); // Ensure two decimal places

                        if ($allocAmount > 0) {
                            $investmentIds[] = $investment->id;
                            Distribution::create([
                                'deal_id' => $distribution->deal_id,
                                'investor_id' => $investment->investor_id,
                                'investment_id' => $investment->id,
                                'amount' => $allocAmount * 100,
                                'distribution_date' => $distribution->distribution_date,
                                'distribution_type' => 'return_of_capital',
                                'is_visible' => $distribution->is_visible,
                                'parent_distribution_id' => $distribution->id,
                                'source' => $distribution->source,
                                'count_toward' => $distribution->count_toward,
                                'calculation_method' => $distribution->calculation_method,
                            ]);
                            $investorsToNotify[] = [
                                'investor_id' => $investment->investor_id,
                                'investment_id' => $investment->id,
                                'amount' => $allocAmount,
                                'type' => 'return_of_capital',
                            ];

                            $transactionId = null;
                            if ($investment->contribution_method === 'ach_payment') {
                                $secret_key = $stripeAccSecret['value'];
                                $investor = $investment->investor;
                                $transactionId = $this->stripeACHService->transferToInvestor(
                                    $secret_key,
                                    $investor,
                                    $allocAmount,
                                    $offering->id,
                                    $distribution->memo
                                );
                                \Log::info('DistributionsController@processDistribution: Stripe ACH transfer', [
                                    'investment_id' => $investment->id,
                                    'allocAmount' => $allocAmount,
                                    'offering_id' => $offering->id,
                                    'stripe_transaction_id' => $transactionId ?? null,
                                ]);
                            }
                            $saveDistributionDetail($investment, $allocAmount, $distribution, $offering, $transactionId);
                        }
                    }
                }

                // Send email notifications
                $this->sendDistributionEmails($distribution, $investorsToNotify);

                \Log::info('DistributionsController@processDistribution: ReturnInvested calculation completed');
                return [
                    'status' => 'success',
                    'investment_ids' => array_unique($investmentIds),
                    'investor_count' => count($investorCount),
                    'message' => 'Distribution processed successfully for ' . count($investorCount) . ' investors.'
                ];
            } elseif ($distribution->calculation_method === 'Custom') {
                \Log::info('DistributionsController@processDistribution: Custom calculation started');
                $investorsToNotify = [];
                $customAmounts = json_decode($distribution->custom_amounts, true) ?? [];
                foreach ($offerings as $offering) {
                    $offeringInvestments = $investments->where('offering_id', $offering->id);
                    foreach ($offeringInvestments as $investment) {
                        $allocAmount = isset($customAmounts[$investment->id]) ? (float) $customAmounts[$investment->id] : 0;
                        $allocAmount = round($allocAmount, 2); // Ensure two decimal places

                        if ($allocAmount > 0) {
                            $investorCount[$investment->investor_id] = true; // Track unique investor
                            $investmentIds[] = $investment->id;
                            Distribution::create([
                                'deal_id' => $distribution->deal_id,
                                'investor_id' => $investment->investor_id,
                                'investment_id' => $investment->id,
                                'amount' => $allocAmount * 100,
                                'distribution_date' => $distribution->distribution_date,
                                'distribution_type' => $distribution->distribution_type ?? 'custom',
                                'is_visible' => $distribution->is_visible,
                                'parent_distribution_id' => $distribution->id,
                                'source' => $distribution->source,
                                'count_toward' => $distribution->count_toward,
                                'calculation_method' => $distribution->calculation_method,
                            ]);
                            $investorsToNotify[] = [
                                'investor_id' => $investment->investor_id,
                                'investment_id' => $investment->id,
                                'amount' => $allocAmount,
                                'type' => 'custom',
                            ];

                            $transactionId = null;
                            if ($investment->contribution_method === 'ach_payment') {
                                $secret_key = $stripeAccSecret['value'];
                                $investor = $investment->investor;
                                $transactionId = $this->stripeACHService->transferToInvestor(
                                    $secret_key,
                                    $investor,
                                    $allocAmount,
                                    $offering->id,
                                    $distribution->memo
                                );
                                \Log::info('DistributionsController@processDistribution: Stripe ACH transfer', [
                                    'investment_id' => $investment->id,
                                    'allocAmount' => $allocAmount,
                                    'offering_id' => $offering->id,
                                    'stripe_transaction_id' => $transactionId ?? null,
                                ]);
                            }
                            $saveDistributionDetail($investment, $allocAmount, $distribution, $offering, $transactionId);
                        }
                    }
                }

                // Send email notifications
                $this->sendDistributionEmails($distribution, $investorsToNotify);

                \Log::info('DistributionsController@processDistribution: Custom calculation completed');
                return [
                    'status' => 'success',
                    'investment_ids' => array_unique($investmentIds),
                    'investor_count' => count($investorCount),
                    'message' => 'Distribution processed successfully for ' . count($investorCount) . ' investors.'
                ];
            }

            \Log::info('DistributionsController@processDistribution: Completed successfully', [
                'distribution_id' => $distribution->id,
                'investment_ids' => $investmentIds,
            ]);
            return [
                'status' => 'success',
                'investment_ids' => array_unique($investmentIds),
                'investments_to_distribute' => $investmentsToDistribute,
                'investor_count' => count($investorCount),
                'message' => 'Distribution processed successfully for ' . count($investorCount) . ' investors.'
            ];
        } catch (\Exception $e) {
            \Log::error('DistributionsController@processDistribution: Exception', [
                'distribution_id' => $distribution->id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return [
                'status' => 'error',
                'message' => $e->getMessage(),
            ];
        }
    }

    protected function processAchTransfer(Investment $investment, $amount, Distribution $distribution)
    {
        \Log::info('DistributionsController@processAchTransfer: Start', [
            'investment_id' => $investment->id,
            'investor_id' => $investment->investor_id,
            'amount' => $amount,
            'distribution_id' => $distribution->id
        ]);

        try {
            $investor = $investment->investor;

            // Verify ACH details
            if (!$investor->ach_customer_id || !$investor->ach_payment_method_id) {
                \Log::warning('DistributionsController@processAchTransfer: Missing ACH details', [
                    'investor_id' => $investor->id,
                    'ach_customer_id' => $investor->ach_customer_id,
                    'ach_payment_method_id' => $investor->ach_payment_method_id
                ]);
                return null;
            }

            // Convert amount to cents for Stripe
            $amountInCents = (int) ($amount * 100);

            // Create a transfer to the investor's bank account via ACH
            $transfer = \Stripe\Transfer::create([
                'amount' => $amountInCents,
                'currency' => 'usd',
                'destination' => $investor->ach_customer_id,
                'transfer_group' => "distribution_{$distribution->id}",
                'metadata' => [
                    'investment_id' => $investment->id,
                    'distribution_id' => $distribution->id,
                    'investor_id' => $investor->id,
                ],
            ]);

            \Log::info('DistributionsController@processAchTransfer: Transfer created', [
                'transfer_id' => $transfer->id,
                'amount' => $amount,
                'investor_id' => $investor->id,
                'distribution_id' => $distribution->id
            ]);

            return $transfer->id;
        } catch (\Exception $e) {
            \Log::error('DistributionsController@processAchTransfer: Failed to process ACH transfer', [
                'investment_id' => $investment->id,
                'investor_id' => $investment->investor_id,
                'amount' => $amount,
                'distribution_id' => $distribution->id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return null;
        }
    }

    protected function sendDistributionEmails(Distribution $distribution, array $investorsToNotify)
    {
        \Log::info('DistributionsController@sendDistributionEmails: Start', [
            'distribution_id' => $distribution->id,
            'investors_count' => count($investorsToNotify)
        ]);

        // Group by investor_id to consolidate amounts per investor
        $investorAmounts = [];
        foreach ($investorsToNotify as $item) {
            $investorId = $item['investor_id'];
            $amount = $item['amount'];
            if (!isset($investorAmounts[$investorId])) {
                $investorAmounts[$investorId] = 0;
            }
            $investorAmounts[$investorId] += $amount;
        }

        // Fetch deal details
        $deal = Deal::find($distribution->deal_id);

        foreach ($investorAmounts as $investorId => $totalAmount) {
            try {
                $investor = Investor::find($investorId);
                if (!$investor || !$investor->investor_email) {
                    \Log::warning('DistributionsController@sendDistributionEmails: Invalid or missing investor email', [
                        'investor_id' => $investorId,
                        'distribution_id' => $distribution->id
                    ]);
                    continue;
                }

                // Send email using Laravel Notification
                $investor->notify(new DistributionNotification([
                    'deal_name' => $deal->name ?? 'Unknown Deal',
                    'amount' => number_format($totalAmount, 2),
                    'distribution_date' => $distribution->distribution_date,
                    'memo' => $distribution->memo,
                ]));

                \Log::info('DistributionsController@sendDistributionEmails: Email sent', [
                    'investor_id' => $investorId,
                    'email' => $investor->investor_email,
                    'distribution_id' => $distribution->id,
                    'amount' => $totalAmount
                ]);
            } catch (\Exception $e) {
                \Log::error('DistributionsController@sendDistributionEmails: Failed to send email', [
                    'investor_id' => $investorId,
                    'distribution_id' => $distribution->id,
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }

        \Log::info('DistributionsController@sendDistributionEmails: Completed', [
            'distribution_id' => $distribution->id
        ]);
    }

    public function toggleVisibility(Request $request, $id)
    {
        $distribution = Distribution::findOrFail($id);
        $distribution->is_visible = $request->is_visible;
        $distribution->save();

        return response()->json(['success' => true]);
    }

    public function destroy($id)
    {
        $distribution = Distribution::find($id);
        if ($distribution) {
            $distribution->delete();
            return response()->json(['success' => true]);
        }
        return response()->json(['success' => false, 'message' => 'Distribution not found.']);
    }

    protected function getStripeConfig()
    {
        $stripeConfig = Gateway::where('code', '114')->first();
        if (!$stripeConfig) {
            throw new \Exception('Stripe ACH gateway configuration not found.');
        }
        return json_decode($stripeConfig->gateway_parameters, true);
    }
}