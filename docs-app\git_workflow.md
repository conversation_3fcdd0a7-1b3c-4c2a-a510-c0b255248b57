# Git Workflow Guide for CRE Investment Platform

## Commit and Push Best Practices

### Files to Exclude from Git Tracking

The following files and directories should NOT be tracked in Git:

1. **Maintenance Scripts**
   - All files in `/scripts/**` directory
   - These include permission scripts, backup scripts, and utility scripts

2. **Documentation**
   - All files in `/docs/**` directory
   - README.md should be moved here before committing

3. **Configuration Files**
   - `.windsurfrules` configuration files
   - Standard Laravel files like `.env`, `.env.backup`, etc.

4. **Asset Images**
   - `/public/assets_images/**` - These should be managed separately

5. **Log Files**
   - Any log files generated during development or testing

### Files That Should Be Tracked

The following files should be tracked in Git:

1. **Core Application Files**
   - Controllers, Models, and Views following Laravel MVC structure
   - Middleware and Service Providers
   - Routes and Configurations

2. **Database Files**
   - Migrations
   - Seeders (especially `DatabaseSeeder.php` and `AdminRoleSeeder.php`)

3. **Frontend Components**
   - Vue.js components
   - JavaScript and CSS assets

## Pre-Commit Checklist

Before creating a commit, follow these steps:

1. **Organize Files**
   - Move any documentation to `/docs` directory
   - Move any maintenance scripts to `/scripts` directory with appropriate subdirectories
   - Ensure README.md is in the `/docs` directory if it contains sensitive information

2. **Check .gitignore**
   - Verify that the `.gitignore` file includes all necessary exclusions
   - Current exclusions include:
     ```
     /scripts/**
     /docs/**
     /.windsurfrules
     /public/assets_images/**
     ```

3. **Review Changes**
   - Run `git status` to see what files will be included in the commit
   - Run `git diff` to review the specific changes

4. **Create Meaningful Commit**
   - Use a standardized commit message format: `MM-DD-YY Description of Changes`
   - Example: `03-12-25 Staff Management Enhancement Backup-Restore`

## Pushing to Repository

When pushing to the repository:

1. **Check for Updates**
   - Run `git fetch` to check for updates from the remote repository
   - If there are updates, consider using `git pull` before pushing

2. **Handle Conflicts**
   - If there are conflicts, resolve them carefully, especially in core files like:
     - `app/Http/Controllers/Admin/StaffController.php`
     - `database/seeders/RoleAndPermissionSeeder.php`

3. **Push Changes**
   - Use `git push origin <branch-name>` to push your changes
   - For force pushes (when necessary): `git push -f origin <branch-name>`

## Advanced Git Operations

### Removing Files from Git History

If you need to remove files from Git history without leaving a trace:

```bash
git filter-branch --force --index-filter "git rm --cached --ignore-unmatch <file-path>" --prune-empty --tag-name-filter cat -- --all
git push -f origin <branch-name>
```

This should be used sparingly and with caution as it rewrites Git history.

### Stashing Changes

If you need to temporarily set aside changes:

```bash
git stash save "Description of stashed changes"
# Do other work
git stash apply # To reapply the stashed changes
```

## Alignment with Platform Architecture

This Git workflow aligns with the platform's architectural principles:

1. **Follow Laravel's MVC Implementation**
   - Core application files are tracked and organized according to Laravel conventions

2. **Use Eloquent ORM for Data Access**
   - Database-related files are properly tracked

3. **Maintain Vue.js Component Structure**
   - Frontend components are tracked and organized

4. **Respect Middleware Chain**
   - Middleware files are properly tracked

5. **Roles and Permissions Principle**
   - "Roles and permissions are there to interact with existing modules and not the other way around"
   - File organization respects this principle by keeping the core application clean

By following these guidelines, we maintain a clean core application while effectively managing utility scripts and documentation.
