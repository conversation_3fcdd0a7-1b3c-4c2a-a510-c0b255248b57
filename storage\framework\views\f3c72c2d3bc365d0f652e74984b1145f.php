<?php
    $loginContent = getContent('login.content', true);
    $label = 'form--label';
    $formControl = '';
?>
<?php $__env->startSection('main-content'); ?>
    <section class="account">
        <div class="account-inner py-60 bg-pattern3">
            <div class="container ">
                <div class="row justify-content-center">
                    <div class="col-md-10 col-lg-8 col-xl-6 col-xxl-5">
                        <form method="POST" action="<?php echo e(route('user.login')); ?>" class="account-form verify-gcaptcha">
                            <?php echo csrf_field(); ?>
                            <div class="account-form__header text-center">
                                <a class="mb-5" href="<?php echo e(route('home')); ?>"> <img src="<?php echo e(siteLogo()); ?>"></a>
                                <h5 class="account-form__title mb-3"><?php echo e(__($loginContent->data_values->heading)); ?></h5>

                                <?php echo $__env->make($activeTemplate . 'partials.social_login', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                            </div>
                            <div class="account-form__body">
                                <div class="form-group">
                                    <label for="usernameOrEmail" class="form--label required"><?php echo app('translator')->get('Username or Email'); ?></label>
                                    <input class="form--control" type="text" name="username"
                                        value="<?php echo e(old('username')); ?>" id="usernameOrEmail" required>
                                </div>
                                <div class="form-group">
                                    <label for="your-password" class="form--label required"><?php echo app('translator')->get('Password'); ?></label>
                                    <div class="position-relative">
                                        <input class="form--control" type="password" name="password" id="your-password">
                                    </div>
                                </div>
                                <?php if (isset($component)) { $__componentOriginalff0a9fdc5428085522b49c68070c11d6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalff0a9fdc5428085522b49c68070c11d6 = $attributes; } ?>
<?php $component = App\View\Components\Captcha::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('captcha'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Captcha::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($label),'formControl' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($formControl)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalff0a9fdc5428085522b49c68070c11d6)): ?>
<?php $attributes = $__attributesOriginalff0a9fdc5428085522b49c68070c11d6; ?>
<?php unset($__attributesOriginalff0a9fdc5428085522b49c68070c11d6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalff0a9fdc5428085522b49c68070c11d6)): ?>
<?php $component = $__componentOriginalff0a9fdc5428085522b49c68070c11d6; ?>
<?php unset($__componentOriginalff0a9fdc5428085522b49c68070c11d6); ?>
<?php endif; ?>
                                <div class="flex-between">
                                    <div class="form--check">
                                        <input class="form-check-input" type="checkbox" id="remember" name="remember"
                                            <?php echo e(old('remember') ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="remember"><?php echo app('translator')->get('Remember me'); ?></label>
                                    </div>
                                    <a href="<?php echo e(route('user.password.request')); ?>"
                                        class="account-form__forgot-pass"><?php echo app('translator')->get('Forgot Password'); ?>?</a>
                                </div>
                            </div>
                            <div class="account-form__footer">
                                <button type="submit" id="recaptcha" class="w-100 btn btn--base"><?php echo app('translator')->get('Login'); ?></button>
                                <p class="account-form__subtitle mt-3">
                                    <?php echo app('translator')->get('Don\'t have an account'); ?>?
                                    <a href="<?php echo e(route('user.register')); ?>"><?php echo app('translator')->get('Register'); ?></a>
                                </p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\PLATFORM\resources\views/templates/basic/user/auth/login.blade.php ENDPATH**/ ?>