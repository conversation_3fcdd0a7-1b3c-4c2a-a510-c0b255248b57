# Windsurf Rules Configuration for CRE Investment Platform

# System Architecture
architecture:
  pattern: Laravel MVC
  components:
    models:
      - Deal
      - DealClass
      - Investment
      - WaterFall
      - Asset
      - Distribution
      - Offering
      - OfferingFundingInfo
    controllers:
      - Admin\DealController
      - Admin\InvestmentController
      - Admin\WaterFallController
      - Admin\DistributionsController
      - Admin\PropertyController
    middleware:
      - AuthMiddleware
      - KycMiddleware
      - RegistrationStep
      - CheckStatus

# Frontend Architecture
frontend:
  vue_components:
    - Waterfall.vue
    - HurdleComponent.vue
    - Gp_Provision.vue
    - DocumensoTemplate.vue
    - PercentInput.vue
  templates:
    admin:
      - layouts
      - deals
      - invest
      - property
      - waterfall
    public:
      - offerings
      - templates/basic

# Protected Systems
protected_systems:
  waterfall:
    components:
      - WaterFall Model
      - ClassHurdle Model
      - GPProvision Model
      - Distribution Model
    vue_components:
      - Waterfall.vue
      - HurdleComponent.vue
      - Gp_Provision.vue
    features:
      - Distribution calculations
      - Hurdle management
      - GP provision handling
      - Class-based distribution

  investment_processing:
    components:
      - Investment Model
      - Invest Model
      - Profit Model
      - InvestmentQuestionnaire Model

  asset_management:
    models:
      - Asset
      - AssetMedia
      - Property
      - Location

# Protected Directories
protected_directories:
  core:
    - /app/Http/Controllers/Admin/**
    - /app/Http/Middleware/**
    - /app/Models/**
  frontend:
    - /resources/views/admin/**
    - /resources/js/components/**
  config:
    - /config/**

# Code Patterns
code_patterns:
  models:
    naming: PascalCase
    relationships: snake_case
    examples:
      - funding_info()
      - deal_class()
      - asset_media()
  
  vue_components:
    framework: Vue
    features:
      - Component-based architecture
      - Props for data passing
      - Event emission
      - Computed properties

# Active Integrations
protected_integrations:
  payment_gateways:
    - Authorize
    - Checkout
    - Coinbase
    - Binance
  document_systems:
    - Documenso
  kyc_systems:
    - KYC verification
    - AML compliance

# Development Protocol
development_protocol:
  verification:
    - check_github_development
    - verify_protected_status
    - review_dependencies
    - test_authentication
  implementation:
    - follow_laravel_mvc
    - use_eloquent_orm
    - maintain_vue_components
  testing:
    - feature_tests
    - middleware_tests
    - authentication_flow
    - distribution_calculations

# Error Resolution
error_resolution:
  steps:
    - check_github_development
    - identify_exact_error
    - verify_middleware_chain
    - minimal_required_change
    - test_specific_functionality

#Access Points
Admin Portal: http://localhost:8000/admin/
User Portal: http://localhost:8000/user/login
Live Reference: https://mycompanyllc.cashflowportal.com/app/