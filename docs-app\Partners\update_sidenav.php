<?php

// Read the existing sidenav.json file
$sidenavPath = __DIR__ . '/resources/views/admin/partials/sidenav.json';
$existingSidenav = json_decode(file_get_contents($sidenavPath), true);

// Define our new menu items
$partnerManagement = [
    'title' => 'Partner Management',
    'icon' => 'las la-handshake',
    'menu_active' => 'admin.partner-management*',
    'submenu' => [
        [
            'keyword' => [
                'Partners',
                'Partner List',
                'All Partners'
            ],
            'title' => 'All Partners',
            'route_name' => 'admin.partner-management.index',
            'menu_active' => 'admin.partner-management.index'
        ],
        [
            'keyword' => [
                'Add Partner',
                'Create Partner',
                'New Partner'
            ],
            'title' => 'Add New Partner',
            'route_name' => 'admin.partner-management.create',
            'menu_active' => 'admin.partner-management.create'
        ]
    ]
];

$partnerPortal = [
    'title' => 'Partner Portal',
    'icon' => 'las la-user-tie',
    'menu_active' => 'admin.partner*',
    'submenu' => [
        [
            'keyword' => [
                'Partner Dashboard',
                'Partner Home'
            ],
            'title' => 'Dashboard',
            'route_name' => 'admin.partner.dashboard',
            'menu_active' => 'admin.partner.dashboard'
        ],
        [
            'keyword' => [
                'Partner Profile',
                'My Profile'
            ],
            'title' => 'Profile',
            'route_name' => 'admin.partner.profile',
            'menu_active' => 'admin.partner.profile'
        ],
        [
            'keyword' => [
                'Partner Deals',
                'My Deals'
            ],
            'title' => 'My Deals',
            'route_name' => 'admin.partner.deals.index',
            'menu_active' => 'admin.partner.deals.index'
        ]
    ]
];

// Create a new sidenav array with our new menu items inserted after 'deals'
$newSidenav = [];
$dealsFound = false;

foreach ($existingSidenav as $key => $value) {
    $newSidenav[$key] = $value;
    
    // Insert our new menu items after the 'deals' section
    if ($key === 'deals') {
        $dealsFound = true;
        $newSidenav['partner_management'] = $partnerManagement;
        $newSidenav['partner_portal'] = $partnerPortal;
    }
}

// If 'deals' section wasn't found, add our new menu items at the beginning
if (!$dealsFound) {
    $newSidenav = array_merge(
        ['partner_management' => $partnerManagement, 'partner_portal' => $partnerPortal],
        $newSidenav
    );
}

// Create a backup of the existing sidenav.json file
$backupPath = $sidenavPath . '.bak.' . date('Y-m-d-H-i-s');
file_put_contents($backupPath, file_get_contents($sidenavPath));

// Write the updated sidenav.json file
file_put_contents($sidenavPath, json_encode($newSidenav, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));

echo "Sidenav updated successfully! Backup created at {$backupPath}\n";
