<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('distribution_details', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('deal_id');
            $table->unsignedBigInteger('offering_id');
            $table->unsignedBigInteger('investment_id');
            $table->unsignedBigInteger('distribution_id');
            $table->decimal('amount', 15, 2); // Amount distributed, 2 decimal places
            $table->boolean('is_distributed')->default(true); // True after creation
            $table->enum('is_transferred', ['transferred', 'pending'])->default('pending'); // Transfer status
            $table->string('transaction_id')->nullable(); // Stripe transaction ID
            $table->timestamps();

            // Foreign keys
            $table->foreign('deal_id')->references('id')->on('deals')->onDelete('cascade');
            $table->foreign('offering_id')->references('id')->on('offerings')->onDelete('cascade');
            $table->foreign('investment_id')->references('id')->on('investments')->onDelete('cascade');
            $table->foreign('distribution_id')->references('id')->on('distributions')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('distribution_details');
    }
};
