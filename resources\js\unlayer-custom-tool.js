debugger;
function registerCustomTools(unlayer) {
    unlayer.registerTool({
        name: 'custom_button',
        label: 'Custom Button',
        icon: 'fa-star', 
        supportedDisplayModes: ['email'],
        options: {
            default: {
                title: 'Button Settings',
                options: {
                    buttonText: {
                        label: 'Button Text',
                        defaultValue: 'Click Me',
                        widget: 'text',
                    },
                    buttonUrl: {
                        label: 'Button URL',
                        defaultValue: 'https://example.com',
                        widget: 'text',
                    },
                    buttonColor: {
                        label: 'Button Color',
                        defaultValue: '#007bff',
                        widget: 'color_picker',
                    },
                },
            },
        },
        values: {},
        renderer: {
            Viewer: function (toolData) {
                const { values } = toolData;
                return `
                    <a href="${values.buttonUrl}" style="display: inline-block; padding: 10px 20px; background-color: ${values.buttonColor}; color: white; text-decoration: none; border-radius: 5px;">
                        ${values.buttonText}
                    </a>
                `;
            },
            exporter: {
                html: function (toolData) {
                    const { values } = toolData;
                    return `
                        <a href="${values.buttonUrl}" style="display: inline-block; padding: 10px 20px; background-color: ${values.buttonColor}; color: white; text-decoration: none; border-radius: 5px;">
                            ${values.buttonText}
                        </a>
                    `;
                },
            },
        },
    });
}