# Offering Questionnaire Implementation

## Overview

The Offering Questionnaire module was implemented to provide a flexible system for creating and managing questionnaires with multiple tabs (Personal, Entity, Entity IRA, Relationships, Accreditation, IRA, and Entity Type). Each tab contains fields that can be created, edited, deleted, and reordered using drag-and-drop functionality.

## Features Implemented

1. **Tab System**
   - Multiple predefined tabs (Personal, Entity, Entity IRA, Relationships, Accreditation, IRA, Entity Type)
   - Tab navigation with active tab highlighting
   - Fields are filtered by the active tab

2. **Field Management**
   - Create, edit, and delete fields
   - Support for various field types (text, textarea, select, radio, checkbox, date, email, number, file)
   - Required/optional toggle
   - Options management for select, radio, and checkbox fields
   - Drag-and-drop reordering

3. **Database Structure**
   - `offering_questionnaires` table for storing questionnaire metadata
   - `offering_questionnaire_fields` table for storing field data
   - JSON storage for field options

4. **Custom Commands**
   - Commands for seeding predefined fields for each tab
   - Support for updating and refreshing field data

## Implementation Approach

The implementation followed <PERSON><PERSON>'s MVC architecture:

1. **Models**
   - `OfferingQuestionnaire` - Represents a questionnaire with metadata
   - `OfferingQuestionnaireField` - Represents a field within a questionnaire

2. **Controllers & Livewire Components**
   - `OfferingQuestionnaireController` - Handles CRUD operations for questionnaires
   - `Builder` Livewire component - Manages the interactive builder interface

3. **Views**
   - Blade templates with Livewire integration
   - Modal forms for adding/editing fields
   - Responsive design matching the application's styling

## Challenges and Solutions

### 1. Drag-and-Drop Implementation

**Challenge**: Initially implemented using Livewire Sortable, but encountered an "ArgumentCountError" with the standard implementation.

**Solution**: 
- Switched to jQuery UI Sortable for more reliable drag-and-drop functionality
- Implemented custom event handling to update field order in the database
- Added visual feedback during dragging with appropriate CSS

### 2. Field Creation Issues

**Challenge**: The "Add" and "Update" buttons in the field modal were not working correctly.

**Attempted Solutions**:
- Tried changing `wire:model.defer` to `wire:model` for form fields
- Experimented with removing parentheses from the `wire:click` directive
- Added error handling and logging to diagnose issues
- Tried implementing a custom event listener for button clicks

**Root Cause Analysis**:
The issue might be related to:
- Conflicts between form submission events and button click events
- Livewire event handling differences between versions
- Potential issues with the `created_by` field not being properly set

### 3. Authentication Guard Issues

**Challenge**: Using `Auth::id()` without specifying the guard caused null values in the `created_by` field.

**Solution**: Updated to use `Auth::guard('admin')->id()` to get the correct admin user ID.

### 4. Database Schema Evolution

**Challenge**: The `offering_questionnaire_fields` table was missing the `created_by` column.

**Solution**: Created a migration to add the `created_by` column to the table and updated the model's fillable array.

## Custom Commands Reference

1. **AddEntityFieldsCommand**
   - Adds predefined Entity fields to a questionnaire

2. **AddEntityIraFieldsCommand**
   - Adds predefined Entity IRA fields to a questionnaire

3. **AddAccreditationFieldsCommand**
   - Adds predefined Accreditation fields to a questionnaire

4. **AddRelationshipsFieldsCommand**
   - Adds predefined Relationships fields to a questionnaire

5. **AddIraFieldsCommand**
   - Adds predefined IRA fields to a questionnaire

6. **AddEntityTypeFieldsCommand**
   - Adds predefined Entity Type fields to a questionnaire

## Future Improvements

1. **Field Modal Functionality**
   - Resolve the issues with the "Add" and "Update" buttons not working
   - Implement a more robust solution for form submission

2. **Tab Management**
   - Consider implementing a more flexible tab management system
   - Add support for reordering tabs

3. **Field Validation**
   - Enhance field validation rules
   - Add support for custom validation messages

4. **UI Enhancements**
   - Improve visual feedback during drag-and-drop operations
   - Add confirmation dialogs for delete operations

## Conclusion

The Offering Questionnaire module provides a flexible and extensible system for creating and managing questionnaires with multiple tabs and field types. While there are still some issues to resolve, particularly with the field modal functionality, the overall architecture is solid and follows Laravel's best practices.

The module successfully implements the core requirements of tab-based organization, field management, and drag-and-drop reordering. Future work will focus on resolving the remaining issues and implementing the suggested improvements.
