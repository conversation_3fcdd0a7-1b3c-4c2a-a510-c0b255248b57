<?php if(gs('multi_language')): ?>
    <?php
        $language       = App\Models\Language::all();
        $activeLanguage = $language->where('code', config('app.locale'))->first();
    ?>
    <div class="custom--dropdown">
        <div class="custom--dropdown__selected dropdown-list__item">
            <div>
                <div class="thumb">
                    <img src="<?php echo e(getImage(getFilePath('language') . '/' . @$activeLanguage->image, getFileSize('language'))); ?>" alt="image">
                </div>
            </div>
            <span class="text"><?php echo e(__(@$activeLanguage->name)); ?></span>
            <span class="icon"><i class="fas fa-angle-down"></i></span>

        </div>
        <ul class="dropdown-list">
            <?php $__currentLoopData = $language; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if(@$item->id != @$activeLanguage->id): ?>
                    <li class="dropdown-list__item langSel" data-value="<?php echo e($item->code); ?>">
                        <div>
                            <div class="thumb">
                                <img src="<?php echo e(getImage(getFilePath('language') . '/' . @$item->image, getFileSize('language'))); ?>" alt="image">
                            </div>
                        </div>
                        <span class="text"><?php echo e(__($item->name)); ?></span>
                    </li>
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    </div>
<?php endif; ?>
<?php /**PATH C:\laragon\www\PLATFORM\resources\views/templates/basic/partials/language.blade.php ENDPATH**/ ?>