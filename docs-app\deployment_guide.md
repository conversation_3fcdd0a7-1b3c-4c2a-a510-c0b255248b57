# CRE Platform Deployment Guide

## System Requirements

### Server Requirements

1. **Hardware Requirements**
   - CPU: 4+ cores
   - RAM: 16GB minimum
   - Storage: 100GB+ SSD
   - Network: 1Gbps connection

2. **Software Requirements**
   ```bash
   PHP 8.1+
   MySQL 8.0+
   Node.js 16+
   Redis 6+
   Nginx/Apache
   Composer 2+
   ```

### SSL Configuration

1. **Certificate Requirements**
   - Valid SSL certificate
   - Proper chain configuration
   - Automatic renewal setup

2. **SSL Configuration Example**
   ```nginx
   server {
       listen 443 ssl;
       server_name your-cre-platform.com;
       
       ssl_certificate /path/to/certificate.crt;
       ssl_certificate_key /path/to/private.key;
       ssl_protocols TLSv1.2 TLSv1.3;
   }
   ```

## Environment Setup

### 1. Initial Setup

```bash
# Clone repository
git clone [repository-url]
cd [project-directory]

# Install dependencies
composer install --no-dev --optimize-autoloader
npm install
npm run production

# Set up environment file
cp .env.example .env
php artisan key:generate
```

### 2. Environment Configuration

```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-cre-platform.com

DB_CONNECTION=mysql
DB_HOST=your-db-host
DB_PORT=3306
DB_DATABASE=cre_platform
DB_USERNAME=db_user
DB_PASSWORD=secure_password

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

QUEUE_CONNECTION=redis
CACHE_DRIVER=redis
SESSION_DRIVER=redis
```

## Database Setup

### 1. Database Creation

```sql
CREATE DATABASE cre_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'cre_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON cre_platform.* TO 'cre_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. Migration and Seeding

```bash
# Run migrations
php artisan migrate --force

# Seed essential data
php artisan db:seed --class=EssentialDataSeeder

# Optimize database
php artisan db:optimize
```

## Security Configuration

### 1. File Permissions

```bash
# Set correct permissions
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache

# Secure sensitive files
chmod 600 .env
chmod 600 storage/oauth-*.key
```

### 2. Security Headers

```php
// config/security.php
return [
    'headers' => [
        'X-Frame-Options' => 'SAMEORIGIN',
        'X-XSS-Protection' => '1; mode=block',
        'X-Content-Type-Options' => 'nosniff',
        'Strict-Transport-Security' => 'max-age=31536000; includeSubDomains',
        'Content-Security-Policy' => "default-src 'self'"
    ]
];
```

## Queue Configuration

### 1. Queue Worker Setup

```bash
# Install supervisor
apt-get install supervisor

# Create supervisor configuration
cat > /etc/supervisor/conf.d/cre-worker.conf << EOF
[program:cre-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/project/artisan queue:work redis --sleep=3 --tries=3
autostart=true
autorestart=true
user=www-data
numprocs=4
redirect_stderr=true
stdout_logfile=/var/log/cre-worker.log
EOF

# Update supervisor
supervisorctl reread
supervisorctl update
```

### 2. Queue Monitoring

```php
// config/horizon.php
return [
    'environments' => [
        'production' => [
            'supervisor-1' => [
                'connection' => 'redis',
                'queue' => ['default', 'distributions', 'documents'],
                'balance' => 'simple',
                'processes' => 10,
                'tries' => 3,
            ],
        ],
    ],
];
```

## Caching Strategy

### 1. Cache Configuration

```php
// config/cache.php
return [
    'default' => 'redis',
    'stores' => [
        'redis' => [
            'driver' => 'redis',
            'connection' => 'cache',
            'lock_connection' => 'default',
        ],
    ],
];
```

### 2. Cache Implementation

```bash
# Clear all caches before deployment
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## Monitoring Setup

### 1. Application Monitoring

```php
// config/logging.php
return [
    'channels' => [
        'stack' => [
            'driver' => 'stack',
            'channels' => ['daily', 'slack'],
        ],
        'slack' => [
            'driver' => 'slack',
            'url' => env('LOG_SLACK_WEBHOOK_URL'),
            'username' => 'CRE Platform Monitor',
            'emoji' => ':boom:',
            'level' => 'critical',
        ],
    ],
];
```

### 2. Performance Monitoring

```bash
# Install monitoring tools
composer require laravel/telescope --dev

# Configure Telescope
php artisan telescope:install
php artisan migrate
```

## Backup Configuration

### 1. Database Backup

```bash
# Create backup script
cat > /usr/local/bin/cre-backup.sh << EOF
#!/bin/bash
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="/path/to/backups"
mysqldump -u root -p cre_platform > "$BACKUP_DIR/db_backup_$TIMESTAMP.sql"
gzip "$BACKUP_DIR/db_backup_$TIMESTAMP.sql"
EOF

# Set up cron job
0 0 * * * /usr/local/bin/cre-backup.sh
```

### 2. File Backup

```php
// config/backup.php
return [
    'backup' => [
        'name' => 'cre_platform_backup',
        'source' => [
            'files' => [
                'include' => [
                    base_path(),
                ],
                'exclude' => [
                    base_path('vendor'),
                    base_path('node_modules'),
                ],
            ],
            'databases' => [
                'mysql',
            ],
        ],
    ],
];
```

## Deployment Process

### 1. Pre-deployment Checklist

```bash
# Check requirements
php artisan about

# Run tests
php artisan test --env=testing

# Check database status
php artisan migrate:status
```

### 2. Vue.js Build Configuration

```bash
# Vue.js specific build steps
npm install vue@3.2     # Install Vue 3
npm install @vitejs/plugin-vue   # Install Vite Vue plugin

# Configure Vite for Vue
# vite.config.js
import vue from '@vitejs/plugin-vue'
export default {
    plugins: [vue()],
    build: {
        // Optimize waterfall components
        rollupOptions: {
            input: {
                waterfall: 'resources/js/components/Waterfall.vue'
            }
        }
    }
}
```

### 3. Deployment Steps

```bash
# Put application in maintenance mode
php artisan down

# Pull latest changes
git pull origin main

# Install/update dependencies
composer install --no-dev --optimize-autoloader
npm install

# Build Vue components and assets
npm run build  # This will build Vue components with Vite
npm run production  # This builds other assets

# Run migrations
php artisan migrate --force

# Clear and optimize
php artisan optimize
php artisan view:cache

# Restart queue workers
supervisorctl restart all

# Bring application back online
php artisan up
```

## Post-deployment Verification

### 1. Health Checks

```php
// routes/web.php
Route::get('/health', function () {
    return response()->json([
        'status' => 'healthy',
        'database' => DB::connection()->getPdo() ? 'connected' : 'failed',
        'cache' => Cache::connection()->ping() ? 'connected' : 'failed',
        'queue' => Queue::size() !== null ? 'running' : 'failed'
    ]);
});
```

### 2. Monitoring Checks

```bash
# Check error logs
tail -f storage/logs/laravel.log

# Monitor queue processing
php artisan horizon:status

# Check system resources
htop