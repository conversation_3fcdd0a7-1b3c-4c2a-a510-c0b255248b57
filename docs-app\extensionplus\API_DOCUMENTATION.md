# Extension System API Documentation

## 1. Overview

This document provides a comprehensive reference for the Extension System API, including core classes, interfaces, and methods available for extension developers.

## 2. Core API Classes

### 2.1 ExtensionRegistry

The `ExtensionRegistry` class is responsible for managing the registration and retrieval of extensions.

```php
namespace App\Extensions;

class ExtensionRegistry
{
    /**
     * Get all registered extensions
     *
     * @return array Array of Extension instances
     */
    public function all(): array;
    
    /**
     * Get a specific extension by code
     *
     * @param string $code Extension code
     * @return Extension|null Extension instance or null if not found
     */
    public function get(string $code): ?Extension;
    
    /**
     * Check if an extension is registered
     *
     * @param string $code Extension code
     * @return bool True if extension is registered
     */
    public function has(string $code): bool;
    
    /**
     * Register an extension
     *
     * @param Extension $extension Extension instance
     * @return void
     */
    public function register(Extension $extension): void;
    
    /**
     * Get all enabled extensions
     *
     * @return array Array of enabled Extension instances
     */
    public function enabled(): array;
    
    /**
     * Get all disabled extensions
     *
     * @return array Array of disabled Extension instances
     */
    public function disabled(): array;
}
```

### 2.2 Extension

The `Extension` class represents an individual extension and provides methods for interacting with it.

```php
namespace App\Extensions;

class Extension
{
    /**
     * Get the extension code
     *
     * @return string Extension code
     */
    public function getCode(): string;
    
    /**
     * Get the extension name
     *
     * @return string Extension name
     */
    public function getName(): string;
    
    /**
     * Get the extension version
     *
     * @return string Extension version
     */
    public function getVersion(): string;
    
    /**
     * Get the extension description
     *
     * @return string Extension description
     */
    public function getDescription(): string;
    
    /**
     * Get the extension author
     *
     * @return string Extension author
     */
    public function getAuthor(): string;
    
    /**
     * Get the extension author URL
     *
     * @return string Extension author URL
     */
    public function getAuthorUrl(): string;
    
    /**
     * Get the extension manifest
     *
     * @return array Extension manifest
     */
    public function getManifest(): array;
    
    /**
     * Check if the extension is enabled
     *
     * @return bool True if extension is enabled
     */
    public function isEnabled(): bool;
    
    /**
     * Enable the extension
     *
     * @return bool True if extension was enabled
     */
    public function enable(): bool;
    
    /**
     * Disable the extension
     *
     * @return bool True if extension was disabled
     */
    public function disable(): bool;
    
    /**
     * Get the extension path
     *
     * @param string $path Optional path to append
     * @return string Full path to the extension or a file within it
     */
    public function path(string $path = ''): string;
    
    /**
     * Get a setting value
     *
     * @param string $key Setting key
     * @param mixed $default Default value if setting not found
     * @return mixed Setting value
     */
    public function getSetting(string $key, $default = null);
    
    /**
     * Set a setting value
     *
     * @param string $key Setting key
     * @param mixed $value Setting value
     * @return void
     */
    public function setSetting(string $key, $value): void;
}
```

### 2.3 HookSystem

The `HookSystem` class manages hooks and provides methods for registering and executing hooks.

```php
namespace App\Extensions;

class HookSystem
{
    /**
     * Register a hook handler
     *
     * @param string $hookName Hook name
     * @param callable $handler Hook handler function
     * @param int $priority Hook priority (lower numbers execute first)
     * @param string $extensionCode Extension code
     * @return void
     */
    public function register(string $hookName, callable $handler, int $priority = 10, string $extensionCode = null): void;
    
    /**
     * Execute hooks for a specific hook name
     *
     * @param string $hookName Hook name
     * @param mixed $data Data to pass to hook handlers
     * @return mixed Modified data after hook execution
     */
    public function execute(string $hookName, $data = null);
    
    /**
     * Check if a hook has any registered handlers
     *
     * @param string $hookName Hook name
     * @return bool True if hook has handlers
     */
    public function hasHandlers(string $hookName): bool;
    
    /**
     * Get all registered hooks
     *
     * @return array Array of registered hooks
     */
    public function getRegisteredHooks(): array;
    
    /**
     * Remove all hooks for a specific extension
     *
     * @param string $extensionCode Extension code
     * @return void
     */
    public function removeExtensionHooks(string $extensionCode): void;
}
```

### 2.4 ExtensionLoader

The `ExtensionLoader` class is responsible for loading extensions and their service providers.

```php
namespace App\Extensions;

class ExtensionLoader
{
    /**
     * Load all enabled extensions
     *
     * @return void
     */
    public function loadAll(): void;
    
    /**
     * Load a specific extension
     *
     * @param string $code Extension code
     * @return bool True if extension was loaded
     */
    public function load(string $code): bool;
    
    /**
     * Register an extension's service provider
     *
     * @param string $providerClass Service provider class name
     * @return void
     */
    public function registerServiceProvider(string $providerClass): void;
    
    /**
     * Boot an extension's service provider
     *
     * @param string $providerClass Service provider class name
     * @return void
     */
    public function bootServiceProvider(string $providerClass): void;
}
```

### 2.5 ExtensionManager

The `ExtensionManager` class provides high-level methods for managing extensions.

```php
namespace App\Extensions;

class ExtensionManager
{
    /**
     * Install an extension from a package
     *
     * @param string $packagePath Path to the extension package
     * @return bool True if extension was installed
     */
    public function install(string $packagePath): bool;
    
    /**
     * Uninstall an extension
     *
     * @param string $code Extension code
     * @return bool True if extension was uninstalled
     */
    public function uninstall(string $code): bool;
    
    /**
     * Enable an extension
     *
     * @param string $code Extension code
     * @return bool True if extension was enabled
     */
    public function enable(string $code): bool;
    
    /**
     * Disable an extension
     *
     * @param string $code Extension code
     * @return bool True if extension was disabled
     */
    public function disable(string $code): bool;
    
    /**
     * Update an extension
     *
     * @param string $code Extension code
     * @param string $packagePath Path to the extension package
     * @return bool True if extension was updated
     */
    public function update(string $code, string $packagePath): bool;
    
    /**
     * Get extension dependencies
     *
     * @param string $code Extension code
     * @return array Array of dependencies
     */
    public function getDependencies(string $code): array;
    
    /**
     * Check if an extension can be safely uninstalled
     *
     * @param string $code Extension code
     * @return bool True if extension can be uninstalled
     */
    public function canUninstall(string $code): bool;
}
```

## 3. Extension Service Provider

Extension developers should create a service provider that extends the `ExtensionServiceProvider` class.

```php
namespace App\Extensions;

use Illuminate\Support\ServiceProvider;

abstract class ExtensionServiceProvider extends ServiceProvider
{
    /**
     * Extension code
     *
     * @var string
     */
    protected $extensionCode;
    
    /**
     * Register extension services
     *
     * @return void
     */
    public function register(): void
    {
        // Register extension services
    }
    
    /**
     * Boot extension services
     *
     * @return void
     */
    public function boot(): void
    {
        // Boot extension services
    }
    
    /**
     * Register extension routes
     *
     * @return void
     */
    protected function registerRoutes(): void
    {
        // Register extension routes
    }
    
    /**
     * Register extension views
     *
     * @return void
     */
    protected function registerViews(): void
    {
        // Register extension views
    }
    
    /**
     * Register extension hooks
     *
     * @return void
     */
    protected function registerHooks(): void
    {
        // Register extension hooks
    }
    
    /**
     * Register extension menu items
     *
     * @return void
     */
    protected function registerMenuItems(): void
    {
        // Register extension menu items
    }
}
```

## 4. Hook API

### 4.1 Available Hooks

The following hooks are available for extensions to use:

#### Deal Hooks

- `deal.create.before`: Called before a deal is created
- `deal.create.after`: Called after a deal is created
- `deal.update.before`: Called before a deal is updated
- `deal.update.after`: Called after a deal is updated
- `deal.delete.before`: Called before a deal is deleted
- `deal.delete.after`: Called after a deal is deleted
- `deal.view.before`: Called before a deal is viewed
- `deal.view.after`: Called after a deal is viewed

#### Offering Hooks

- `offering.create.before`: Called before an offering is created
- `offering.create.after`: Called after an offering is created
- `offering.update.before`: Called before an offering is updated
- `offering.update.after`: Called after an offering is updated
- `offering.delete.before`: Called before an offering is deleted
- `offering.delete.after`: Called after an offering is deleted
- `offering.view.before`: Called before an offering is viewed
- `offering.view.after`: Called after an offering is viewed

#### Asset Hooks

- `asset.create.before`: Called before an asset is created
- `asset.create.after`: Called after an asset is created
- `asset.update.before`: Called before an asset is updated
- `asset.update.after`: Called after an asset is updated
- `asset.delete.before`: Called before an asset is deleted
- `asset.delete.after`: Called after an asset is deleted
- `asset.view.before`: Called before an asset is viewed
- `asset.view.after`: Called after an asset is viewed

#### UI Hooks

- `admin.sidebar`: Called when rendering the admin sidebar
- `admin.dashboard`: Called when rendering the admin dashboard
- `admin.header`: Called when rendering the admin header
- `admin.footer`: Called when rendering the admin footer
- `user.sidebar`: Called when rendering the user sidebar
- `user.dashboard`: Called when rendering the user dashboard
- `user.header`: Called when rendering the user header
- `user.footer`: Called when rendering the user footer

### 4.2 Using Hooks

To register a hook handler, use the `HookSystem::register` method:

```php
// In your extension service provider
protected function registerHooks(): void
{
    app(HookSystem::class)->register('deal.create.after', function($deal) {
        // Do something with the deal
        return $deal;
    }, 10, $this->extensionCode);
}
```

To execute hooks, use the `HookSystem::execute` method:

```php
// In your controller
public function store(Request $request)
{
    $data = $request->validated();
    
    // Execute hooks before creating the deal
    $data = app(HookSystem::class)->execute('deal.create.before', $data);
    
    $deal = Deal::create($data);
    
    // Execute hooks after creating the deal
    $deal = app(HookSystem::class)->execute('deal.create.after', $deal);
    
    return response()->json($deal);
}
```

## 5. Extension Manifest

Each extension must include a `manifest.json` file in its root directory. The manifest file defines the extension's metadata and requirements.

```json
{
    "name": "Loopnet Integration",
    "code": "loopnet",
    "version": "1.0.0",
    "description": "Integrates with Loopnet to import property listings",
    "author": "CREI LLC",
    "author_url": "https://example.com",
    "requires": {
        "platform": ">=1.0.0",
        "php": ">=7.4.0",
        "extensions": {
            "api-client": ">=1.0.0"
        }
    },
    "permissions": [
        "deal.create",
        "offering.create",
        "asset.create"
    ],
    "settings": {
        "api_key": {
            "type": "string",
            "label": "API Key",
            "description": "Loopnet API Key",
            "required": true
        },
        "api_secret": {
            "type": "password",
            "label": "API Secret",
            "description": "Loopnet API Secret",
            "required": true
        },
        "results_per_page": {
            "type": "integer",
            "label": "Results Per Page",
            "description": "Number of results to display per page",
            "default": 10,
            "min": 5,
            "max": 50
        }
    },
    "providers": [
        "Acme\\Loopnet\\LoopnetServiceProvider"
    ],
    "assets": {
        "js": [
            "js/loopnet.js"
        ],
        "css": [
            "css/loopnet.css"
        ]
    },
    "menu_items": [
        {
            "label": "Loopnet",
            "icon": "fa-building",
            "route": "admin.loopnet.index",
            "permission": "admin.loopnet.view",
            "children": [
                {
                    "label": "Search Listings",
                    "route": "admin.loopnet.search",
                    "permission": "admin.loopnet.search"
                },
                {
                    "label": "Imported Listings",
                    "route": "admin.loopnet.listings",
                    "permission": "admin.loopnet.listings.view"
                },
                {
                    "label": "Settings",
                    "route": "admin.loopnet.settings",
                    "permission": "admin.loopnet.settings"
                }
            ]
        }
    ]
}
```

## 6. Extension Directory Structure

Extensions should follow this directory structure:

```
extensions/
  loopnet/
    manifest.json
    composer.json
    src/
      LoopnetServiceProvider.php
      Controllers/
        LoopnetController.php
        SearchController.php
        ImportController.php
      Models/
        LoopnetListing.php
        LoopnetSearch.php
      Services/
        LoopnetApiClient.php
      routes/
        web.php
        api.php
      resources/
        views/
          index.blade.php
          search.blade.php
          listings.blade.php
          settings.blade.php
        js/
          loopnet.js
        css/
          loopnet.css
      database/
        migrations/
          create_loopnet_listings_table.php
          create_loopnet_searches_table.php
```

## 7. Extension Settings API

The Extension Settings API provides methods for accessing and modifying extension settings.

```php
namespace App\Extensions;

class ExtensionSettings
{
    /**
     * Get a setting value
     *
     * @param string $extensionCode Extension code
     * @param string $key Setting key
     * @param mixed $default Default value if setting not found
     * @return mixed Setting value
     */
    public function get(string $extensionCode, string $key, $default = null);
    
    /**
     * Set a setting value
     *
     * @param string $extensionCode Extension code
     * @param string $key Setting key
     * @param mixed $value Setting value
     * @return void
     */
    public function set(string $extensionCode, string $key, $value): void;
    
    /**
     * Get all settings for an extension
     *
     * @param string $extensionCode Extension code
     * @return array Array of settings
     */
    public function all(string $extensionCode): array;
    
    /**
     * Check if a setting exists
     *
     * @param string $extensionCode Extension code
     * @param string $key Setting key
     * @return bool True if setting exists
     */
    public function has(string $extensionCode, string $key): bool;
    
    /**
     * Remove a setting
     *
     * @param string $extensionCode Extension code
     * @param string $key Setting key
     * @return void
     */
    public function remove(string $extensionCode, string $key): void;
    
    /**
     * Remove all settings for an extension
     *
     * @param string $extensionCode Extension code
     * @return void
     */
    public function removeAll(string $extensionCode): void;
}
```

## 8. Loopnet API Client

The Loopnet extension includes an API client for interacting with the Loopnet API.

```php
namespace Acme\Loopnet\Services;

class LoopnetApiClient
{
    /**
     * Search for listings
     *
     * @param array $parameters Search parameters
     * @return array Search results
     */
    public function search(array $parameters): array;
    
    /**
     * Get a listing by ID
     *
     * @param string $id Listing ID
     * @return array Listing data
     */
    public function getListing(string $id): array;
    
    /**
     * Get listing images
     *
     * @param string $id Listing ID
     * @return array Image data
     */
    public function getListingImages(string $id): array;
    
    /**
     * Get listing documents
     *
     * @param string $id Listing ID
     * @return array Document data
     */
    public function getListingDocuments(string $id): array;
    
    /**
     * Get listing contacts
     *
     * @param string $id Listing ID
     * @return array Contact data
     */
    public function getListingContacts(string $id): array;
}
```
