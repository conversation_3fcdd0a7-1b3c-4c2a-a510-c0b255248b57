# Staff Management System Documentation

## Overview
The staff management system allows administrators to manage staff members and their roles within the CREI Investment Platform. It uses <PERSON><PERSON>'s Spatie Permission package for role-based access control.

## Components

### 1. Controllers
- Location: `app/Http/Controllers/Admin/StaffController.php`
- Handles all staff and role management operations

### 2. Views
- Location: `resources/views/admin/staff/`
- Files:
  - `index.blade.php`: Staff listing
  - `create.blade.php`: Add new staff
  - `edit.blade.php`: Edit staff
  - `roles.blade.php`: Role management
  - `edit_role.blade.php`: Edit role permissions

### 3. Routes
- Location: `routes/admin.php`
- All routes are prefixed with `admin/staff`
- Protected by admin middleware

## Routes Structure

```php
// Staff Management
Route::prefix('staff')->name('staff.')->group(function () {
    Route::get('/', 'index')->name('index');
    Route::get('/create', 'create')->name('create');
    Route::post('/store', 'store')->name('store');
    Route::get('/edit/{id}', 'edit')->name('edit');
    Route::post('/update/{id}', 'update')->name('update');
    Route::delete('/{id}', 'destroy')->name('destroy');
    
    // Role Management
    Route::get('/roles', 'roles')->name('roles');
    Route::post('/roles', 'storeRole')->name('roles.store');
    Route::get('/roles/{id}/edit', 'editRole')->name('roles.edit');
    Route::put('/roles/{id}', 'updateRole')->name('roles.update');
    Route::delete('/roles/{id}', 'destroyRole')->name('roles.destroy');
});
```

## Available Roles
1. Super Admin
2. Investment Management
3. User Management
4. Marketing Management
5. Finance Management
6. Property Management

## Important Notes

### Role Management
1. Super Admin role cannot be:
   - Edited
   - Deleted
   - Modified in any way

2. When creating/editing roles:
   - Name must be unique
   - At least one permission must be selected
   - Permissions are grouped by module

### Staff Management
1. Staff members require:
   - Unique email
   - Unique username (min 6 characters)
   - Password (min 6 characters)
   - Assigned role

2. Super Admin users:
   - Cannot be deleted
   - Have access to all permissions

## Views and Templates

### roles.blade.php
- Displays list of roles
- Allows creating new roles
- Shows role permissions
- Requires:
  - `$roles`: Collection of Role models
  - `$permissions`: Grouped permissions by module
  - `$pageTitle`: Page title string

### edit_role.blade.php
- Edit role permissions
- Requires:
  - `$role`: Role model instance
  - `$permissions`: Grouped permissions
  - `$pageTitle`: Page title string

## Common Issues and Solutions

1. **Missing Permissions Variable**
   - Ensure `roles()` method passes grouped permissions to view
   - Check `Permission::all()->groupBy()` is working

2. **Route Not Found**
   - Clear route cache: `php artisan route:clear`
   - Verify route names match view references

3. **Role Update Fails**
   - Check form method is PUT/PATCH
   - Ensure role ID is properly passed
   - Verify permissions array is present

## Dependencies
- Laravel Spatie Permission package
- Bootstrap 5 for UI
- jQuery for AJAX operations
