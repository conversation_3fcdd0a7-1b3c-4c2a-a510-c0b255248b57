@extends('admin.layouts.app')
@section('panel')
    <div class="d-flex justify-content-between mt-4 align-items-center">
        <h4>Email Template Editor</h4>
        <div>
            <button class="btn btn-secondary" onclick="history.back()">Back</button>
            <button class="btn btn-primary" x-data @click="saveTemplate()">Save Template</button>
        </div>
    </div>
    <hr>

    {{-- Email Editor --}}
    <div class="editor-complete">
        <div id="editor-container"></div>
    </div>

    <style>
        #editor-container {
            height: 100%;
        }

        .editor-complete {
            height: 700px;
        }
    </style>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="https://editor.unlayer.com/embed.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
@endsection
@push('script')
    <script>
        unlayer.init({
            id: 'editor-container',
            projectId: 275698,
            displayMode: 'email',
        });

        function saveTemplate() {
            unlayer.exportHtml(function(data) {
                debugger;
                const design = data.design; // JSON design to save
                let url = "{{ route('admin.templates.addTemplate') }}";
                fetch(url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                        },
                        body: JSON.stringify({
                            design: design
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('Template saved successfully!');
                        } else {
                            alert('Error saving template: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Failed to save template.');
                    });
            });
        }
    </script>
@endpush
