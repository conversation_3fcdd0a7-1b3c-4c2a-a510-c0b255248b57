<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Distribution;
use App\Http\Controllers\Admin\DistributionsController;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class ProcessDistributions extends Command
{
    protected $signature = 'distributions:process';
    protected $description = 'Process distributions scheduled for the current date';

    public function handle()
    {
        \Log::info('ProcessDistributions command started');

        $today = Carbon::today()->toDateString();
        $distributions = Distribution::where('distribution_date', $today)
            ->where('approved', false)
            ->get();

        \Log::info('ProcessDistributions: Found $', ['count' => $distributions->count()]);

        $controller = app(DistributionsController::class);

        foreach ($distributions as $distribution) {
            try {
                $result = $controller->processDistribution($distribution);
                if ($result['status'] === 'success') {
                    $distribution->update(['approved' => true]);
                    \Log::info('ProcessDistributions: Processed distribution', [
                        'distribution_id' => $distribution->id,
                        'investment_ids' => $result['investment_ids'] ?? [],
                    ]);
                } else {
                    \Log::error('ProcessDistributions: Failed to process distribution', [
                        'distribution_id' => $distribution->id,
                        'error' => $result['message'],
                    ]);
                }
            } catch (\Exception $e) {
                \Log::error('ProcessDistributions: Exception', [
                    'distribution_id' => $distribution->id,
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }

        \Log::info('ProcessDistributions: Completed');
    }
}