# Partner View Templates Guide

## Overview

This document provides instructions for creating the view templates for the Partner interface. The approach is to create partner-specific views that are based on the existing admin views but with modifications for the partner context.

## Implementation Steps

### 1. Create Partner Layout

Create a new layout file at `resources/views/partner/layouts/app.blade.php` by copying and modifying the admin layout:

```php
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $general->sitename($pageTitle ?? '') }}</title>
    <!-- Include the same CSS and JS as the admin layout -->
    <link rel="stylesheet" href="{{ asset('assets/admin/css/app.css') }}">
    <!-- Add partner-specific CSS if needed -->
    <link rel="stylesheet" href="{{ asset('assets/partner/css/partner.css') }}">
</head>
<body class="hold-transition sidebar-mini">
    <div class="wrapper">
        <!-- Navbar -->
        @include('partner.layouts.partials.topnav')
        <!-- /.navbar -->

        <!-- Main Sidebar Container -->
        @include('partner.layouts.partials.sidenav')

        <!-- Content Wrapper. Contains page content -->
        <div class="content-wrapper">
            <!-- Content Header (Page header) -->
            <div class="content-header">
                <div class="container-fluid">
                    <div class="row mb-2">
                        <div class="col-sm-6">
                            <h1 class="m-0">{{ $pageTitle ?? '' }}</h1>
                        </div>
                        <div class="col-sm-6">
                            <ol class="breadcrumb float-sm-right">
                                <li class="breadcrumb-item"><a href="{{ route('partner.dashboard') }}">Dashboard</a></li>
                                @stack('breadcrumb-items')
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /.content-header -->

            <!-- Main content -->
            <section class="content">
                <div class="container-fluid">
                    @yield('content')
                </div>
            </section>
            <!-- /.content -->
        </div>
        <!-- /.content-wrapper -->

        <!-- Footer -->
        @include('partner.layouts.partials.footer')
    </div>
    <!-- ./wrapper -->

    <!-- Scripts -->
    <script src="{{ asset('assets/admin/js/app.js') }}"></script>
    <!-- Add partner-specific JS if needed -->
    <script src="{{ asset('assets/partner/js/partner.js') }}"></script>
    @stack('scripts')
</body>
</html>
```

### 2. Create Partner Sidebar

Create a sidebar navigation file at `resources/views/partner/layouts/partials/sidenav.blade.php`:

```php
<aside class="main-sidebar sidebar-dark-primary elevation-4">
    <!-- Brand Logo -->
    <a href="{{ route('partner.dashboard') }}" class="brand-link">
        <img src="{{ getImage(imagePath()['logoIcon']['path'] .'/logo.png') }}" alt="Logo" class="brand-image img-circle elevation-3" style="opacity: .8">
        <span class="brand-text font-weight-light">{{ __($general->sitename) }}</span>
    </a>

    <!-- Sidebar -->
    <div class="sidebar">
        <!-- Sidebar user panel -->
        <div class="user-panel mt-3 pb-3 mb-3 d-flex">
            <div class="image">
                <img src="{{ getImage(imagePath()['profile']['admin']['path'].'/'. auth()->guard('admin')->user()->image) }}" class="img-circle elevation-2" alt="User Image">
            </div>
            <div class="info">
                <a href="#" class="d-block">{{ auth()->guard('admin')->user()->name }}</a>
            </div>
        </div>

        <!-- Sidebar Menu -->
        <nav class="mt-2">
            <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                <li class="nav-item">
                    <a href="{{ route('partner.dashboard') }}" class="nav-link {{ request()->routeIs('partner.dashboard') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-tachometer-alt"></i>
                        <p>Dashboard</p>
                    </a>
                </li>
                
                <li class="nav-item {{ request()->routeIs('partner.deals*') ? 'menu-open' : '' }}">
                    <a href="#" class="nav-link {{ request()->routeIs('partner.deals*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-building"></i>
                        <p>
                            Deals
                            <i class="right fas fa-angle-left"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <li class="nav-item">
                            <a href="{{ route('partner.deals.index') }}" class="nav-link {{ request()->routeIs('partner.deals.index') ? 'active' : '' }}">
                                <i class="far fa-circle nav-icon"></i>
                                <p>All Deals</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ route('partner.deals.create') }}" class="nav-link {{ request()->routeIs('partner.deals.create') ? 'active' : '' }}">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Create Deal</p>
                            </a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <a href="{{ route('partner.profile.index') }}" class="nav-link {{ request()->routeIs('partner.profile*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-user"></i>
                        <p>Profile</p>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="{{ route('admin.logout') }}" class="nav-link">
                        <i class="nav-icon fas fa-sign-out-alt"></i>
                        <p>Logout</p>
                    </a>
                </li>
            </ul>
        </nav>
        <!-- /.sidebar-menu -->
    </div>
    <!-- /.sidebar -->
</aside>
```

### 3. Create Partner Deal Views

Create the following view files by copying and modifying the corresponding admin views:

#### Deal Index View

Create `resources/views/partner/deals/index.blade.php`:

```php
@extends('partner.layouts.app')

@section('content')
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">All Deals</h3>
                <div class="card-tools">
                    <a href="{{ route('partner.deals.create') }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> Create Deal
                    </a>
                </div>
            </div>
            <div class="card-body table-responsive p-0">
                <table class="table table-hover text-nowrap">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Type</th>
                            <th>Stage</th>
                            <th>Created At</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($deals as $deal)
                            <tr>
                                <td>{{ $deal->name }}</td>
                                <td>{{ $deal->type }}</td>
                                <td>{{ $deal->deal_stage }}</td>
                                <td>{{ $deal->created_at->format('Y-m-d') }}</td>
                                <td>
                                    <a href="{{ route('partner.deals.show', $deal->id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('partner.deals.edit', $deal->id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('partner.deals.destroy', $deal->id) }}" method="POST" style="display: inline;">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this deal?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="text-center">No deals found</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection
```

#### Deal Create View

Create `resources/views/partner/deals/create.blade.php` by copying and modifying the admin create view.

#### Deal Edit View

Create `resources/views/partner/deals/edit.blade.php` by copying and modifying the admin edit view.

#### Deal Show View

Create `resources/views/partner/deals/show.blade.php` by copying and modifying the admin show view.

### 4. Create Other Required View Files

Create the following additional view files:

- `resources/views/partner/layouts/partials/topnav.blade.php`
- `resources/views/partner/layouts/partials/footer.blade.php`
- `resources/views/partner/profile/index.blade.php`

### 5. Create Partner CSS and JS Files (Optional)

Create partner-specific CSS and JS files if needed:

- `public/assets/partner/css/partner.css`
- `public/assets/partner/js/partner.js`

## How the View Templates Work

1. **Layout Structure**: The partner layout follows the same structure as the admin layout but with partner-specific branding and navigation.

2. **Sidebar Navigation**: The sidebar includes links to the partner dashboard, deals, and profile.

3. **Route References**: All links use partner-specific route names (`partner.dashboard`, `partner.deals.index`, etc.).

4. **Consistent Styling**: The partner interface uses the same styling as the admin interface for consistency.

## Troubleshooting

### Common Issues

1. **Missing Assets**: Ensure that the CSS and JS files referenced in the layout exist.

2. **Route Not Found**: Verify that all routes referenced in the views are defined in `routes/partner.php`.

3. **Layout Issues**: Check that all required partials (topnav, sidenav, footer) are created and included correctly.

### Debugging Tips

Add temporary debugging output to the views:

```php
{{ dd(auth()->guard('admin')->user()) }}
{{ dd(request()->route()->getName()) }}
```

This implementation follows the principle that "roles and permissions are there to interact with existing modules and not the other way around" by creating partner-specific views that are based on the existing admin views but with modifications for the partner context.
