# Roles and Permissions System Documentation

## Overview

The roles and permissions system in the CRE Investment Platform follows the principle that "roles and permissions are there to interact with existing modules and not the other way around." This means that the permissions system adapts to the existing architecture rather than requiring changes to the modules.

## System Architecture

### Permission Structure

Permissions are organized based on the admin sidebar structure, providing a clear mapping between UI elements and backend access controls. Each permission follows a format of `{module}.{action}`, such as:

- `users.view`
- `deals.create`
- `system.manage_backups`

### Role Types

1. **Executive Roles (Protected)**
   - Super Admin: Has access to all permissions
   - CEO, CFO, COO, CTO, CLO: Have specific sets of permissions relevant to their domains

2. **Department Manager Roles**
   - Accounting Manager: Access to deposits, withdrawals, and investments
   - Investor Relations Manager: Access to users and limited deal access
   - Compliance & Legal Manager: Access to deals, offerings, and documents

## Implementation Details

### Permission Checking

Permission checking is implemented through the `PermissionChecker` trait, which provides methods to verify if a user has the required permissions to access specific features:

```php
private function checkPermission()
{
    $user = auth()->guard('admin')->user();
    
    // Allow Super Admin to access features regardless of specific permission
    if ($user->hasRole('Super Admin')) {
        return true;
    }
    
    // Check for specific permission for other roles
    if (!$user->can('system.manage_backups')) {
        abort(403, 'Unauthorized action.');
    }
}
```

### Role Management

Roles are managed through the `StaffController`, which includes methods for creating, updating, and deleting roles. The controller ensures that:

1. Executive roles are protected from modification
2. All roles have at least one permission
3. The Super Admin role always maintains all permissions

## Technical Implementation

### Permission Map

The system uses a `permission_map.php` configuration file that defines all available permissions and their groupings. This map is used to generate the permission UI and validate permission assignments.

### Middleware

The `CheckPermission` middleware verifies that users have the required permissions to access specific routes:

```php
public function handle($request, Closure $next)
{
    $user = auth()->guard('admin')->user();
    $routeName = $request->route()->getName();
    
    // Get the permission required for this route
    $permission = config('permission_routes.' . $routeName);
    
    if ($permission && !$user->can($permission) && !$user->hasRole('Super Admin')) {
        abort(403, 'Unauthorized action.');
    }
    
    return $next($request);
}
```

## Challenges and Solutions

### Challenge 1: Permission Alignment with Admin Sidebar

**Problem**: The existing permissions did not align with the actual modules in the admin sidebar, leading to confusion and potential security gaps.

**Solution**: 
- Created and ran `check_existing_permissions.php` to analyze existing permissions against the admin sidebar structure
- Identified permissions that were missing or unused based on actual modules in the application
- Updated the permission map to align with the actual modules

### Challenge 2: Inconsistent Role Management

**Problem**: The `updateRole` method in `StaffController` had issues with permission validation and role protection.

**Solution**:
- Fixed the `updateRole` method to handle permission validation better
- Implemented protection for executive roles to prevent modification
- Added validation to ensure roles always have at least one permission

### Challenge 3: Missing Technical Permissions

**Problem**: Technical permissions for managing APIs, data, and system settings were not properly defined or assigned.

**Solution**:
- Created and maintained tech-related permissions (`tech.manage_apis`, `tech.manage_data`, etc.)
- Created and assigned the CTO role with appropriate tech permissions
- Ensured these permissions were properly integrated into the permission checking system

### Challenge 4: Non-existent Method in StaffController

**Problem**: The `update` method in `StaffController` was using a non-existent `findById()` method.

**Solution**:
- Fixed the `update` method to use `find()` instead of the non-existent `findById()` method
- Ensured proper error handling for cases where a staff member is not found

## Best Practices

1. **Always check permissions** in controller methods using the `checkPermission()` method
2. **Never modify protected roles** (Super Admin, CEO, CFO, COO, CTO, CLO)
3. **Ensure new features have corresponding permissions** in the permission map
4. **Follow the existing permission naming convention** when adding new permissions
5. **Use role-based access control** rather than hard-coding user checks

## Maintenance and Updates

When adding new modules or features to the platform:

1. Add corresponding permissions to the `permission_map.php` file
2. Update the `permission_routes.php` file to map routes to permissions
3. Implement permission checks in controller methods
4. Assign appropriate permissions to existing roles

By following these guidelines, the roles and permissions system will continue to provide secure and flexible access control for the CRE Investment Platform.
