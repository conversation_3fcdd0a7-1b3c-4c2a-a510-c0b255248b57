<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class kpisCollection extends Model
{
    protected $fillable = [
        'deal_id',
        'kpi_collection_name',
        'kpi_collection_type',
        'kpi_data'
    ];
    protected $casts = [
        'kpi_data' => 'array',
    ];


    public function deal()
    {
        return $this->belongsTo(Deal::class);
    }

    public function kpis()
    {
        return $this->hasMany(Kpi::class);
    }

    public function kpi_names()
    {
        return $this->hasMany(KpiName::class);
    }

}
