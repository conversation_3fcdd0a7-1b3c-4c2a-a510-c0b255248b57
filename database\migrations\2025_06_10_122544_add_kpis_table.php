<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the kpis_collections table if it exists
        Schema::dropIfExists('kpis_collections');

        // Create kpis_collections table
        Schema::create('kpis_collections', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('deal_id');
            $table->foreign('deal_id')->references('id')->on('deals')->onDelete('cascade');
            $table->string('kpi_collection_name')->nullable();
            $table->string('kpi_collection_type')->nullable();
            $table->timestamps();
        });

        // Create kpis table
        Schema::create('kpis', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('kpis_collection_id');
            $table->foreign('kpis_collection_id')->references('id')->on('kpis_collections')->onDelete('cascade');
            $table->unsignedBigInteger('kpi_name')->nullable();
            $table->foreign('kpi_name')->references('id')->on('kpi_names')->onDelete('cascade'); 
            $table->string('kpi_collection_format')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kpis');
        Schema::dropIfExists('kpis_collections');
    }
};
