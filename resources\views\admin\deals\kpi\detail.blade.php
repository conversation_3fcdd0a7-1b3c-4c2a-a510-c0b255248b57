@extends('admin.layouts.app')
@php
    if (auth('admin')->user()->has<PERSON><PERSON>('partner')) {
        $prefix = 'partner';
    } else {
        $prefix = 'admin';
    }
@endphp
@push('style')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/plugins/monthSelect/style.css">
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.10.0/css/bootstrap-datepicker.min.css">
@endpush
@section('panel')
    <div x-data="KPIFormDetail()">
        <template x-if="loading">
            <div class="custom-loader-overlay">
                <div class="custom-loader"></div>
            </div>
        </template>
        <div class="mb-4 mt-3">
            <h1>{{ $deal->name }}</h1>
        </div>
        <nav aria-label="breadcrumbs">
            <ol class="breadcrumbs align-items-center">
                <li class="breadcrumbs-item">
                    <a href="{{ route($prefix . '.dashboard') }}" class="home-icon"><i class="fas fa-home"
                            title="Dashboard"></i></a>
                </li>
                <li class="breadcrumbs-item" onclick="window.location.href='{{ route($prefix . '.deals.index') }}'">Deals
                </li>
                <li class="breadcrumbs-items">></li>
                <li class="breadcrumbs-item"
                    onclick="window.location.href='{{ route($prefix . '.deals.summary', $deal->id) }}'">
                    {{ $deal->name }}</li>
                <li class="breadcrumbs-items">></li>
                <li class="breadcrumbs-item active" aria-current="page">{{ $kpicollection->kpi_collection_name }}</li>
            </ol>
        </nav>
        <hr>
        <div class="d-flex justify-content-between mt-4 align-items-center">
            <div class="d-flex align-items-center gap-3">
                <button type="button" class="btn btn-outline-primary rounded-lg" onclick="window.history.back();">
                    <i class="fas fa-arrow-left me-1"></i> Back
                </button>
                <h2 class="mb-0 fw-semibold" style="font-size: 24px;">{{ $kpicollection->kpi_collection_name }}</h2>
            </div>
            <div class="d-flex flex-row gap-4">
                @if (count($kpicollection->kpis) > 0)
                    <button class="btn btn-outline-primary" id="add-date">Add Date</button>
                @endif
                <button class="btn btn-outline-primary" id="add-kpi" data-bs-toggle="modal" data-bs-target="#addKPIModal">
                    + Add Kpi
                </button>
            </div>
        </div>
        <style>
            .deal-modal.right .modal-dialog {
                position: fixed;
                margin: auto;
                width: 50rem;
                max-width: 50%;
                height: 100%;
                right: 0;
                top: 0;
                bottom: 0;
            }

            .deal-modal.right .modal-content {
                height: 100%;
                overflow-y: auto;
            }

            .deal-modal.right .modal-body {
                padding: 15px 15px 80px;
            }

            /* Style for addKPINameModal */
            #addKPINameModal .modal-dialog {
                max-width: 600px;
                z-index: 1051;
            }

            #addKPINameModal .modal-content {
                overflow-y: auto;
            }

            #addKPINameModal .modal-body {
                padding: 15px;
            }

            /* Blur effect for addKPIModal when addKPINameModal is open */
            #addKPIModal.blurred .modal-content {
                filter: blur(3px);
                transition: filter 0.3s ease;
            }

            /* Ensure addKPIModal content is not interactive when blurred */
            #addKPIModal.blurred {
                pointer-events: none;
            }
        </style>
        @if (count($kpicollection->kpis) == 0)
            <table class="table table-bordered mt-3" id="metrics-table">
                <tr>
                    <td colspan="{{ count($dates) + 2 }}" class="text-center text-muted">
                        There are no KPIs yet. Please create a KPI first.
                    </td>
                </tr>
                </tr>
            </table>
        @endif
        @if (count($kpicollection->kpis) > 0)
            <table class="table table-bordered mt-3" id="metrics-table">
                <thead class="table-light">
                    <tr>
                        <th>KPI</th>
                        @foreach ($dates as $date)
                            @if ($kpicollection->kpi_collection_type === 'yearly')
                                <th>{{ $date->format('Y') }}</th>
                            @elseif ($kpicollection->kpi_collection_type === 'monthly')
                                <th>{{ $date->format('Y-m') }}</th>
                            @elseif ($kpicollection->kpi_collection_type === 'daily')
                                <th>{{ $date->format('Y-m-d') }}</th>
                            @endif
                        @endforeach
                        <th>Actions</th>
                    </tr>

                </thead>

                <tbody id="metricTable">
                    @foreach ($kpicollection->kpis as $kpi)
                        @if ($kpi !== '')
                            <tr>
                                <td>{{ $kpi->kpiName->kpi_name }}</td>
                                @foreach ($dates as $date)
                                    <td>
                                        @php
                                            $values = json_decode($kpi->kpi_values->first()->values ?? '[]', true);
                                            $profit =
                                                collect($values)->firstWhere('date', $date->format('Y-m-d'))[
                                                    'profit'
                                                ] ?? '';
                                            $formattedProfit = $profit;

                                            switch ($kpi->kpi_collection_format) {
                                                case 'currency_usd':
                                                    $formattedProfit = '$' . number_format((float) $profit, 2);
                                                    break;
                                                case 'currency_other':
                                                    $formattedProfit = '¤' . number_format((float) $profit, 2);
                                                    break;
                                                case 'percentage':
                                                    $formattedProfit = number_format((float) $profit, 2) . '%';
                                                    break;
                                                case 'number':
                                                default:
                                                    $formattedProfit = number_format((float) $profit, 2);
                                                    break;
                                            }
                                        @endphp

                                        <input type="text" class="form-control kpi-value-input"
                                            data-kpi-id="{{ $kpi->id }}" data-date="{{ $date->format('Y-m-d') }}"
                                            data-format="{{ $kpi->kpi_collection_format }}" value="{{ $formattedProfit }}"
                                            x-on:input="
                                        if ($el.dataset.format === 'currency_usd' || $el.dataset.format === 'currency_other') {
                                            moneyFormat($el);
                                        } else if ($el.dataset.format === 'percentage') {
                                            percentFormat($el);
                                        }
                                    ">
                                    </td>
                                @endforeach

                                <td>
                                    <i class="fas fa-trash" role="button" title="Delete KPI"
                                        @click="deleteKpi({{ $kpi->id }})"></i>
                                </td>
                            </tr>
                        @endif
                    @endforeach
                </tbody>
            </table>
            <div class="d-flex justify-content-end mt-3">
                <button class="btn btn-primary" @click="saveKpiValues()">Save KPI Values</button>
            </div>
        @endif
        <!-- Add KPI Modal -->
        <div class="deal-modal modal right fade" id="addKPIModal" tabindex="-1" aria-labelledby="addKPIModalLabel"
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content px-2">
                    <div class="modal-header row bg-primary text-white" style="height:80px;">
                        <h5 class="modal-title col text-white">Add KPI Collection</h5>
                        <button type="button" class="btn-close text-white" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div id="addKPIForm">
                            @csrf
                            <p>Add a KPI collection to contain a set of KPIs</p>
                            <div class="mb-3">
                                <label for="kpi_name" class="form-label">KPI Collection Name</label>
                                <select id="kpi_name" name="kpi_name" class="form-select" @change="changeKpiName()"
                                    x-model="kpiForm.kpi_name">
                                    <option value="">Select or add name</option>
                                    <!-- Dynamically populated options -->
                                    <template x-for="name in kpiNames" :key="name.id">
                                        <option :value="name.id" x-text="name.kpi_name"></option>
                                    </template>
                                    {{--  @foreach ($kpicollection->kpi_names as $kpiName)
                                        <option value="{{ $kpiName->id }}">{{ $kpiName->kpi_name }}</option>
                                    @endforeach  --}}
                                    <option value="add_name">Add Name</option>
                                </select>
                            </div>
                            <div>
                                <label for="kpi_collection_format" class="form-label">KPI Collection Format</label>
                                <select id="kpi_collection_format" name="kpi_collection_format" class="form-select"
                                    x-model="kpiForm.kpi_collection_format">
                                    <option value="">Select KPI Format</option>
                                    <option value="number">Number</option>
                                    <option value="currency_usd">Currency-USD</option>
                                    <option value="currency_other">Currency-Other</option>
                                    <option value="percentage">Percentage</option>
                                </select>
                            </div>
                            <div class="d-flex pt-5 justify-content-end">
                                <button type="button" class="btn btn-secondary me-2"
                                    data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary deal-save"
                                    @click="submitKPIForm(kpiForm)">Save</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add KPI Name Modal -->
        <div class="deal-modal modal right fade" id="addKPINameModal" tabindex="-1"
            aria-labelledby="addKPINameModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-sm"> <!-- Smaller width with modal-sm -->
                <div class="modal-content px-2">
                    <div class="modal-header row bg-primary text-white" style="height:80px;">
                        <h5 class="modal-title col text-white">Add KPI Name</h5>
                        <button type="button" class="btn-close text-white" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div id="addKPINameForm">
                            @csrf
                            <div class="mb-3">
                                <label for="kpi_name" class="form-label">KPI Collection Name</label>
                                <input type="text" id="kpi_name" name="kpi_name" class="form-control"
                                    x-model="newKpiName" placeholder="Enter new KPI name">
                            </div>
                            <div class="d-flex pt-5 justify-content-end">
                                <button type="button" class="btn btn-secondary me-2"
                                    data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary" @click="submitNewKPIName()">Save</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('script')
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/plugins/monthSelect/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.10.0/js/bootstrap-datepicker.min.js">
    </script>

    <script>
        function KPIFormDetail() {
            return {
                ...alpineHelpers(),
                kpiForm: {
                    _token: '{{ csrf_token() }}',
                    kpis_collection_id: "{{ $kpicollection->id }}",
                    kpi_name: '',
                    kpi_collection_format: '',
                },
                kpiNames: JSON.parse('{!! $kpicollection->kpi_names !!}'),
                newKpiName: '',
                errors: {},
                loading: false,
                collectionType: "{{ $kpicollection->kpi_collection_type }}",

                init() {
                    // Remove any existing temp input
                    const existingInput = document.getElementById('temp-date-input');
                    if (existingInput) existingInput.remove();

                    const button = document.getElementById('add-date');

                    // Create hidden input for datepicker
                    const input = document.createElement('input');
                    input.type = 'text';
                    input.id = 'temp-date-input';
                    input.style.position = 'absolute';
                    input.style.zIndex = 9999;
                    input.style.opacity = 0; // hide the input
                    document.body.appendChild(input);

                    const rect = button.getBoundingClientRect();
                    input.style.top = `${rect.bottom + window.scrollY}px`;
                    input.style.left = `${rect.left + window.scrollX}px`;

                    if (input._flatpickr) input._flatpickr.destroy();
                    $(input).datepicker?.('destroy');

                    if (this.collectionType === 'yearly') {
                        $(input).datepicker({
                            format: "yyyy",
                            minViewMode: "years",
                            autoclose: true
                        }).on('changeDate', (e) => {
                            const year = e.date.getFullYear();
                            const dateStr = `${year}-01-01`;

                            if (this.validateDate(dateStr)) {
                                this.addDateColumn(dateStr);
                            } else {
                                Swal.fire('Error', 'Invalid date for collection type: ' + this.collectionType,
                                    'error');
                            }

                            input.remove(); // cleanup
                        });

                        // Only open picker on button click (not auto)
                        button.addEventListener('click', () => {
                            $(input).datepicker('show');
                        }, {
                            once: true
                        }); // so it doesn't attach multiple times

                    } else {
                        let flatpickrConfig = {
                            dateFormat: 'Y-m-d',
                            onChange: (selectedDates, dateStr) => {
                                if (this.validateDate(dateStr)) {
                                    this.addDateColumn(dateStr);
                                } else {
                                    Swal.fire('Error', 'Invalid date for collection type: ' + this.collectionType,
                                        'error');
                                }
                                input.remove(); // cleanup
                            },
                        };

                        if (this.collectionType === 'monthly') {
                            flatpickrConfig.plugins = [
                                new monthSelectPlugin({
                                    shorthand: false,
                                    dateFormat: 'Y-m-01',
                                    altFormat: 'F Y',
                                }),
                            ];
                            flatpickrConfig.dateFormat = 'Y-m-01';
                        }

                        const picker = flatpickr(input, flatpickrConfig);

                        // Open only on button click
                        button.addEventListener('click', () => {
                            picker.open();
                        }, {
                            once: true
                        });
                    }
                },


                validateDate(dateStr) {
                    const date = new Date(dateStr);
                    if (isNaN(date)) return false;

                    switch (this.collectionType) {
                        case 'yearly':
                            return date.getMonth() === 0 && date.getDate() === 1; // Jan 1st
                        case 'monthly':
                            return date.getDate() === 1; // First of month
                        case 'daily':
                            return true; // Any date
                        default:
                            return false;
                    }
                },

                async addDateColumn(dateStr) {
                    this.loading = true;
                    try {
                        const response = await fetch("{{ route($prefix . '.kpi.addDate', $kpicollection->id) }}", {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                date: dateStr
                            }),
                        });

                        this.loading = false;

                        if (response.status === 200) {
                            Swal.fire('Success', 'Date added successfully', 'success');
                            window.location.reload();
                        } else {
                            const responseData = await response.json();
                            Swal.fire('Error', responseData.message || 'Error adding date', 'error');
                        }
                    } catch (error) {
                        this.loading = false;
                        console.error('Error:', error);
                        Swal.fire('Error', 'An error occurred while adding the date', 'error');
                    }
                },

                async saveKpiValues() {
                    this.loading = true;

                    const stripFormattedValue = (value, format) => {
                        if (!value) return null;

                        switch (format) {
                            case 'currency_usd':
                            case 'currency_other':
                                return value.replace(/[^0-9.]/g, ''); // Remove $, ¤, commas
                            case 'percentage':
                                return value.replace('%', '').trim(); // Remove %
                            case 'number':
                            default:
                                return value.replace(/,/g, ''); // Just remove commas
                        }
                    };

                    const values = [];
                    document.querySelectorAll('.kpi-value-input').forEach(input => {
                        const format = input.dataset.format;
                        const rawValue = input.value;
                        const cleanValue = stripFormattedValue(rawValue, format);

                        values.push({
                            kpi_id: input.dataset.kpiId,
                            date: input.dataset.date,
                            profit: cleanValue !== '' ? parseFloat(cleanValue) : null,
                        });
                    });


                    try {
                        const response = await fetch(
                            "{{ route($prefix . '.kpi.storeKpiValue', $kpicollection->id) }}", {
                                method: 'POST',
                                headers: {
                                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    values
                                }),
                            });

                        this.loading = false;

                        if (response.status === 200) {
                            Swal.fire('Success', 'KPI values saved successfully', 'success');
                        } else {
                            const responseData = await response.json();
                            Swal.fire('Error', responseData.message || 'Error saving KPI values', 'error');
                        }
                    } catch (error) {
                        this.loading = false;
                        console.error('Error:', error);
                        Swal.fire('Error', 'An error occurred while saving KPI values', 'error');
                    }
                },

                async deleteKpi(kpiId) {
                    if (!confirm('Are you sure you want to delete this KPI?')) return;
                    this.loading = true;
                    try {
                        const response = await fetch(
                            `{{ route($prefix . '.kpi.deleteKpi', 'KPI_ID_PLACEHOLDER') }}`.replace(
                                'KPI_ID_PLACEHOLDER', kpiId), {
                                method: 'DELETE',
                                headers: {
                                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                                },
                            });

                        this.loading = false;
                        if (response.status === 200) {
                            window.location.reload();
                        } else {
                            Swal.fire('Error', 'Error deleting KPI', 'error');
                        }
                    } catch (error) {
                        this.loading = false;
                        console.error('Error:', error);
                        Swal.fire('Error', 'An error occurred while deleting the KPI', 'error');
                    }
                },

                changeKpiName() {
                    if (this.kpiForm.kpi_name === 'add_name') {
                        $('#addKPIModal').addClass('blurred');
                        $('#addKPINameModal').modal('show');
                        this.kpiForm.kpi_name = '';
                        $('#addKPINameModal').off('hidden.bs.modal').on('hidden.bs.modal', function() {
                            $('#addKPIModal').removeClass('blurred');
                        });
                    }
                },

                async submitNewKPIName() {
                    if (!this.newKpiName.trim()) {
                        Swal.fire('Error', 'Please enter a KPI name', 'error');
                        return;
                    }

                    this.loading = true;
                    try {
                        let url = "{{ route($prefix . '.kpi.storeName') }}";
                        const response = await fetch(url, {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                _token: '{{ csrf_token() }}',
                                kpis_collection_id: "{{ $kpicollection->id }}",
                                kpi_name: this.newKpiName
                            })
                        });

                        this.loading = false;

                        if (response.status === 422) {
                            const responseData = await response.json();
                            this.errors = responseData.errors;
                            Swal.fire('Error', 'Validation failed', 'error');
                            return;
                        }

                        if (response.status === 200) {
                            let res = await response.json();
                            this.kpiNames.push(res.kpi);
                            $('#addKPINameModal').modal('hide');
                            Swal.fire('Success', 'KPI name saved successfully', 'success');
                        } else {
                            const responseData = await response.json();
                            Swal.fire('Error', responseData.message || 'Error saving KPI name', 'error');
                        }
                    } catch (error) {
                        this.loading = false;
                        console.error('Error:', error);
                        Swal.fire('Error', 'An error occurred while saving the KPI name', 'error');
                    }
                },

                async submitKPIForm(data) {
                    this.loading = true;
                    let url = "{{ route($prefix . '.kpi.storeKpi', $kpicollection->id) }}";

                    try {
                        let formData = new FormData();
                        for (const key in data) {
                            if (data.hasOwnProperty(key)) {
                                formData.append(key, data[key]);
                            }
                        }

                        const response = await fetch(url, {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            body: formData
                        });

                        this.loading = false;

                        if (response.status === 422) {
                            const responseData = await response.json();
                            this.errors = responseData.errors;
                            Swal.fire('Error', 'Validation failed', 'error');
                            return;
                        }

                        const responseData = await response.json();
                        if (response.status === 200) {
                            const modalElement = document.querySelector('#addKPIModal');
                            const modalInstance = bootstrap.Modal.getInstance(modalElement);
                            modalInstance.hide();
                            window.location.reload();
                            Swal.fire('Success', 'KPI saved successfully', 'success');
                        } else {
                            Swal.fire('Error', responseData.message || 'Error saving KPI', 'error');
                        }
                    } catch (error) {
                        this.loading = false;
                        console.error('Error:', error);
                        Swal.fire('Error', 'An error occurred while saving the KPI', 'error');
                    }
                }
            }
        }
    </script>
    <script>
        function alpineHelpers() {
            return {
                moneyFormat(el) {
                    let value = el.value;
                    // Remove non-numeric characters except for the decimal point
                    value = value.replace(/[^\d.]/g, '');

                    // Remove leading zeros
                    value = value.replace(/^0+(?=\d)/, '');
                    // If there's more than one decimal point, keep only the first one
                    const parts = value.split('.');
                    if (parts.length > 2) {
                        value = parts[0] + '.' + parts.slice(1).join('');
                    }
                    // Limit the decimal part to two digits
                    if (parts[1]) {
                        parts[1] = parts[1].slice(0, 2);
                        value = parts.join('.');
                    }
                    // Add commas for thousands separator
                    value = value.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                    // Ensure that the value starts with $ and no other non-numeric characters
                    if (value !== '') {
                        el.value = '$' + value;
                    } else {
                        el.value = '$0';
                    }
                },
                percentFormat(el) {
                    let value = el.value;
                    // Remove non-numeric characters except for the decimal point
                    value = value.replace(/[^\d.]/g, '');

                    // Remove leading zeros
                    value = value.replace(/^0+(?=\d)/, '');
                    // If there's more than one decimal point, keep only the first one
                    const parts = value.split('.');
                    if (parts.length > 2) {
                        value = parts[0] + '.' + parts.slice(1).join('');
                    }
                    // Limit the decimal part to two digits
                    if (parts[1]) {
                        parts[1] = parts[1].slice(0, 2);
                        value = parts.join('.');
                    }
                    // Ensure that the value ends with % and no other non-numeric characters
                    if (value !== '') {
                        el.value = value + '%';
                    } else {
                        el.value = '0%';
                    }
                    el.setSelectionRange(el.value.length - 1, el.value.length - 1);
                }
            }
        }
    </script>
@endpush
