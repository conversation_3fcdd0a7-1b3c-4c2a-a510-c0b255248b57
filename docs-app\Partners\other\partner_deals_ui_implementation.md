# Partner Deals Section: Complete Mirroring Implementation

## Overview

This document outlines the comprehensive implementation strategy for mirroring the Admin Deals section in the Partner interface of the CREI Investment Platform. This includes not only UI components but also controllers, models, routes, permissions, and business logic to ensure a consistent experience while maintaining appropriate access controls.

## UI Components

### 1. Header Section

#### 1.1 Page Title and Add Deal Button
```html
<div class="d-flex justify-content-between pb-4">
    <h1>Deal List</h1>
    <button type="button" class="btn btn_primary" onclick="window.location.href='{{ route("partner.deals.create") }}'">
        Add Deal <span class="ms-4 text-white fw-bold">+</span>
    </button>
</div>
```

#### 1.2 Deal Statistics
```html
<div class="row mb-4">
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card bg-light">
            <div class="card-body">
                <h5 class="card-title">Total Deals</h5>
                <p class="card-text fs-4">{{ $totalDeals }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <h5 class="card-title">Active Deals</h5>
                <p class="card-text fs-4">{{ $activeDeals }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card bg-warning">
            <div class="card-body">
                <h5 class="card-title">Pending Deals</h5>
                <p class="card-text fs-4">{{ $pendingDeals }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card bg-secondary text-white">
            <div class="card-body">
                <h5 class="card-title">Closed Deals</h5>
                <p class="card-text fs-4">{{ $closedDeals }}</p>
            </div>
        </div>
    </div>
</div>
```

### 2. Table View (Desktop)

#### 2.1 Table Structure
```html
<table class="table custom-data-table">
    <thead>
        <tr>
            <th>
                <a href="{{ route('partner.deals.index', ['sort' => 'name', 'direction' => request('sort') == 'name' && request('direction') == 'asc' ? 'desc' : 'asc']) }}">
                    @lang('Deal name')
                    <i class="las {{ request('sort') == 'name' ? (request('direction') == 'asc' ? 'la-arrow-up' : 'la-arrow-down') : 'la-arrows-alt-v' }}"></i>
                </a>
            </th>
            <th>
                <a href="{{ route('partner.deals.index', ['sort' => 'deal_stage', 'direction' => request('sort') == 'deal_stage' && request('direction') == 'asc' ? 'desc' : 'asc']) }}">
                    @lang('Deal stage')
                    <i class="las {{ request('sort') == 'deal_stage' ? (request('direction') == 'asc' ? 'la-arrow-up' : 'la-arrow-down') : 'la-arrows-alt-v' }}"></i>
                </a>
            </th>
            <th>
                <a href="{{ route('partner.deals.index', ['sort' => 'total_in_progress', 'direction' => request('sort') == 'total_in_progress' && request('direction') == 'asc' ? 'desc' : 'asc']) }}">
                    @lang('Total in progress')
                    <i class="las {{ request('sort') == 'total_in_progress' ? (request('direction') == 'asc' ? 'la-arrow-up' : 'la-arrow-down') : 'la-arrows-alt-v' }}"></i>
                </a>
            </th>
            <th>
                <a href="{{ route('partner.deals.index', ['sort' => 'total_accepted', 'direction' => request('sort') == 'total_accepted' && request('direction') == 'asc' ? 'desc' : 'asc']) }}">
                    @lang('Total accepted')
                    <i class="las {{ request('sort') == 'total_accepted' ? (request('direction') == 'asc' ? 'la-arrow-up' : 'la-arrow-down') : 'la-arrows-alt-v' }}"></i>
                </a>
            </th>
            <th>
                <a href="{{ route('partner.deals.index', ['sort' => 'raise_target', 'direction' => request('sort') == 'raise_target' && request('direction') == 'asc' ? 'desc' : 'asc']) }}">
                    @lang('Raise target')
                    <i class="las {{ request('sort') == 'raise_target' ? (request('direction') == 'asc' ? 'la-arrow-up' : 'la-arrow-down') : 'la-arrows-alt-v' }}"></i>
                </a>
            </th>
            <th>
                <a href="{{ route('partner.deals.index', ['sort' => 'distributions', 'direction' => request('sort') == 'distributions' && request('direction') == 'asc' ? 'desc' : 'asc']) }}">
                    @lang('Distributions')
                    <i class="las {{ request('sort') == 'distributions' ? (request('direction') == 'asc' ? 'la-arrow-up' : 'la-arrow-down') : 'la-arrows-alt-v' }}"></i>
                </a>
            </th>
            <th>
                <a href="{{ route('partner.deals.index', ['sort' => 'investors', 'direction' => request('sort') == 'investors' && request('direction') == 'asc' ? 'desc' : 'asc']) }}">
                    @lang('Investors')
                    <i class="las {{ request('sort') == 'investors' ? (request('direction') == 'asc' ? 'la-arrow-up' : 'la-arrow-down') : 'la-arrows-alt-v' }}"></i>
                </a>
            </th>
            <th>
                <a href="{{ route('partner.deals.index', ['sort' => 'close_date', 'direction' => request('sort') == 'close_date' && request('direction') == 'asc' ? 'desc' : 'asc']) }}">
                    @lang('Close date')
                    <i class="las {{ request('sort') == 'close_date' ? (request('direction') == 'asc' ? 'la-arrow-up' : 'la-arrow-down') : 'la-arrows-alt-v' }}"></i>
                </a>
            </th>
            <th>@lang('Action')</th>
        </tr>
    </thead>
    <tbody>
        @forelse($deals as $deal)
            <tr>
                <td><a href="{{ route('partner.deals.summary', $deal->id) }}">{{ $deal->name }}</a></td>
                <td>{{ $deal->deal_stage }}</td>
                <td>${{ number_format($deal->total_in_progress ?? 0, 2) }} ({{ number_format($deal->progress_percentage ?? 0, 2) }}%)</td>
                <td>{{ $deal->total_accepted ?? 0 }}</td>
                <td>${{ number_format($deal->raise_target ?? 0, 0) }}</td>
                <td>
                    @if(isset($deal->distributions) && count($deal->distributions) > 0)
                        @foreach ($deal->distributions as $distribution)
                            <div>
                                {{ $distribution->amount }}<br>
                            </div>
                        @endforeach
                    @else
                        N/A
                    @endif
                </td>
                <td>{{ $deal->investors_count ?? 'N/A' }}</td>
                <td>{{ isset($deal->close_date) ? showDateTime($deal->close_date) : 'N/A' }}</td>
                <td>
                    <div class="dropdown">
                        <button class="btn p-0" type="button" data-bs-toggle="dropdown">
                            <i class="las la-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="{{ route('partner.deals.summary', $deal->id) }}">
                                    <i class="las la-eye me-2"></i> View
                                </a>
                            </li>
                            <li>
                                <button class="dropdown-item text-danger" data-bs-toggle="modal" data-bs-target="#deleteConfirmModal" onclick="setDeleteData({{ $deal->id }}, '{{ $deal->name }}')">
                                    <i class="las la-trash me-2"></i> Delete
                                </button>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        @empty
            <tr>
                <td colspan="10" class="text-center">No deals found</td>
            </tr>
        @endforelse
    </tbody>
</table>
```

### 3. Mobile View

#### 3.1 Mobile Card Structure
```html
<div class="mobile-list">
    @forelse($deals as $deal)
        <div class="mobile-deal-item">
            <div class="mobile-deal-header">
                <div class="mobile-deal-name">
                    <a href="{{ route('partner.deals.summary', $deal->id) }}">{{ $deal->name }}</a>
                </div>
                <div class="mobile-deal-status">
                    {{ $deal->deal_stage }}
                </div>
            </div>
            <div class="mobile-deal-details">
                <div class="mobile-deal-info">
                    <div class="info-item">
                        <span class="info-label">Total in progress:</span>
                        <span class="info-value">${{ number_format($deal->total_in_progress ?? 0, 2) }} ({{ number_format($deal->progress_percentage ?? 0, 2) }}%)</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Total accepted:</span>
                        <span class="info-value">{{ $deal->total_accepted ?? 0 }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Raise target:</span>
                        <span class="info-value">${{ number_format($deal->raise_target ?? 0, 0) }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Distributions:</span>
                        <span class="info-value">
                            @if(isset($deal->distributions) && count($deal->distributions) > 0)
                                @foreach ($deal->distributions as $distribution)
                                    {{ $distribution->amount }}<br>
                                @endforeach
                            @else
                                N/A
                            @endif
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Investors:</span>
                        <span class="info-value">{{ $deal->investors_count ?? 'N/A' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Close date:</span>
                        <span class="info-value">{{ isset($deal->close_date) ? showDateTime($deal->close_date) : 'N/A' }}</span>
                    </div>
                </div>
                <div class="mobile-deal-actions">
                    <div class="button-group">
                        <a href="{{ route('partner.deals.summary', $deal->id) }}" class="btn btn-sm btn-outline-primary">
                            <i class="las la-eye"></i>
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteConfirmModal" onclick="setDeleteData({{ $deal->id }}, '{{ $deal->name }}')">
                            <i class="las la-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @empty
        <div class="alert alert-warning">No deals found</div>
    @endforelse
</div>
```

### 4. Modals

#### 4.1 Delete Confirmation Modal
```html
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@lang('Delete Confirmation')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="deleteQuestion">@lang('Are you sure you want to delete this deal?')</p>
            </div>
            <div class="modal-footer">
                <form action="" method="POST" id="deleteForm">
                    @csrf
                    @method('DELETE')
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('Cancel')</button>
                    <button type="submit" class="btn btn-danger">@lang('Delete')</button>
                </form>
            </div>
        </div>
    </div>
</div>
```

#### 4.2 Add Deal Modal
```html
<div class="deal-modal modal right fade" id="addDealModal" tabindex="-1" aria-labelledby="addDealModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content px-2">
            <div class="modal-header row">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                <div class="stepper-wrapper">
                    <div class="stepper-item" :class="{ 'active': currentStep === 'deal', 'completed': completedSteps.includes('deal') }">
                        <div class="step-counter">1</div>
                        <div class="step-name">Deal</div>
                    </div>
                    <div class="stepper-item" :class="{ 'active': currentStep === 'assets', 'completed': completedSteps.includes('assets') }">
                        <div class="step-counter">2</div>
                        <div class="step-name">Assets</div>
                    </div>
                </div>
            </div>
            <div class="modal-body">
                <!-- Deal Form Body -->
                <!-- Form fields for deal creation -->
            </div>
        </div>
    </div>
</div>
```

## CSS Styling

### 1. Button Styles
```css
.btn_primary {
    background-color: blue;
    border-radius: 4px;
    padding: 0px;
    height: 40px;
    width: 200px;
    display: flex;
    justify-content: center;
    float: right;
    color: white;
    font-weight: bold;
    border: none;
    padding: 10px 16px;
    cursor: pointer;
}

.btn_primary:hover {
    color: white;
    border-radius: 4px;
    padding: 0px;
    height: 40px;
    width: 200px;
    display: flex;
    float: right;
    background-color: #69A2FF;
    font-weight: bold;
    border: none;
    padding: 10px 16px;
    cursor: pointer;
    border-radius: 4px;
    transition: 0.3sec;
}
```

### 2. Table Sorting Styles
```css
.table th a {
    color: inherit;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
}

.table th a i {
    margin-left: 5px;
    font-size: 14px;
}
```

### 3. Mobile View Styles
```css
.mobile-list {
    display: none;
}

@media (max-width: 767px) {
    .table-responsive {
        display: none;
    }
    
    .mobile-list {
        display: block;
    }
    
    .mobile-deal-item {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 15px;
        padding: 15px;
    }
    
    .mobile-deal-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }
    
    .mobile-deal-name .user {
        display: flex;
        align-items: center;
    }
    
    .mobile-deal-name .user .thumb {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 10px;
    }
    
    .mobile-deal-name .user .thumb img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .mobile-deal-details {
        display: flex;
        flex-direction: column;
    }
    
    .mobile-deal-info {
        margin-bottom: 15px;
    }
    
    .info-item {
        margin-bottom: 5px;
    }
    
    .info-label {
        font-weight: bold;
        margin-right: 5px;
    }
    
    .mobile-deal-actions {
        display: flex;
        justify-content: flex-end;
    }
}
```

## JavaScript Functionality

### 1. Alpine.js Components

#### 1.1 Dashboard Component
```javascript
function dashboard() {
    return {
        currentStep: 'deal',
        completedSteps: [],
        errors: {},
        loading: false,
        dealForm: {
            name: '',
            type: '',
            description: '',
            image: null,
            deal_stage: 'pending',
            close_date: null
        },
        dealErrors: {},
        assetForm: {
            name: '',
            property_type: '',
            type_of_units: 'Square Feet',
            number_of_units: '',
            address: '',
            city: '',
            state: '',
            zip_code: '',
            country: '',
            images: []
        },
        assetErrors: {},
        assetList: false,
        assets: [],
        selectedAsset: null,
        deleteId: null,
        
        setDeleteId(id) {
            this.deleteId = id;
            document.getElementById('deleteForm').action = `/partner/deals/${id}`;
        },
        
        nextStep() {
            if (this.currentStep === 'deal') {
                this.validateDealForm();
                if (Object.keys(this.dealErrors).length === 0) {
                    this.completedSteps.push('deal');
                    this.currentStep = 'assets';
                }
            }
        },
        
        prevStep() {
            if (this.currentStep === 'assets') {
                this.currentStep = 'deal';
            }
        },
        
        validateDealForm() {
            this.dealErrors = {};
            if (!this.dealForm.name) {
                this.dealErrors.name = 'Deal name is required';
            }
            if (!this.dealForm.type) {
                this.dealErrors.type = 'Deal type is required';
            }
            if (!this.dealForm.description) {
                this.dealErrors.description = 'Description is required';
            }
        },
        
        validateAssetForm() {
            this.assetErrors = {};
            if (!this.assetForm.name) {
                this.assetErrors.name = 'Property name is required';
            }
            if (!this.assetForm.property_type) {
                this.assetErrors.property_type = 'Property type is required';
            }
            if (!this.assetForm.number_of_units) {
                this.assetErrors.number_of_units = 'Number of units is required';
            }
            if (!this.assetForm.address) {
                this.assetErrors.address = 'Address is required';
            }
            if (!this.assetForm.city) {
                this.assetErrors.city = 'City is required';
            }
            if (!this.assetForm.state) {
                this.assetErrors.state = 'State is required';
            }
            if (!this.assetForm.zip_code) {
                this.assetErrors.zip_code = 'Zip code is required';
            }
            if (!this.assetForm.country) {
                this.assetErrors.country = 'Country is required';
            }
        },
        
        handleFiles(event) {
            const files = event.target.files;
            this.assetForm.images = [];
            for (let i = 0; i < files.length; i++) {
                this.assetForm.images.push(files[i]);
            }
        },
        
        async submitDealForm() {
            this.loading = true;
            const formData = new FormData();
            
            // Append deal form data
            Object.keys(this.dealForm).forEach(key => {
                formData.append(key, this.dealForm[key]);
            });
            
            try {
                const response = await fetch('/partner/deals', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // Handle success
                    window.location.href = `/partner/deals/${data.deal.id}`;
                } else {
                    // Handle validation errors
                    this.dealErrors = data.errors || {};
                }
            } catch (error) {
                console.error('Error:', error);
            } finally {
                this.loading = false;
            }
        }
    };
}
```

## Controller Implementation

### 1. Partner DealController

The `Partner\DealController` needs to mirror the functionality of the `Admin\DealController` while enforcing partner-specific access controls:

```php
namespace App\Http\Controllers\Partner;

use App\Http\Controllers\Controller;
use App\Models\Deal;
use App\Models\PartnerDeal;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DealController extends Controller
{
    /**
     * Display a listing of the deals.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $admin = Auth::guard('admin')->user();
        
        // Get deals associated with the partner
        $partnerDeals = PartnerDeal::where('admin_id', $admin->id)->pluck('deal_id');
        
        $deals = Deal::whereIn('id', $partnerDeals)
            ->with(['investments'])
            ->withCount('investments');
        
        // Apply sorting
        if ($request->has('sort')) {
            $direction = $request->input('direction', 'asc');
            $column = $request->input('sort');
            $deals->orderBy($column, $direction);
        } else {
            $deals->latest();
        }
        
        $deals = $deals->paginate(getPaginate());
        
        // Calculate deal statistics
        $totalDeals = $deals->total();
        $activeDeals = Deal::whereIn('id', $partnerDeals)->where('deal_stage', 'active')->count();
        $pendingDeals = Deal::whereIn('id', $partnerDeals)->where('deal_stage', 'pending')->count();
        $closedDeals = Deal::whereIn('id', $partnerDeals)->where('deal_stage', 'closed')->count();
        
        return view('partner.deals.index', compact('deals', 'totalDeals', 'activeDeals', 'pendingDeals', 'closedDeals'));
    }
    
    /**
     * Show the form for creating a new deal.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('partner.deals.create');
    }
    
    /**
     * Store a newly created deal in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|string|max:255',
            'description' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'deal_stage' => 'required|in:active,pending,closed',
            'close_date' => 'nullable|date',
        ]);
        
        // Create the deal
        $deal = new Deal();
        $deal->name = $request->name;
        $deal->type = $request->type;
        $deal->description = $request->description;
        $deal->deal_stage = $request->deal_stage;
        $deal->close_date = $request->close_date;
        
        // Handle image upload
        if ($request->hasFile('image')) {
            $path = getFilePath('deal');
            $size = getFileSize('deal');
            $filename = uploadImage($request->image, $path, $size);
            $deal->image = $filename;
        }
        
        $deal->save();
        
        // Associate the deal with the partner
        $admin = Auth::guard('admin')->user();
        $partnerDeal = new PartnerDeal();
        $partnerDeal->admin_id = $admin->id;
        $partnerDeal->deal_id = $deal->id;
        $partnerDeal->save();
        
        return redirect()->route('partner.deals.show', $deal->id)->with('success', 'Deal created successfully');
    }
    
    /**
     * Display the specified deal.
     *
     * @param  \App\Models\Deal  $deal
     * @return \Illuminate\Http\Response
     */
    public function show(Deal $deal)
    {
        // Check if the partner has access to this deal
        $admin = Auth::guard('admin')->user();
        $partnerDeal = PartnerDeal::where('admin_id', $admin->id)
            ->where('deal_id', $deal->id)
            ->first();
        
        if (!$partnerDeal) {
            abort(403, 'Unauthorized access');
        }
        
        $deal->load(['assets', 'offerings', 'classes']);
        
        return view('partner.deals.show', compact('deal'));
    }
    
    /**
     * Show the form for editing the specified deal.
     *
     * @param  \App\Models\Deal  $deal
     * @return \Illuminate\Http\Response
     */
    public function edit(Deal $deal)
    {
        // Check if the partner has access to this deal
        $admin = Auth::guard('admin')->user();
        $partnerDeal = PartnerDeal::where('admin_id', $admin->id)
            ->where('deal_id', $deal->id)
            ->first();
        
        if (!$partnerDeal) {
            abort(403, 'Unauthorized access');
        }
        
        return view('partner.deals.edit', compact('deal'));
    }
    
    /**
     * Update the specified deal in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Deal  $deal
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Deal $deal)
    {
        // Check if the partner has access to this deal
        $admin = Auth::guard('admin')->user();
        $partnerDeal = PartnerDeal::where('admin_id', $admin->id)
            ->where('deal_id', $deal->id)
            ->first();
        
        if (!$partnerDeal) {
            abort(403, 'Unauthorized access');
        }
        
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|string|max:255',
            'description' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'deal_stage' => 'required|in:active,pending,closed',
            'close_date' => 'nullable|date',
        ]);
        
        $deal->name = $request->name;
        $deal->type = $request->type;
        $deal->description = $request->description;
        $deal->deal_stage = $request->deal_stage;
        $deal->close_date = $request->close_date;
        
        // Handle image upload
        if ($request->hasFile('image')) {
            $path = getFilePath('deal');
            $size = getFileSize('deal');
            $filename = uploadImage($request->image, $path, $size);
            $deal->image = $filename;
        }
        
        $deal->save();
        
        return redirect()->route('partner.deals.show', $deal->id)->with('success', 'Deal updated successfully');
    }
    
    /**
     * Remove the specified deal from storage.
     *
     * @param  \App\Models\Deal  $deal
     * @return \Illuminate\Http\Response
     */
    public function destroy(Deal $deal)
    {
        // Check if the partner has access to this deal
        $admin = Auth::guard('admin')->user();
        $partnerDeal = PartnerDeal::where('admin_id', $admin->id)
            ->where('deal_id', $deal->id)
            ->first();
        
        if (!$partnerDeal) {
            abort(403, 'Unauthorized access');
        }
        
        // Delete the partner-deal association
        $partnerDeal->delete();
        
        // Note: We don't delete the actual deal, just remove the association
        // This ensures that if multiple partners have access to the same deal,
        // one partner can't delete it for everyone
        
        return redirect()->route('partner.deals.index')->with('success', 'Deal removed successfully');
    }
}
```

## Model Relationships

### 1. PartnerDeal Model

The `PartnerDeal` model represents the relationship between partners and deals:

```php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PartnerDeal extends Model
{
    use HasFactory;
    
    protected $table = "partner_deals";
    
    protected $fillable = [
        "admin_id",
        "deal_id"
    ];
    
    /**
     * Get the admin (partner) associated with the partner deal.
     */
    public function admin()
    {
        return $this->belongsTo(Admin::class);
    }
    
    /**
     * Get the deal associated with the partner deal.
     */
    public function deal()
    {
        return $this->belongsTo(Deal::class);
    }
}
```

### 2. Admin Model Extension

Extend the `Admin` model to include the relationship with deals:

```php
/**
 * Get the deals associated with the partner.
 */
public function deals()
{
    return $this->hasMany(PartnerDeal::class, 'admin_id');
}
```

### 3. Deal Model Extension

Extend the `Deal` model to include the relationship with partners:

```php
/**
 * Get the partners associated with the deal.
 */
public function partners()
{
    return $this->hasMany(PartnerDeal::class, 'deal_id');
}
```

## Route Configuration

### 1. Partner Routes

The partner routes should mirror the admin routes for deals:

```php
// Partner Routes
Route::group(['prefix' => 'partner', 'as' => 'partner.', 'middleware' => ['auth:admin', 'role:partner']], function () {
    // Dashboard
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    // Deals
    Route::resource('deals', DealController::class);
    
    // Deal Assets
    Route::resource('deals.assets', AssetController::class);
    
    // Deal Offerings
    Route::resource('deals.offerings', OfferingController::class);
    
    // Deal Classes
    Route::resource('deals.classes', ClassController::class);
    
    // Deal Investments
    Route::resource('deals.investments', InvestmentController::class);
});
```

## Middleware and Access Control

### 1. Role Middleware

Ensure the `role` middleware is properly configured to restrict access to partner routes:

```php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $role
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $role)
    {
        $user = Auth::guard('admin')->user();
        
        if (!$user || $user->role !== $role) {
            abort(403, 'Unauthorized action.');
        }
        
        return $next($request);
    }
}
```

### 2. Deal Access Middleware

Create a middleware to check if a partner has access to a specific deal:

```php
namespace App\Http\Middleware;

use App\Models\PartnerDeal;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PartnerDealAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::guard('admin')->user();
        $dealId = $request->route('deal');
        
        if ($user->role !== 'partner') {
            return $next($request);
        }
        
        $partnerDeal = PartnerDeal::where('admin_id', $user->id)
            ->where('deal_id', $dealId)
            ->first();
        
        if (!$partnerDeal) {
            abort(403, 'Unauthorized access to this deal.');
        }
        
        return $next($request);
    }
}
```

## Database Schema

### 1. Partner Deals Table

Create a migration for the `partner_deals` table:

```php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('partner_deals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('admin_id')->constrained('admins')->onDelete('cascade');
            $table->foreignId('deal_id')->constrained('deals')->onDelete('cascade');
            $table->timestamps();
            
            // Ensure a partner can't be associated with the same deal multiple times
            $table->unique(['admin_id', 'deal_id']);
        });
    }
    
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('partner_deals');
    }
};
```

## Business Logic Implementation

### 1. Service Layer

Create a `PartnerDealService` to encapsulate business logic related to partner deals:

```php
namespace App\Services;

use App\Models\Admin;
use App\Models\Deal;
use App\Models\PartnerDeal;
use Illuminate\Support\Collection;

class PartnerDealService
{
    /**
     * Get all deals associated with a partner.
     *
     * @param  \App\Models\Admin  $partner
     * @return \Illuminate\Support\Collection
     */
    public function getPartnerDeals(Admin $partner): Collection
    {
        return Deal::whereIn('id', function ($query) use ($partner) {
            $query->select('deal_id')
                ->from('partner_deals')
                ->where('admin_id', $partner->id);
        })->get();
    }
    
    /**
     * Associate a deal with a partner.
     *
     * @param  \App\Models\Admin  $partner
     * @param  \App\Models\Deal  $deal
     * @return \App\Models\PartnerDeal
     */
    public function associateDealWithPartner(Admin $partner, Deal $deal): PartnerDeal
    {
        return PartnerDeal::firstOrCreate([
            'admin_id' => $partner->id,
            'deal_id' => $deal->id,
        ]);
    }
    
    /**
     * Dissociate a deal from a partner.
     *
     * @param  \App\Models\Admin  $partner
     * @param  \App\Models\Deal  $deal
     * @return bool
     */
    public function dissociateDealFromPartner(Admin $partner, Deal $deal): bool
    {
        return PartnerDeal::where('admin_id', $partner->id)
            ->where('deal_id', $deal->id)
            ->delete() > 0;
    }
    
    /**
     * Check if a partner has access to a deal.
     *
     * @param  \App\Models\Admin  $partner
     * @param  \App\Models\Deal  $deal
     * @return bool
     */
    public function hasAccessToDeal(Admin $partner, Deal $deal): bool
    {
        return PartnerDeal::where('admin_id', $partner->id)
            ->where('deal_id', $deal->id)
            ->exists();
    }
}
```

## Testing Strategy

### 1. Unit Tests

Create unit tests for the `PartnerDealService`:

```php
namespace Tests\Unit\Services;

use App\Models\Admin;
use App\Models\Deal;
use App\Models\PartnerDeal;
use App\Services\PartnerDealService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PartnerDealServiceTest extends TestCase
{
    use RefreshDatabase;
    
    private PartnerDealService $service;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new PartnerDealService();
    }
    
    public function test_get_partner_deals()
    {
        $partner = Admin::factory()->create(['role' => 'partner']);
        $deal1 = Deal::factory()->create();
        $deal2 = Deal::factory()->create();
        
        PartnerDeal::factory()->create([
            'admin_id' => $partner->id,
            'deal_id' => $deal1->id,
        ]);
        
        PartnerDeal::factory()->create([
            'admin_id' => $partner->id,
            'deal_id' => $deal2->id,
        ]);
        
        $deals = $this->service->getPartnerDeals($partner);
        
        $this->assertCount(2, $deals);
        $this->assertTrue($deals->contains($deal1));
        $this->assertTrue($deals->contains($deal2));
    }
    
    public function test_associate_deal_with_partner()
    {
        $partner = Admin::factory()->create(['role' => 'partner']);
        $deal = Deal::factory()->create();
        
        $partnerDeal = $this->service->associateDealWithPartner($partner, $deal);
        
        $this->assertInstanceOf(PartnerDeal::class, $partnerDeal);
        $this->assertEquals($partner->id, $partnerDeal->admin_id);
        $this->assertEquals($deal->id, $partnerDeal->deal_id);
    }
    
    public function test_dissociate_deal_from_partner()
    {
        $partner = Admin::factory()->create(['role' => 'partner']);
        $deal = Deal::factory()->create();
        
        PartnerDeal::factory()->create([
            'admin_id' => $partner->id,
            'deal_id' => $deal->id,
        ]);
        
        $result = $this->service->dissociateDealFromPartner($partner, $deal);
        
        $this->assertTrue($result);
        $this->assertDatabaseMissing('partner_deals', [
            'admin_id' => $partner->id,
            'deal_id' => $deal->id,
        ]);
    }
    
    public function test_has_access_to_deal()
    {
        $partner = Admin::factory()->create(['role' => 'partner']);
        $deal = Deal::factory()->create();
        
        PartnerDeal::factory()->create([
            'admin_id' => $partner->id,
            'deal_id' => $deal->id,
        ]);
        
        $result = $this->service->hasAccessToDeal($partner, $deal);
        
        $this->assertTrue($result);
    }
    
    public function test_has_no_access_to_deal()
    {
        $partner = Admin::factory()->create(['role' => 'partner']);
        $deal = Deal::factory()->create();
        
        $result = $this->service->hasAccessToDeal($partner, $deal);
        
        $this->assertFalse($result);
    }
}
```

### 2. Feature Tests

Create feature tests for the `Partner\DealController`:

```php
namespace Tests\Feature\Partner;

use App\Models\Admin;
use App\Models\Deal;
use App\Models\PartnerDeal;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DealControllerTest extends TestCase
{
    use RefreshDatabase;
    
    private Admin $partner;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->partner = Admin::factory()->create(['role' => 'partner']);
    }
    
    public function test_partner_can_view_deals_index()
    {
        $deal = Deal::factory()->create();
        PartnerDeal::factory()->create([
            'admin_id' => $this->partner->id,
            'deal_id' => $deal->id,
        ]);
        
        $response = $this->actingAs($this->partner, 'admin')
            ->get(route('partner.deals.index'));
        
        $response->assertStatus(200);
        $response->assertViewHas('deals');
        $response->assertSee($deal->name);
    }
    
    public function test_partner_can_create_deal()
    {
        $dealData = [
            'name' => 'Test Deal',
            'type' => 'Residential',
            'description' => 'Test description',
            'deal_stage' => 'pending',
        ];
        
        $response = $this->actingAs($this->partner, 'admin')
            ->post(route('partner.deals.store'), $dealData);
        
        $this->assertDatabaseHas('deals', [
            'name' => 'Test Deal',
        ]);
        
        $deal = Deal::where('name', 'Test Deal')->first();
        
        $this->assertDatabaseHas('partner_deals', [
            'admin_id' => $this->partner->id,
            'deal_id' => $deal->id,
        ]);
        
        $response->assertRedirect(route('partner.deals.show', $deal->id));
    }
    
    public function test_partner_can_view_own_deal()
    {
        $deal = Deal::factory()->create();
        PartnerDeal::factory()->create([
            'admin_id' => $this->partner->id,
            'deal_id' => $deal->id,
        ]);
        
        $response = $this->actingAs($this->partner, 'admin')
            ->get(route('partner.deals.show', $deal->id));
        
        $response->assertStatus(200);
        $response->assertViewHas('deal');
        $response->assertSee($deal->name);
    }
    
    public function test_partner_cannot_view_other_partners_deal()
    {
        $otherPartner = Admin::factory()->create(['role' => 'partner']);
        $deal = Deal::factory()->create();
        PartnerDeal::factory()->create([
            'admin_id' => $otherPartner->id,
            'deal_id' => $deal->id,
        ]);
        
        $response = $this->actingAs($this->partner, 'admin')
            ->get(route('partner.deals.show', $deal->id));
        
        $response->assertStatus(403);
    }
    
    public function test_partner_can_update_own_deal()
    {
        $deal = Deal::factory()->create();
        PartnerDeal::factory()->create([
            'admin_id' => $this->partner->id,
            'deal_id' => $deal->id,
        ]);
        
        $updateData = [
            'name' => 'Updated Deal',
            'type' => $deal->type,
            'description' => $deal->description,
            'deal_stage' => $deal->deal_stage,
        ];
        
        $response = $this->actingAs($this->partner, 'admin')
            ->put(route('partner.deals.update', $deal->id), $updateData);
        
        $this->assertDatabaseHas('deals', [
            'id' => $deal->id,
            'name' => 'Updated Deal',
        ]);
        
        $response->assertRedirect(route('partner.deals.show', $deal->id));
    }
    
    public function test_partner_can_remove_deal_association()
    {
        $deal = Deal::factory()->create();
        PartnerDeal::factory()->create([
            'admin_id' => $this->partner->id,
            'deal_id' => $deal->id,
        ]);
        
        $response = $this->actingAs($this->partner, 'admin')
            ->delete(route('partner.deals.destroy', $deal->id));
        
        $this->assertDatabaseMissing('partner_deals', [
            'admin_id' => $this->partner->id,
            'deal_id' => $deal->id,
        ]);
        
        // The deal itself should still exist
        $this->assertDatabaseHas('deals', [
            'id' => $deal->id,
        ]);
        
        $response->assertRedirect(route('partner.deals.index'));
    }
}
```

## Deployment Considerations

### 1. Database Migration

Ensure the `partner_deals` table is created before deploying the feature:

```bash
php artisan migrate
```

### 2. Data Seeding

Create a seeder to populate initial partner-deal associations:

```php
namespace Database\Seeders;

use App\Models\Admin;
use App\Models\Deal;
use App\Models\PartnerDeal;
use Illuminate\Database\Seeder;

class PartnerDealSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $partners = Admin::where('role', 'partner')->get();
        $deals = Deal::all();
        
        foreach ($partners as $partner) {
            // Associate each partner with a random selection of deals
            $dealsToAssociate = $deals->random(rand(1, 3));
            
            foreach ($dealsToAssociate as $deal) {
                PartnerDeal::firstOrCreate([
                    'admin_id' => $partner->id,
                    'deal_id' => $deal->id,
                ]);
            }
        }
    }
}
```

## Maintenance and Monitoring

### 1. Logging

Implement logging for partner deal actions:

```php
use Illuminate\Support\Facades\Log;

// In DealController methods
Log::info('Partner viewed deals list', ['partner_id' => $admin->id]);
Log::info('Partner created deal', ['partner_id' => $admin->id, 'deal_id' => $deal->id]);
Log::info('Partner updated deal', ['partner_id' => $admin->id, 'deal_id' => $deal->id]);
Log::info('Partner removed deal association', ['partner_id' => $admin->id, 'deal_id' => $deal->id]);
```

### 2. Error Handling

Implement proper error handling for partner deal operations:

```php
try {
    // Deal operation
    return redirect()->route('partner.deals.index')->with('success', 'Operation completed successfully');
} catch (\Exception $e) {
    Log::error('Error in partner deal operation', [
        'partner_id' => $admin->id,
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
    ]);
    
    return redirect()->back()->with('error', 'An error occurred. Please try again.');
}
```

## Interface Alignment: Mirroring Admin Deals Interface

After reviewing both the admin and partner interfaces, several discrepancies were identified that need to be addressed to achieve proper mirroring. This section outlines the specific changes required to align the partner deals interface with the admin interface.

### Key Differences Identified

1. **Table Columns**
   - Admin interface includes: Deal name, Deal stage, Total in progress, Total accepted, Raise target, Distributions, Investors, Close date
   - Partner interface currently has: Deal name, Status, Type, Created, Investors, Actions

2. **Data Display**
   - Admin: Simple text link for deal name
   - Partner: User thumbnail with deal name
   - Admin: Plain text for deal stage
   - Partner: Colored badges for status
   - Admin: Different formatting for monetary values

3. **Action Menu**
   - Admin: Dropdown menu with View and Delete options
   - Partner: Separate action buttons for Edit, View, and Delete

4. **Export Button**
   - Admin has an "Export all deals" button
   - Partner interface lacks this feature

5. **Filter Buttons**
   - Partner interface has filter buttons for All, Active, Pending, Closed
   - Admin interface doesn't have these filters

### Required Changes

#### 1. Table Structure Update

```html
<table class="table custom-data-table">
    <thead>
        <tr>
            <th>
                <a href="{{ route('partner.deals.index', ['sort' => 'name', 'direction' => request('sort') == 'name' && request('direction') == 'asc' ? 'desc' : 'asc']) }}">
                    @lang('Deal name')
                    <i class="las {{ request('sort') == 'name' ? (request('direction') == 'asc' ? 'la-arrow-up' : 'la-arrow-down') : 'la-arrows-alt-v' }}"></i>
                </a>
            </th>
            <th>
                <a href="{{ route('partner.deals.index', ['sort' => 'deal_stage', 'direction' => request('sort') == 'deal_stage' && request('direction') == 'asc' ? 'desc' : 'asc']) }}">
                    @lang('Deal stage')
                    <i class="las {{ request('sort') == 'deal_stage' ? (request('direction') == 'asc' ? 'la-arrow-up' : 'la-arrow-down') : 'la-arrows-alt-v' }}"></i>
                </a>
            </th>
            <th>
                <a href="{{ route('partner.deals.index', ['sort' => 'total_in_progress', 'direction' => request('sort') == 'total_in_progress' && request('direction') == 'asc' ? 'desc' : 'asc']) }}">
                    @lang('Total in progress')
                    <i class="las {{ request('sort') == 'total_in_progress' ? (request('direction') == 'asc' ? 'la-arrow-up' : 'la-arrow-down') : 'la-arrows-alt-v' }}"></i>
                </a>
            </th>
            <th>
                <a href="{{ route('partner.deals.index', ['sort' => 'total_accepted', 'direction' => request('sort') == 'total_accepted' && request('direction') == 'asc' ? 'desc' : 'asc']) }}">
                    @lang('Total accepted')
                    <i class="las {{ request('sort') == 'total_accepted' ? (request('direction') == 'asc' ? 'la-arrow-up' : 'la-arrow-down') : 'la-arrows-alt-v' }}"></i>
                </a>
            </th>
            <th>
                <a href="{{ route('partner.deals.index', ['sort' => 'raise_target', 'direction' => request('sort') == 'raise_target' && request('direction') == 'asc' ? 'desc' : 'asc']) }}">
                    @lang('Raise target')
                    <i class="las {{ request('sort') == 'raise_target' ? (request('direction') == 'asc' ? 'la-arrow-up' : 'la-arrow-down') : 'la-arrows-alt-v' }}"></i>
                </a>
            </th>
            <th>
                <a href="{{ route('partner.deals.index', ['sort' => 'distributions', 'direction' => request('sort') == 'distributions' && request('direction') == 'asc' ? 'desc' : 'asc']) }}">
                    @lang('Distributions')
                    <i class="las {{ request('sort') == 'distributions' ? (request('direction') == 'asc' ? 'la-arrow-up' : 'la-arrow-down') : 'la-arrows-alt-v' }}"></i>
                </a>
            </th>
            <th>
                <a href="{{ route('partner.deals.index', ['sort' => 'investors', 'direction' => request('sort') == 'investors' && request('direction') == 'asc' ? 'desc' : 'asc']) }}">
                    @lang('Investors')
                    <i class="las {{ request('sort') == 'investors' ? (request('direction') == 'asc' ? 'la-arrow-up' : 'la-arrow-down') : 'la-arrows-alt-v' }}"></i>
                </a>
            </th>
            <th>
                <a href="{{ route('partner.deals.index', ['sort' => 'close_date', 'direction' => request('sort') == 'close_date' && request('direction') == 'asc' ? 'desc' : 'asc']) }}">
                    @lang('Close date')
                    <i class="las {{ request('sort') == 'close_date' ? (request('direction') == 'asc' ? 'la-arrow-up' : 'la-arrow-down') : 'la-arrows-alt-v' }}"></i>
                </a>
            </th>
            <th>@lang('Action')</th>
        </tr>
    </thead>
    <!-- Table body implementation follows -->
</table>
```

#### 2. Table Row Data Display

```html
<tr>
    <td><a href="{{ route('partner.deals.summary', $deal->id) }}">{{ $deal->name }}</a></td>
    <td>{{ $deal->deal_stage }}</td>
    <td>${{ number_format($deal->total_in_progress ?? 0, 2) }} ({{ number_format($deal->progress_percentage ?? 0, 2) }}%)</td>
    <td>{{ $deal->total_accepted ?? 0 }}</td>
    <td>${{ number_format($deal->raise_target ?? 0, 0) }}</td>
    <td>
        @if(isset($deal->distributions) && count($deal->distributions) > 0)
            @foreach ($deal->distributions as $distribution)
                <div>
                    {{ $distribution->amount }}<br>
                </div>
            @endforeach
        @else
            N/A
        @endif
    </td>
    <td>{{ $deal->investors_count ?? 'N/A' }}</td>
    <td>{{ isset($deal->close_date) ? showDateTime($deal->close_date) : 'N/A' }}</td>
    <td>
        <div class="dropdown">
            <button class="btn p-0" type="button" data-bs-toggle="dropdown">
                <i class="las la-ellipsis-v"></i>
            </button>
            <ul class="dropdown-menu">
                <li>
                    <a class="dropdown-item" href="{{ route('partner.deals.summary', $deal->id) }}">
                        <i class="las la-eye me-2"></i> View
                    </a>
                </li>
                <li>
                    <button class="dropdown-item text-danger" data-bs-toggle="modal" data-bs-target="#deleteConfirmModal" onclick="setDeleteData({{ $deal->id }}, '{{ $deal->name }}')">
                        <i class="las la-trash me-2"></i> Delete
                    </button>
                </li>
            </ul>
        </div>
    </td>
</tr>
```

#### 3. Export Button Addition

```html
<div class="d-flex justify-content-between align-items-center mb-3">
    <div class="search-box">
        <input type="text" class="form-control" placeholder="@lang('Search deals...')">
    </div>
    <a href="#" class="btn btn-outline-primary px-4">@lang('Export all deals')</a>
</div>
```

#### 4. Delete Confirmation Modal Update

```html
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@lang('Delete Confirmation')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="deleteQuestion">@lang('Are you sure you want to delete this deal?')</p>
            </div>
            <div class="modal-footer">
                <form action="" method="POST" id="deleteForm">
                    @csrf
                    @method('DELETE')
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('Cancel')</button>
                    <button type="submit" class="btn btn-danger">@lang('Delete')</button>
                </form>
            </div>
        </div>
    </div>
</div>
```

#### 5. Mobile View Update

The mobile view will be updated to match the desktop columns and display the same data, formatted appropriately for mobile devices.

### Controller Updates

The `DealController` will need to be updated to provide the additional data required for the new columns:

```php
// In App\Http\Controllers\Partner\DealController.php
public function index()
{
    $partnerId = auth()->user()->partner->id;
    $partnerDeals = $this->partnerDealService->getPartnerDealsWithFullRelations($partnerId);
    $deals = $partnerDeals->map(function ($partnerDeal) {
        return $partnerDeal->deal;
    });
    
    $pageTitle = 'All Deals';
    
    return view('partner.deals.index', compact('deals', 'pageTitle'));
}
```

## View Composer Implementation

The DealViewComposer injects shared data into both admin and partner views:

```php
namespace App\Http\View\Composers;

use Illuminate\View\View;

class DealViewComposer
{
    public function compose(View $view)
    {
        $viewName = $view->getName();
        $isPartnerView = str_contains($viewName, 'partner.');
        
        $view->with([
            'isPartnerView' => $isPartnerView,
            'routePrefix' => $isPartnerView ? 'partner' : 'admin',
            'addDealRoute' => $isPartnerView ? route('partner.deals.create') : route('admin.deals.create'),
            'storeRoute' => $isPartnerView ? route('partner.deals.store') : route('admin.deals.store'),
            'indexRoute' => $isPartnerView ? route('partner.deals') : route('admin.deals'),
            'modalId' => 'dealModal',
            'tableId' => 'dealsTable',
        ]);
    }
}
```

## Maintenance Notes

When making changes to the Admin Deals interface:

1. Update shared JavaScript and CSS files as needed
2. If the view structure changes, update the View Composer to provide any new data
3. Update the Partner Deals view to match the new structure if necessary

This approach ensures that both interfaces remain synchronized while minimizing changes to core files.

## Implementation Benefits

1. **No Core File Changes**: The admin template remains untouched
2. **Centralized Logic**: View preparation logic is centralized in the composer
3. **Automatic Synchronization**: The composer ensures both views receive the same data structure
4. **Maintainable**: When the admin view structure changes, only the composer needs updating
5. **Follows Laravel Patterns**: Uses standard Laravel features (View Composers)

## Security Considerations

The implementation maintains proper access controls by:

1. Using partner-specific routes and controllers
2. Filtering deals based on partner ID in the controller
3. Using role-based permissions to restrict access to specific actions
