<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class DistributionNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $details;

    public function __construct(array $details)
    {
        $this->details = $details;
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Distribution Notification')
            ->greeting('Hello ' . ($notifiable->name ?? 'Investor'))
            ->line('You have received a distribution for the deal: ' . $this->details['deal_name'])
            ->line('Amount: $' . $this->details['amount'])
            ->line('Distribution Date: ' . $this->details['distribution_date'])
            ->line('Memo: ' . ($this->details['memo'] ?? 'N/A'))
            ->action('View Details', url('/'))
            ->line('Thank you for your investment!');
    }
}