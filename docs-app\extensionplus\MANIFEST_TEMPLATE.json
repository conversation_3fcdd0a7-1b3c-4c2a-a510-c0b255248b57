{"name": "Extension Name", "code": "ExtensionCode", "version": "1.0.0", "description": "A detailed description of what the extension does and its main features.", "author": "Developer or Company Name", "author_url": "https://example.com", "provider": "Extensions\\ExtensionCode\\ExtensionCodeServiceProvider", "requires": {"platform": ">=1.0.0", "php": ">=8.0.0"}, "permissions": {"admin": ["view", "manage", "settings"], "user": ["view", "use"]}, "settings": {"api_key": {"type": "string", "label": "API Key", "description": "Your API Key for the service", "required": true}, "api_secret": {"type": "password", "label": "API Secret", "description": "Your API Secret for the service", "required": true}, "results_per_page": {"type": "integer", "label": "Results Per Page", "description": "Number of results to display per page", "default": 10, "min": 5, "max": 50}, "notification_email": {"type": "email", "label": "Notification Email", "description": "Email address to receive notifications", "required": false}, "default_status": {"type": "select", "label": "Default Status", "description": "Default status for new items", "options": {"active": "Active", "pending": "Pending", "inactive": "Inactive"}, "default": "pending"}, "features": {"type": "checkbox", "label": "Enabled Features", "description": "Select which features to enable", "options": {"feature_1": "Feature 1", "feature_2": "Feature 2", "feature_3": "Feature 3"}, "default": ["feature_1"]}}, "menu": {"admin": [{"title": "Extension Name", "icon": "fa-puzzle-piece", "route": "admin.extensions.extension-code.index", "permission": "admin.view", "children": [{"title": "Dashboard", "route": "admin.extensions.extension-code.index", "permission": "admin.view"}, {"title": "Settings", "route": "admin.extensions.extension-code.settings", "permission": "admin.settings"}]}]}, "hooks": ["admin.sidebar.after", "admin.dashboard.widgets", "deal.view.tabs", "offering.create.after"], "assets": {"css": ["css/extension-code.css"], "js": ["js/extension-code.js"], "images": ["images/icon.png"]}, "database": {"migrations": ["create_extension_code_tables"], "seeders": ["ExtensionCodeSeeder"]}, "is_core": false}