<?php

namespace App\Console\Commands;

use App\Models\Deal;
use App\Services\DistributionService;
use Illuminate\Console\Command;
use Carbon\Carbon;

class ProcessDealCloseDistributions extends Command
{
    protected $signature = 'distributions:process-close';
    protected $description = 'Process distributions for deals with close_date matching today';

    protected $distributionService;

    public function __construct(DistributionService $distributionService)
    {
        parent::__construct();
        $this->distributionService = $distributionService;
    }

    public function handle()
    {
        $today = Carbon::today()->setTimezone('Asia/Karachi')->toDateString(); // 2025-06-03
        $this->info("Checking for deals with close_date: {$today}");
        // $today = Carbon::today()->toDateString();
        $deals = Deal::where('close_date', $today)->with('investments')->get();

        foreach ($deals as $deal) {
            // Skip if no investments
            if ($deal->investments->isEmpty()) {
                $this->info("No investments for Deal {$deal->id}");
                continue;
            }

            // Assume the deal has a default waterfall
            $waterfall = $deal->waterfalls()->where('is_default', true)->first();
            if (!$waterfall) {
                $this->error("No default waterfall for Deal {$deal->id}");
                continue;
            }

            // Calculate total investment amount as the distribution amount (or use actual profits)
            $totalInvestment = $deal->investments->sum('investment_amount');
            $amount = $totalInvestment; // Replace with actual profit amount if available

            try {
                $this->distributionService->createAndDistribute(
                    $deal->id,
                    $amount,
                    $waterfall->id,
                    'return_of_capital', // Or 'profit' based on your logic
                    "Close date distribution for Deal {$deal->id}",
                    [
                        'source' => 'deal_closure',
                        'start_date' => $deal->close_date,
                        'end_date' => $deal->close_date,
                        'distribution_date' => $today,
                        'compounding_period' => 'annual', // Adjust as needed
                    ]
                );
                $this->info("Distribution processed for Deal {$deal->id}");
            } catch (\Exception $e) {
                $this->error("Failed to process distribution for Deal {$deal->id}: {$e->getMessage()}");
            }
        }
        $this->info('Deal close distributions processed successfully.');
    }
}