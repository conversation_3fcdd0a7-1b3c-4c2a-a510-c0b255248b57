<div x-data="distributionTab()">

    <style>
        .tag-text {
            font-size: 0.7rem;
        }

        .tag-cross {
            width: 1rem;
            height: 1rem;
            padding-left: 10px;
        }

        .btn.btn-link {
            width: 20px;
        }

        .step-circle {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .step-circle.active {
            background-color: #007bff;
            /* Blue color for active */
            color: #fff;
        }

        .step-circle.inactive {
            background-color: #e9ecef;
            /* Gray for inactive */
            color: #6c757d;
        }

        .step-circle.green {
            background-color: #28a745;
            /* Green for completed */
            color: #fff;
            position: relative;
        }

        .step-circle.green::after {
            content: '✔';
            /* Add checkmark */
            position: absolute;
            right: -15px;
            /* Position the arrow */
            font-size: 14px;
            color: #28a745;
        }

        .step-circle.blue {
            background-color: #007bff;
            /* Blue for step 2 */
            color: #fff;
        }

        .step-text {
            font-size: 14px;
            margin-top: 4px;
        }

        .step-divider {
            width: 40px;
            height: 1px;
            background-color: #e9ecef;
            margin: 0 8px;
        }

        .step-divider.active {
            background-color: #007bff;
        }
    </style>
    <div class="d-flex align-items-center justify-content-center">
        <!-- Step 1 -->
        <div class="text-center" id="stepCalculationMethod">
            <div class="step-circle inactive" onclick="showStep('calculationMethod')">
                1
            </div>
            <div class="step-text">
                Calculation method
            </div>
        </div>

        <!-- Divider -->
        <div class="step-divider"></div>

        <!-- Step 2 -->
        <div class="text-center" id="stepDistributionDetails">
            <div class="step-circle inactive" onclick="showStep('distributionDetails')">
                2
            </div>
            <div class="step-text">
                Distribution details
            </div>
        </div>
    </div>


    <?php
        if (auth('admin')->user()->hasRole('partner')) {
            $prefix = 'partner';
        } else {
            $prefix = 'admin';
        }
    ?>

    <div id="calculationMethod" class="step-content">
        <div class="container mt-4">
            <h3>Choose a calculation method</h3>
            <a href="#" class="text-primary">Watch overview</a>
            <?php echo csrf_field(); ?>
            <!-- Calculation Method Options -->
            <label class="option-card" :class="{ 'selected': isActive('simple_pro_rata') }"
                @click="selectOption('simple_pro_rata')">
                
                <div class="card-body">
                    <h5 class="card-title">Simple pro-rata split on share of distribution</h5>
                    <p class="card-text">
                        Input a dollar amount to be split between chosen classes based on their
                        <a href="#" class="text-primary">distribution share</a>. Within a class, amounts are split
                        pro-rata
                        between investments based on their
                        <a href="#" class="text-primary">capital balance</a>.
                    </p>
                </div>
            </label>

            <!-- Option 2 -->
            <label class="option-card" :class="{ 'selected': isActive('water_fall') }"
                @click="selectOption('water_fall')">
                
                <div class="card-body">
                    <h5 class="card-title text-primary">Waterfall based on preferred returns, upside, and share of
                        distribution</h5>
                    <h6 class="text-primary mb-2">(Most common)</h6>
                    <p class="card-text">
                        Input a dollar amount to be split based on the chosen
                        <a href="#" class="text-primary">distribution waterfall</a>. Supports complex waterfall
                        structures with multiple hurdles.
                    </p>
                </div>
            </label>

            <!-- Option 3 -->
            <label class="option-card" :class="{ 'selected': isActive('preferred_return') }"
                @click="selectOption('preferred_return')">
                
                <div class="card-body">
                    <h5 class="card-title">Calculate for preferred return payments</h5>
                    <p class="card-text">
                        Pay out
                        <a href="#" class="text-primary">preferred return</a> accrued in a given period.
                    </p>
                </div>
            </label>

            <!-- Option 4 -->
            <label class="option-card" :class="{ 'selected': isActive('invested_payment') }"
                @click="selectOption('invested_payment')">
                
                <div class="card-body">
                    <h5 class="card-title">Calculate for interest payments</h5>
                    <p class="card-text">
                        Pay out
                        <a href="#" class="text-primary">interest</a> accrued in a given period.
                    </p>
                </div>
            </label>

            <!-- Option 5 -->
            <label class="option-card" :class="{ 'selected': isActive('invested_amount') }"
                @click="selectOption('invested_amount')">
                
                <div class="card-body">
                    <h5 class="card-title">Return invested amounts</h5>
                    <p class="card-text">
                        Pay out each active investment's remaining
                        <a href="#" class="text-primary">capital balance</a>.
                    </p>
                </div>
            </label>

            <!-- Option 6 -->
            <label class="option-card" :class="{ 'selected': isActive('Custom') }" @click="selectOption('Custom')">
                
                <div class="card-body">
                    <h5 class="card-title">Custom</h5>
                    <p class="card-text">
                        Import a spreadsheet or manually enter payment amounts.
                    </p>
                </div>
            </label>

            <!-- "Next" Button -->
            <button type="button" class="btn btn-primary mt-3" onclick="showStep('distributionDetails')">Next</button>
        </div>
    </div>



    <div id="distributionDetails" class="step-content" style="display: none;">
        
        <div class="container mt-5">
            <?php echo csrf_field(); ?>
            <template x-if="distributionForm.calculation_method === 'simple_pro_rata'">
                <?php echo $__env->make('admin.deals.distribution.partials.distribution_for_simplepro', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </template>
            <template x-if="distributionForm.calculation_method === 'water_fall'">
                <?php echo $__env->make('admin.deals.distribution.partials.distribution_for_waterfall', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </template>
            <template x-if="distributionForm.calculation_method === 'preferred_return'">
                <?php echo $__env->make('admin.deals.distribution.partials.distribution_for_calculatepref', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </template>
            <template x-if="distributionForm.calculation_method === 'invested_payment'">
                <?php echo $__env->make('admin.deals.distribution.partials.distribution_for_calculateinterest', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </template>
            <template x-if="distributionForm.calculation_method === 'invested_amount'">
                <?php echo $__env->make('admin.deals.distribution.partials.distribution_for_returninvested', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </template>
            <template x-if="distributionForm.calculation_method === 'Custom'">
                <?php echo $__env->make('admin.deals.distribution.partials.distribution_for_custom', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </template>

            <!-- Navigation Buttons -->
            
        </div>
    </div>
</div>


<!-- Distribution Details Section -->

</div>


<?php $__env->startPush('script'); ?>
    <script>
        function alpineHelpers() {
            return {
                moneyFormat(el) {
                    let value = el.value;
                    value = value.replace(/[^\d.]/g, '');
                    value = value.replace(/^0+(?=\d)/, '');
                    const parts = value.split('.');
                    if (parts.length > 2) {
                        value = parts[0] + '.' + parts.slice(1).join('');
                    }
                    if (parts[1]) {
                        parts[1] = parts[1].slice(0, 2);
                        value = parts.join('.');
                    }
                    value = value.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                    el.value = value !== '' ? '$' + value : '$0';
                },
                percentFormat(el) {
                    let value = el.value;
                    value = value.replace(/[^\d.]/g, '');
                    value = value.replace(/^0+(?=\d)/, '');
                    const parts = value.split('.');
                    if (parts.length > 2) {
                        value = parts[0] + '.' + parts.slice(1).join('');
                    }
                    if (parts[1]) {
                        parts[1] = parts[1].slice(0, 2);
                        value = parts.join('.');
                    }
                    el.value = value !== '' ? value + '%' : '0%';
                    el.setSelectionRange(el.value.length - 1, el.value.length - 1);
                }
            }
        }

        function distributionTab() {
            return {
                ...alpineHelpers(),
                distributionErrors: {},
                distributionForm: {
                    deal_id: "<?php echo e($deal->id); ?>",
                    calculation_method: 'water_fall',
                    source: '',
                    distribution_type: '',
                    count_toward: '',
                    amount: '',
                    included_classes: [], // Ensure this is reactive
                    start_date: '',
                    end_date: '',
                    distribution_date: '',
                    memo: '',
                    is_visible: 1,
                    distribution_waterfall: '',
                    compounding_period: '',
                    day_count: '',
                    preffered_return: '',
                    import_distribution: ''
                },
                submitDistributionForm(form) {
                    console.log('Submitting form:', form);
                    let url = "<?php echo e(route($prefix . '.distributions.store')); ?>";

                    fetch(url, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value
                            },
                            body: JSON.stringify(form)
                        })
                        .then(response => {
                            if (response.ok) {
                                return response.json();
                            }
                            if (response.status === 422) {
                                return response.json().then(errors => {
                                    throw errors;
                                });
                            }
                            throw new Error('Something went wrong.');
                        })
                        .then(data => {
                            console.log(data);
                            this.resetForm();
                            $('#addDistributionModal').modal('hide');
                            window.location.reload();
                        })
                        .catch(error => {
                            if (error.errors) {
                                this.distributionErrors = error.errors;
                            } else {
                                console.error('Unexpected error:', error);
                            }
                        });
                },
                resetForm() {
                    this.distributionForm = {
                        deal_id: "<?php echo e($deal->id); ?>",
                        calculation_method: '',
                        source: '',
                        distribution_type: '',
                        count_toward: '',
                        amount: '',
                        included_classes: [],
                        start_date: '',
                        end_date: '',
                        distribution_date: '',
                        memo: '',
                        is_visible: 1,
                        distribution_waterfall: '',
                        compounding_period: '',
                        day_count: '',
                        preffered_return: '',
                        import_distribution: ''
                    };
                },
                isActive(name) {
                    return this.distributionForm.calculation_method === name;
                },
                selectOption(option) {
                    this.distributionForm.calculation_method = option;
                },
                init() {
                    // Listen for notify event from multi-select
                    this.$el.addEventListener('notify', (e) => {
                        this.distributionForm.included_classes = e.detail.classes;
                        console.log('Updated included_classes:', this.distributionForm.included_classes);
                    });
                }
            }
        }
        
        document.addEventListener("alpine:init", () => {
            console.log('Alpine Initialized');
            Alpine.data("alpineMuliSelect", (obj) => ({
                elementId: obj.elementId,
                options: [],
                selected: obj.selected,
                selectedElms: [],
                show: false,
                search: '',
                open() {
                    this.show = true;
                },
                close() {
                    this.show = false;
                },
                toggle() {
                    this.show = !this.show;
                },
                isOpen() {
                    return this.show === true;
                },
                init() {
                    console.log('Alpine component Initialized');
                    const options = document.getElementById(this.elementId).options;
                    for (let i = 0; i < options.length; i++) {
                        this.options.push({
                            value: options[i].value,
                            text: options[i].innerText,
                            search: options[i].dataset.search,
                            selected: Object.values(this.selected).includes(options[i].value)
                        });
                        if (this.options[i].selected) {
                            this.selectedElms.push(this.options[i]);
                        }
                    }
                    this.$watch("search", () => {
                        this.options = [];
                        const options = document.getElementById(this.elementId).options;
                        Object.values(options).filter((el) => {
                            var reg = new RegExp(this.search, 'gi');
                            return el.dataset.search.match(reg);
                        }).forEach((el) => {
                            this.options.push({
                                value: el.value,
                                text: el.innerText,
                                search: el.dataset.search,
                                selected: Object.values(this.selected).includes(
                                    el.value)
                            });
                        });
                    });
                    this.$watch("selected", (e) => {
                        console.log('Dispatching notify event with selected:', e);
                        this.$dispatch('notify', {
                            classes: this.selected
                        });
                    });
                },
                clear() {
                    this.search = '';
                },
                deselect() {
                    setTimeout(() => {
                        this.selected = [];
                        this.selectedElms = [];
                        Object.keys(this.options).forEach((key) => {
                            this.options[key].selected = false;
                        });
                    }, 100);
                },
                select(index, event) {
                    if (!this.options[index].selected) {
                        this.options[index].selected = true;
                        this.selected.push(this.options[index].value);
                        this.selectedElms.push(this.options[index]);
                    } else {
                        this.selected.splice(this.selected.lastIndexOf(this.options[index].value), 1);
                        this.options[index].selected = false;
                        this.selectedElms = this.selectedElms.filter(elm => elm.value !== this.options[
                            index].value);
                    }
                },
                remove(index, option) {
                    this.selectedElms.splice(index, 1);
                    this.selected = this.selected.filter(value => value !== option.value);
                    this.options.forEach(opt => {
                        if (opt.value === option.value) {
                            opt.selected = false;
                        }
                    });
                },
                selectedElements() {
                    return this.options.filter(op => op.selected === true);
                },
                selectedValues() {
                    return this.options.filter(op => op.selected === true).map(el => el.value);
                },
                selectChange() {
                    console.log(this.selected);
                }
            }));
        });
    </script>
    <script>
        // Show specific step and toggle active class in step navigation
        function showStep(step) {
            const calculationMethod = document.getElementById("calculationMethod");
            const distributionDetails = document.getElementById("distributionDetails");
            const stepCalculationMethod = document.getElementById("stepCalculationMethod");
            const stepDistributionDetails = document.getElementById("stepDistributionDetails");
            const step1Circle = stepCalculationMethod.querySelector(".step-circle");
            const step2Circle = stepDistributionDetails.querySelector(".step-circle");
            const stepArrow = document.getElementById("stepArrow");
            const step1Text = document.getElementById("step1Text");

            if (step === "calculationMethod") {
                // Show Calculation Method and hide Distribution Details
                calculationMethod.style.display = "block";
                distributionDetails.style.display = "none";

                // Update step classes
                stepCalculationMethod.classList.add("active");
                stepDistributionDetails.classList.remove("active");

                // Change the color of the circles
                step1Circle.classList.remove("inactive", "green");
                step1Circle.classList.add("blue"); // Step 1 turns blue

                step2Circle.classList.remove("active", "blue");
                step2Circle.classList.add("inactive"); // Step 2 becomes inactive
            } else if (step === "distributionDetails") {
                // Show Distribution Details and hide Calculation Method
                calculationMethod.style.display = "none";
                distributionDetails.style.display = "block";

                // Update step classes
                stepCalculationMethod.classList.remove("active");
                stepDistributionDetails.classList.add("active");

                // Change the color of the circles
                step1Circle.classList.remove("blue");
                step1Circle.classList.add("green"); // Step 1 turns green

                step2Circle.classList.remove("inactive");
                step2Circle.classList.add("blue"); // Step 2 turns blue
            }
        }




        // Highlight selected option card


        document.getElementById("time_span").addEventListener("change", function() {
            const customFields = document.getElementById("custom-date-fields");
            if (this.value === "custom") {
                customFields.style.display = "block"; // Show "From" and "To" fields
            } else {
                customFields.style.display = "none"; // Hide them otherwise
            }
        });

        // Automatically select the default option on page load
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\laragon\www\PLATFORM\resources\views/admin/deals/distribution/distribution_form.blade.php ENDPATH**/ ?>