<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('deal_ach_settings', function (Blueprint $table) {
            $table->string('verify_confirmation')->nullable()->after('verify_detail');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('deal_ach_settings', function (Blueprint $table) {
            $table->dropColumn('verify_confirmation');
        });
    }
};
