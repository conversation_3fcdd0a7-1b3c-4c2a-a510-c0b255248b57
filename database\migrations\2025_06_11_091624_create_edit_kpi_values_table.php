<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('kpi_values', function (Blueprint $table) {
            $table->dropColumn(['date', 'profit']);
            $table->json('values')->nullable()->after('kpi_id');
            $table->unique('kpi_id');
        });
    }

    public function down()
    {
        Schema::table('kpi_values', function (Blueprint $table) {
            $table->dropColumn('values');
            $table->dropUnique(['kpi_id']);
            $table->date('date')->after('kpi_id');
            $table->decimal('profit', 15, 2)->nullable()->after('date');
        });
    }
};
