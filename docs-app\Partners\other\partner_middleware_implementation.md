# Partner Middleware Implementation Guide

## Overview

This document provides detailed instructions for implementing the `PartnerDealAccess` middleware, which is a critical component of the Partner interface. This middleware ensures that partners can only access their own deals.

## Implementation Steps

### 1. Create the Middleware File

Create a new file at `app/Http/Middleware/PartnerDealAccess.php`:

```php
<?php

namespace App\Http\Middleware;

use App\Models\PartnerDeal;
use Closure;
use Illuminate\Support\Facades\Auth;

class PartnerDealAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $user = Auth::guard('admin')->user();
        
        // Only apply to partners
        if (!$user->hasRole('Partner')) {
            return $next($request);
        }
        
        // For index/list views, filter in the controller
        if ($request->route()->getName() == 'partner.deals.index' || 
            $request->route()->getName() == 'partner.dashboard') {
            return $next($request);
        }
        
        // For specific deal routes, check access
        $dealId = $request->route('deal');
        if ($dealId) {
            $hasDealAccess = PartnerDeal::where('admin_id', $user->id)
                ->where('deal_id', $dealId)
                ->exists();
                
            if (!$hasDealAccess) {
                return abort(403, 'You do not have access to this deal');
            }
        }
        
        return $next($request);
    }
}
```

### 2. Register the Middleware in Kernel.php

Open `app/Http/Kernel.php` and add the middleware to the `$routeMiddleware` array:

```php
protected $routeMiddleware = [
    // Other middleware...
    'partner.deal.access' => \App\Http\Middleware\PartnerDealAccess::class,
];
```

### 3. Apply the Middleware in Routes

In `routes/partner.php`, apply the middleware to the deal routes:

```php
Route::group(['middleware' => ['partner.deal.access']], function () {
    // Deal routes...
});
```

## How the Middleware Works

1. **User Role Check**: First, the middleware checks if the user has the 'Partner' role. If not, it allows the request to proceed normally (for admins and other roles).

2. **Route-Based Bypass**: For certain routes like the dashboard and deals index, the middleware allows the request to proceed. Access control for these routes is handled in the controller instead.

3. **Deal Access Check**: For routes with a deal parameter (like show, edit, update), the middleware checks if the partner owns the deal by querying the `partner_deals` table.

4. **Access Denial**: If the partner doesn't own the deal, a 403 Forbidden response is returned.

## Testing the Middleware

To test the middleware:

1. Log in as a partner user
2. Try to access a deal that belongs to the partner (should succeed)
3. Try to access a deal that doesn't belong to the partner (should get 403 error)
4. Log in as an admin and try to access any deal (should succeed)

## Troubleshooting

### Common Issues

1. **403 Errors for Valid Deals**: Check if the `partner_deals` table has the correct associations between partners and deals.

2. **Middleware Not Being Applied**: Verify that the middleware is registered in `Kernel.php` and applied to the routes.

3. **Role Check Not Working**: Ensure that the user has the 'Partner' role assigned and that you're using the correct guard ('admin').

### Debugging Tips

Add temporary logging to the middleware to debug issues:

```php
Log::info('User ID: ' . $user->id);
Log::info('Deal ID: ' . $dealId);
Log::info('Has Access: ' . ($hasDealAccess ? 'Yes' : 'No'));
```

Remember to follow the principle that "roles and permissions are there to interact with existing modules and not the other way around" - this middleware adapts to the existing deal management system without requiring changes to the core functionality.
