<?php
    $featuredPropertyContent = getContent('featured_property.content', true);
    $featuredProperties      = App\Models\Property::active()
        ->where('is_featured', Status::YES)
        ->withSum('invests', 'total_invest_amount')
        ->withCount('invests')
        ->with(['location', 'profitScheduleTime', 'installmentDuration', 'invests'])
        ->orderByDesc('id')
        ->take(3)
        ->get();
?>

<section class="all-property py-120 bg-pattern">
    <div class="container ">
        <div class="section-heading style-left">
            <p class="section-heading__subtitle"><?php echo e(__(@$featuredPropertyContent->data_values->title)); ?></p>
            <div class="section-heading__wrapper">
                <h2 class="section-heading__title"><?php echo e(__(@$featuredPropertyContent->data_values->heading)); ?></h2>
                <a class="section-heading__link" href="<?php echo e(route('property')); ?>">
                    <span><?php echo app('translator')->get('Explore'); ?></span>
                    <i class="las la-long-arrow-alt-right"></i>
                </a>
            </div>
        </div>
        <div class="all-property__cards">
            <?php echo $__env->make($activeTemplate . 'partials.featured_property', ['featuredProperties' => @$featuredProperties], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>
</section>
<?php /**PATH C:\laragon\www\PLATFORM\resources\views/templates/basic/sections/featured_property.blade.php ENDPATH**/ ?>