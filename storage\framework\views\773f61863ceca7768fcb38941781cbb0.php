<?php
    $bannerContent = getContent('banner.content', true);
    $bannerElements = getContent('banner.element', limit: 3, orderById: true);
?>
<section class="banner bg-pattern3">
    <div class="container">
        <div class="row align-items-center gy-4">
            <div class="col-lg-7">
                <div class="banner-content">
                    <p class="banner-content__subtitle"><?php echo e(__(@$bannerContent->data_values->title)); ?></p>
                    <h1 class="banner-content__title"><?php echo e(__(@$bannerContent->data_values->heading)); ?></h1>
                    <a href="<?php echo e(@$bannerContent->data_values->button_url); ?>" class="btn btn--base">
                        <?php echo e(__(@$bannerContent->data_values->button_text)); ?>

                    </a>
                    <ul class="banner-info">
                        <?php $__currentLoopData = @$bannerElements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bannerElement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="banner-info__item">
                                <h3 class="title" data-s-break>
                                    <?php echo e(@$bannerElement->data_values->count); ?>

                                </h3>
                                <span class="text"><?php echo e(__(@$bannerElement->data_values->title)); ?></span>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            </div>
            <div class="d-none d-lg-block col-lg-5">
                <div class="banner-thumbs">
                    <div class="banner-thumbs-image">
                        <img class="fit-image pe-none"
                            src="<?php echo e(frontendImage('banner' , @$bannerContent->data_values->image, '515x565')); ?>"
                            alt="banner-image">
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php /**PATH C:\laragon\www\PLATFORM\resources\views/templates/basic/sections/banner.blade.php ENDPATH**/ ?>