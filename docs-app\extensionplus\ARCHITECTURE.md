# Extension System Technical Architecture

## 1. System Components

### 1.1 Extension Registry

The Extension Registry is the central repository for all extension metadata. It is responsible for:

- Storing extension information in the database
- Tracking extension status (installed, enabled, disabled)
- Managing extension dependencies
- Providing an API for querying extension information

```php
class ExtensionRegistry
{
    /**
     * Get all registered extensions
     */
    public function all()
    {
        return Extension::all();
    }
    
    /**
     * Get all enabled extensions
     */
    public function enabled()
    {
        return Extension::where('is_enabled', true)->get();
    }
    
    /**
     * Get extension by code
     */
    public function get($code)
    {
        return Extension::where('code', $code)->first();
    }
    
    /**
     * Register a new extension
     */
    public function register($manifest)
    {
        // Create or update extension record
    }
    
    /**
     * Enable an extension
     */
    public function enable($code)
    {
        // Enable extension and dependencies
    }
    
    /**
     * Disable an extension
     */
    public function disable($code)
    {
        // Disable extension
    }
    
    /**
     * Unregister an extension
     */
    public function unregister($code)
    {
        // Remove extension from registry
    }
}
```

### 1.2 Extension Loader

The Extension Loader is responsible for loading enabled extensions during application bootstrap. It:

- Reads the extension registry to determine which extensions to load
- Registers extension service providers
- Loads extension routes, views, and translations
- Initializes extension hooks

```php
class ExtensionLoader
{
    protected $app;
    protected $registry;
    
    public function __construct($app, ExtensionRegistry $registry)
    {
        $this->app = $app;
        $this->registry = $registry;
    }
    
    /**
     * Load all enabled extensions
     */
    public function loadExtensions()
    {
        $extensions = $this->registry->enabled();
        
        foreach ($extensions as $extension) {
            $this->loadExtension($extension);
        }
    }
    
    /**
     * Load a specific extension
     */
    protected function loadExtension($extension)
    {
        // Load extension service providers
        $this->loadProviders($extension);
        
        // Register extension aliases
        $this->registerAliases($extension);
    }
    
    /**
     * Load extension service providers
     */
    protected function loadProviders($extension)
    {
        $manifest = json_decode($extension->manifest, true);
        
        if (isset($manifest['providers'])) {
            foreach ($manifest['providers'] as $provider) {
                $this->app->register($provider);
            }
        }
    }
    
    /**
     * Register extension aliases
     */
    protected function registerAliases($extension)
    {
        $manifest = json_decode($extension->manifest, true);
        
        if (isset($manifest['aliases'])) {
            foreach ($manifest['aliases'] as $alias => $class) {
                $this->app->alias($class, $alias);
            }
        }
    }
}
```

### 1.3 Hook System

The Hook System provides a way for extensions to integrate with the platform at specific points without modifying core code. It:

- Allows registering hook handlers
- Triggers hooks at specific points in the application
- Passes context data to hook handlers

```php
class HookSystem
{
    protected $hooks = [];
    
    /**
     * Register a hook handler
     */
    public function register($hook, $callback, $priority = 10)
    {
        if (!isset($this->hooks[$hook])) {
            $this->hooks[$hook] = [];
        }
        
        $this->hooks[$hook][] = [
            'callback' => $callback,
            'priority' => $priority
        ];
        
        // Sort handlers by priority
        usort($this->hooks[$hook], function ($a, $b) {
            return $a['priority'] - $b['priority'];
        });
    }
    
    /**
     * Trigger a hook
     */
    public function trigger($hook, ...$args)
    {
        if (!isset($this->hooks[$hook])) {
            return null;
        }
        
        $result = null;
        
        foreach ($this->hooks[$hook] as $handler) {
            $callback = $handler['callback'];
            $result = call_user_func_array($callback, $args);
        }
        
        return $result;
    }
    
    /**
     * Check if a hook has handlers
     */
    public function hasHandlers($hook)
    {
        return isset($this->hooks[$hook]) && !empty($this->hooks[$hook]);
    }
    
    /**
     * Remove all handlers for a hook
     */
    public function removeHandlers($hook)
    {
        if (isset($this->hooks[$hook])) {
            unset($this->hooks[$hook]);
        }
    }
}
```

### 1.4 Extension Manager

The Extension Manager provides an admin interface for managing extensions. It:

- Lists all available extensions
- Allows installing, enabling, disabling, and uninstalling extensions
- Provides a UI for configuring extension settings
- Handles extension updates

```php
class ExtensionManagerController extends Controller
{
    protected $registry;
    
    public function __construct(ExtensionRegistry $registry)
    {
        $this->registry = $registry;
    }
    
    /**
     * Display a listing of extensions
     */
    public function index()
    {
        $extensions = $this->registry->all();
        return view('admin.extensions.index', compact('extensions'));
    }
    
    /**
     * Show extension details
     */
    public function show($code)
    {
        $extension = $this->registry->get($code);
        return view('admin.extensions.show', compact('extension'));
    }
    
    /**
     * Enable an extension
     */
    public function enable($code)
    {
        $this->registry->enable($code);
        return redirect()->route('admin.extensions.index');
    }
    
    /**
     * Disable an extension
     */
    public function disable($code)
    {
        $this->registry->disable($code);
        return redirect()->route('admin.extensions.index');
    }
    
    /**
     * Show extension settings
     */
    public function settings($code)
    {
        $extension = $this->registry->get($code);
        return view('admin.extensions.settings', compact('extension'));
    }
    
    /**
     * Save extension settings
     */
    public function saveSettings(Request $request, $code)
    {
        $extension = $this->registry->get($code);
        $extension->settings = $request->settings;
        $extension->save();
        
        return redirect()->route('admin.extensions.settings', $code);
    }
}
```

## 2. Extension Structure

### 2.1 Directory Structure

Each extension follows a standard directory structure:

```
extensions/
  u251cu2500u2500 {extension_code}/
  u2502   u251cu2500u2500 Controllers/
  u2502   u251cu2500u2500 Models/
  u2502   u251cu2500u2500 Views/
  u2502   u251cu2500u2500 Routes/
  u2502   u251cu2500u2500 Database/
  u2502   u2502   u251cu2500u2500 Migrations/
  u2502   u2502   u2514u2500u2500 Seeds/
  u2502   u251cu2500u2500 Config/
  u2502   u251cu2500u2500 Resources/
  u2502   u2502   u251cu2500u2500 assets/
  u2502   u2502   u2514u2500u2500 lang/
  u2502   u251cu2500u2500 Providers/
  u2502   u2502   u2514u2500u2500 {ExtensionName}ServiceProvider.php
  u2502   u251cu2500u2500 extension.json
  u2502   u2514u2500u2500 composer.json
```

### 2.2 Extension Manifest

The `extension.json` file contains metadata about the extension:

```json
{
  "name": "Extension Name",
  "code": "extension_code",
  "version": "1.0.0",
  "description": "Extension description",
  "author": "Author Name",
  "author_url": "https://author-website.com",
  "requires": {
    "platform": ">=1.0.0",
    "php": ">=8.0.0",
    "extensions": {
      "other_extension": ">=1.0.0"
    }
  },
  "permissions": [
    "permission_1",
    "permission_2"
  ],
  "settings": {
    "setting_1": {
      "type": "text",
      "label": "Setting 1",
      "default": "Default value"
    },
    "setting_2": {
      "type": "select",
      "label": "Setting 2",
      "options": {
        "option_1": "Option 1",
        "option_2": "Option 2"
      },
      "default": "option_1"
    }
  },
  "menu": [
    {
      "name": "Menu Item",
      "icon": "fa-icon",
      "route": "route.name",
      "permission": "permission_1",
      "children": [
        {
          "name": "Submenu Item",
          "route": "submenu.route"
        }
      ]
    }
  ],
  "providers": [
    "Extensions\\ExtensionCode\\Providers\\ExtensionServiceProvider"
  ],
  "aliases": {
    "Alias": "Extensions\\ExtensionCode\\Facades\\Alias"
  }
}
```

### 2.3 Service Provider

Each extension must have a service provider that bootstraps the extension:

```php
namespace Extensions\ExtensionCode\Providers;

use Illuminate\Support\ServiceProvider;

class ExtensionServiceProvider extends ServiceProvider
{
    public function register()
    {
        // Register extension services
        $this->app->singleton('extension_code', function ($app) {
            return new \Extensions\ExtensionCode\Services\ExtensionService();
        });
        
        // Merge extension config
        $this->mergeConfigFrom(
            __DIR__.'/../Config/config.php', 'extensions.extension_code'
        );
    }
    
    public function boot()
    {
        // Load extension routes
        $this->loadRoutesFrom(__DIR__.'/../Routes/web.php');
        $this->loadRoutesFrom(__DIR__.'/../Routes/api.php');
        
        // Load extension views
        $this->loadViewsFrom(__DIR__.'/../Views', 'extension_code');
        
        // Load extension migrations
        $this->loadMigrationsFrom(__DIR__.'/../Database/Migrations');
        
        // Publish extension assets
        $this->publishes([
            __DIR__.'/../Resources/assets' => public_path('extensions/extension_code'),
        ], 'extension_code-assets');
        
        // Register extension hooks
        $this->registerHooks();
        
        // Register extension menu items
        $this->registerMenu();
    }
    
    protected function registerHooks()
    {
        // Register hooks for extension integration points
        app('extensions.hooks')->register('hook.name', function ($data) {
            // Hook handler logic
        });
    }
    
    protected function registerMenu()
    {
        // Register extension menu items
        app('extensions.hooks')->register('admin.sidebar', function ($menu) {
            // Add menu items from extension.json
            $extension = app('extensions.registry')->get('extension_code');
            $manifest = json_decode($extension->manifest, true);
            
            if (isset($manifest['menu'])) {
                return array_merge($menu, $manifest['menu']);
            }
            
            return $menu;
        });
    }
}
```

## 3. Integration with Core Platform

### 3.1 Service Provider Registration

The Extension System is integrated with the core platform through a service provider:

```php
namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\Extensions\ExtensionRegistry;
use App\Services\Extensions\ExtensionLoader;
use App\Services\Extensions\HookSystem;

class ExtensionServiceProvider extends ServiceProvider
{
    public function register()
    {
        // Register Extension Registry
        $this->app->singleton('extensions.registry', function ($app) {
            return new ExtensionRegistry();
        });
        
        // Register Hook System
        $this->app->singleton('extensions.hooks', function ($app) {
            return new HookSystem();
        });
        
        // Register Extension Loader
        $this->app->singleton('extensions.loader', function ($app) {
            return new ExtensionLoader($app, $app['extensions.registry']);
        });
    }
    
    public function boot()
    {
        // Load enabled extensions
        $this->app['extensions.loader']->loadExtensions();
        
        // Register extension routes
        $this->loadRoutesFrom(base_path('routes/extensions.php'));
        
        // Register extension views
        $this->loadViewsFrom(base_path('resources/views/extensions'), 'extensions');
    }
}
```

### 3.2 Hook Integration

Hooks are integrated into the core platform at specific points:

```php
// In a controller
public function store(Request $request)
{
    // Validate request
    
    // Trigger before hook
    app('extensions.hooks')->trigger('deal.create.before', $request->all());
    
    // Create deal
    $deal = Deal::create($request->all());
    
    // Trigger after hook
    app('extensions.hooks')->trigger('deal.create.after', $deal);
    
    return redirect()->route('deals.show', $deal);
}
```

### 3.3 Menu Integration

Extension menu items are integrated into the admin sidebar:

```php
// In a view composer
View::composer('admin.partials.sidebar', function ($view) {
    $menu = config('admin.menu');
    
    // Get extension menu items
    $extensionMenu = app('extensions.hooks')->trigger('admin.sidebar', $menu);
    
    if ($extensionMenu) {
        $menu = $extensionMenu;
    }
    
    $view->with('menu', $menu);
});
```

## 4. Database Schema

### 4.1 Extensions Table

```php
Schema::create('extensions', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->string('code')->unique();
    $table->string('version');
    $table->text('description')->nullable();
    $table->string('author')->nullable();
    $table->string('author_url')->nullable();
    $table->text('manifest'); // JSON
    $table->text('permissions')->nullable(); // JSON
    $table->text('settings')->nullable(); // JSON
    $table->boolean('is_enabled')->default(false);
    $table->boolean('is_core')->default(false);
    $table->timestamps();
});
```

### 4.2 Extension Settings Table

```php
Schema::create('extension_settings', function (Blueprint $table) {
    $table->id();
    $table->string('extension_code');
    $table->string('key');
    $table->text('value')->nullable();
    $table->timestamps();
    
    $table->unique(['extension_code', 'key']);
    $table->foreign('extension_code')->references('code')->on('extensions')->onDelete('cascade');
});
```

## 5. Security Considerations

### 5.1 Extension Validation

Before installing an extension, the system should validate:

- Extension structure and manifest
- PHP code for potential security issues
- Required permissions
- Compatibility with platform version

### 5.2 Permission Enforcement

Extensions should only be able to access resources they have permission for:

```php
// In a middleware
public function handle($request, Closure $next)
{
    $route = $request->route();
    $extensionCode = $route->parameter('extension');
    
    $extension = app('extensions.registry')->get($extensionCode);
    $manifest = json_decode($extension->manifest, true);
    
    if (isset($manifest['permissions'])) {
        foreach ($manifest['permissions'] as $permission) {
            if (!auth()->user()->can($permission)) {
                abort(403, 'Unauthorized action.');
            }
        }
    }
    
    return $next($request);
}
```

### 5.3 Database Isolation

Extensions should use their own database tables with proper prefixes:

```php
Schema::create('ext_loopnet_listings', function (Blueprint $table) {
    $table->id();
    // Extension-specific fields
    $table->timestamps();
});
```

## 6. Performance Considerations

### 6.1 Lazy Loading

Extensions should be lazy-loaded when possible to minimize performance impact:

```php
// In the ExtensionLoader
protected function loadExtension($extension)
{
    // Only load extension if it's needed for the current request
    if ($this->shouldLoadExtension($extension)) {
        $this->loadProviders($extension);
        $this->registerAliases($extension);
    }
}

protected function shouldLoadExtension($extension)
{
    // Check if the current route belongs to the extension
    $route = request()->route();
    
    if (!$route) {
        return false;
    }
    
    $routeName = $route->getName();
    
    if (strpos($routeName, 'admin.extensions.' . $extension->code) === 0) {
        return true;
    }
    
    // Check if the extension has hooks for the current request
    $manifest = json_decode($extension->manifest, true);
    
    if (isset($manifest['hooks'])) {
        foreach ($manifest['hooks'] as $hook) {
            if (app('extensions.hooks')->hasHandlers($hook)) {
                return true;
            }
        }
    }
    
    return false;
}
```

### 6.2 Asset Optimization

Extension assets should be optimized and combined when possible:

```php
// In the ExtensionServiceProvider
public function boot()
{
    // Combine extension CSS files
    $this->combineCss();
    
    // Combine extension JS files
    $this->combineJs();
}

protected function combineCss()
{
    $extensions = app('extensions.registry')->enabled();
    $css = [];
    
    foreach ($extensions as $extension) {
        $cssPath = public_path('extensions/' . $extension->code . '/css/style.css');
        
        if (file_exists($cssPath)) {
            $css[] = file_get_contents($cssPath);
        }
    }
    
    if (!empty($css)) {
        file_put_contents(public_path('extensions/combined.css'), implode("\n", $css));
    }
}

protected function combineJs()
{
    $extensions = app('extensions.registry')->enabled();
    $js = [];
    
    foreach ($extensions as $extension) {
        $jsPath = public_path('extensions/' . $extension->code . '/js/script.js');
        
        if (file_exists($jsPath)) {
            $js[] = file_get_contents($jsPath);
        }
    }
    
    if (!empty($js)) {
        file_put_contents(public_path('extensions/combined.js'), implode("\n", $js));
    }
}
```

## 7. Testing

### 7.1 Extension Testing

Extensions should include tests to ensure they work correctly:

```php
namespace Extensions\ExtensionCode\Tests;

use Tests\TestCase;

class ExtensionTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();
        
        // Enable the extension for testing
        app('extensions.registry')->enable('extension_code');
    }
    
    public function testExtensionWorks()
    {
        // Test extension functionality
    }
    
    public function tearDown(): void
    {
        // Disable the extension after testing
        app('extensions.registry')->disable('extension_code');
        
        parent::tearDown();
    }
}
```

### 7.2 Hook Testing

Hooks should be tested to ensure they integrate correctly:

```php
public function testHookIntegration()
{
    // Register a test hook handler
    app('extensions.hooks')->register('deal.create.after', function ($deal) {
        $deal->name = 'Modified by hook';
        $deal->save();
    });
    
    // Create a deal
    $deal = Deal::create(['name' => 'Test Deal']);
    
    // Trigger the hook
    app('extensions.hooks')->trigger('deal.create.after', $deal);
    
    // Assert the hook modified the deal
    $this->assertEquals('Modified by hook', $deal->fresh()->name);
}
```

## 8. Deployment

### 8.1 Extension Installation

Extensions can be installed via:

1. **Admin Interface**: Upload a zip file containing the extension
2. **Command Line**: Use an artisan command to install an extension
3. **Composer**: Require an extension package via Composer

```php
// Extension installation command
class ExtensionInstallCommand extends Command
{
    protected $signature = 'extension:install {code} {--source=} {--force}'
    
    public function handle()
    {
        $code = $this->argument('code');
        $source = $this->option('source');
        $force = $this->option('force');
        
        // Install the extension
        $installer = new ExtensionInstaller();
        $result = $installer->install($code, $source, $force);
        
        if ($result) {
            $this->info("Extension {$code} installed successfully.");
        } else {
            $this->error("Failed to install extension {$code}.");
        }
    }
}
```

### 8.2 Extension Updates

Extensions can be updated via:

1. **Admin Interface**: Upload a new version of the extension
2. **Command Line**: Use an artisan command to update an extension
3. **Composer**: Update an extension package via Composer

```php
// Extension update command
class ExtensionUpdateCommand extends Command
{
    protected $signature = 'extension:update {code} {--source=} {--force}'
    
    public function handle()
    {
        $code = $this->argument('code');
        $source = $this->option('source');
        $force = $this->option('force');
        
        // Update the extension
        $updater = new ExtensionUpdater();
        $result = $updater->update($code, $source, $force);
        
        if ($result) {
            $this->info("Extension {$code} updated successfully.");
        } else {
            $this->error("Failed to update extension {$code}.");
        }
    }
}
```

## 9. Conclusion

This technical architecture provides a comprehensive framework for extending the CRE Investment Platform with modular extensions. By following this architecture, extensions can be developed, installed, and managed in a consistent and secure manner, while providing a seamless integration with the core platform.
