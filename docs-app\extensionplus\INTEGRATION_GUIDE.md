# Extension System Integration Guide

## 1. Overview

This guide explains how to integrate extensions with the core platform, covering hooks, UI integration, database integration, and API integration.

## 2. Hook Integration

Hooks are the primary method for extensions to integrate with the core platform. They allow extensions to execute code at specific points in the platform's execution flow.

### 2.1 Available Hook Points

#### Deal Hooks

```php
// In DealController.php
public function store(Request $request)
{
    $data = $request->validated();
    
    // Execute 'deal.create.before' hooks
    $data = app(HookSystem::class)->execute('deal.create.before', $data);
    
    $deal = Deal::create($data);
    
    // Execute 'deal.create.after' hooks
    $deal = app(HookSystem::class)->execute('deal.create.after', $deal);
    
    return response()->json($deal);
}
```

#### Offering Hooks

```php
// In OfferingController.php
public function update(Request $request, $id)
{
    $offering = Offering::findOrFail($id);
    $data = $request->validated();
    
    // Execute 'offering.update.before' hooks
    $data = app(HookSystem::class)->execute('offering.update.before', [
        'offering' => $offering,
        'data' => $data
    ]);
    
    $offering->update($data['data']);
    
    // Execute 'offering.update.after' hooks
    $offering = app(HookSystem::class)->execute('offering.update.after', $offering);
    
    return response()->json($offering);
}
```

#### Asset Hooks

```php
// In AssetController.php
public function destroy($id)
{
    $asset = Asset::findOrFail($id);
    
    // Execute 'asset.delete.before' hooks
    $result = app(HookSystem::class)->execute('asset.delete.before', $asset);
    
    // Check if any hook prevented deletion
    if ($result === false) {
        return response()->json(['message' => 'Deletion prevented by extension'], 403);
    }
    
    $asset->delete();
    
    // Execute 'asset.delete.after' hooks
    app(HookSystem::class)->execute('asset.delete.after', $id);
    
    return response()->json(['message' => 'Asset deleted successfully']);
}
```

#### UI Hooks

```php
// In admin/layouts/sidebar.blade.php
<div class="sidebar">
    <!-- Core sidebar items -->
    
    <!-- Execute 'admin.sidebar' hooks -->
    {!! app(\App\Extensions\HookSystem::class)->execute('admin.sidebar', []) !!}
</div>
```

### 2.2 Implementing Hook Handlers

Extensions can register hook handlers in their service provider:

```php
namespace Acme\Loopnet;

use App\Extensions\ExtensionServiceProvider;
use App\Extensions\HookSystem;

class LoopnetServiceProvider extends ExtensionServiceProvider
{
    protected $extensionCode = 'loopnet';
    
    public function boot()
    {
        parent::boot();
        
        $this->registerHooks();
    }
    
    protected function registerHooks()
    {
        $hookSystem = app(HookSystem::class);
        
        // Register deal hooks
        $hookSystem->register('deal.create.after', function($deal) {
            // Do something with the deal
            return $deal;
        }, 10, $this->extensionCode);
        
        // Register UI hooks
        $hookSystem->register('admin.sidebar', function($content) {
            return $content . view('loopnet::sidebar-items')->render();
        }, 10, $this->extensionCode);
    }
}
```

Alternatively, you can use dedicated hook handler classes:

```php
namespace Acme\Loopnet\Hooks;

class DealHooks
{
    public function afterCreate($deal)
    {
        // Do something with the deal
        return $deal;
    }
}

namespace Acme\Loopnet\Hooks;

class UiHooks
{
    public function adminSidebar($content)
    {
        return $content . view('loopnet::sidebar-items')->render();
    }
}
```

And register them in your service provider:

```php
protected function registerHooks()
{
    $hookSystem = app(HookSystem::class);
    
    // Register hooks using handler classes
    $hookSystem->register('deal.create.after', [app(\Acme\Loopnet\Hooks\DealHooks::class), 'afterCreate'], 10, $this->extensionCode);
    $hookSystem->register('admin.sidebar', [app(\Acme\Loopnet\Hooks\UiHooks::class), 'adminSidebar'], 10, $this->extensionCode);
}
```

## 3. UI Integration

### 3.1 Admin Sidebar Integration

Extensions can add items to the admin sidebar using the `admin.sidebar` hook:

```php
// In resources/views/sidebar-items.blade.php
<li class="nav-item">
    <a href="{{ route('admin.loopnet.index') }}" class="nav-link">
        <i class="nav-icon fas fa-building"></i>
        <p>Loopnet</p>
    </a>
</li>
```

### 3.2 Admin Dashboard Widgets

Extensions can add widgets to the admin dashboard using the `admin.dashboard` hook:

```php
// In resources/views/dashboard-widget.blade.php
<div class="col-lg-4 col-md-6 col-sm-12">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Loopnet Listings</h3>
        </div>
        <div class="card-body">
            <p>Total Listings: {{ $listingCount }}</p>
            <p>Imported Listings: {{ $importedCount }}</p>
        </div>
        <div class="card-footer">
            <a href="{{ route('admin.loopnet.listings') }}" class="btn btn-primary btn-sm">View All</a>
        </div>
    </div>
</div>
```

```php
// In UiHooks.php
public function adminDashboard($content)
{
    $listingCount = LoopnetListing::count();
    $importedCount = LoopnetListing::whereNotNull('deal_id')->count();
    
    return $content . view('loopnet::dashboard-widget', compact('listingCount', 'importedCount'))->render();
}
```

### 3.3 Deal Detail Tabs

Extensions can add tabs to the deal detail page using the `deal.tabs` hook:

```php
// In resources/views/deal-tab.blade.php
<li class="nav-item">
    <a class="nav-link" id="loopnet-tab" data-toggle="pill" href="#loopnet" role="tab" aria-controls="loopnet" aria-selected="false">
        Loopnet
    </a>
</li>
```

```php
// In resources/views/deal-tab-content.blade.php
<div class="tab-pane fade" id="loopnet" role="tabpanel" aria-labelledby="loopnet-tab">
    @if ($listing)
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Listing Details</h3>
                    </div>
                    <div class="card-body">
                        <p><strong>Loopnet ID:</strong> {{ $listing->loopnet_id }}</p>
                        <p><strong>Title:</strong> {{ $listing->title }}</p>
                        <p><strong>Price:</strong> ${{ number_format($listing->price) }}</p>
                        <p><strong>Status:</strong> {{ $listing->status }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Property Details</h3>
                    </div>
                    <div class="card-body">
                        <p><strong>Address:</strong> {{ $listing->address }}</p>
                        <p><strong>City:</strong> {{ $listing->city }}, {{ $listing->state }} {{ $listing->zip }}</p>
                        <p><strong>Building Size:</strong> {{ number_format($listing->building_size) }} sqft</p>
                        <p><strong>Lot Size:</strong> {{ number_format($listing->lot_size) }} sqft</p>
                    </div>
                </div>
            </div>
        </div>
    @else
        <div class="alert alert-info">
            No Loopnet listing associated with this deal.
        </div>
    @endif
</div>
```

```php
// In UiHooks.php
public function dealTabs($tabs, $deal)
{
    $tabs['tabs'][] = view('loopnet::deal-tab')->render();
    
    $listing = LoopnetListing::where('deal_id', $deal->id)->first();
    $tabs['contents'][] = view('loopnet::deal-tab-content', compact('listing'))->render();
    
    return $tabs;
}
```

## 4. Database Integration

### 4.1 Creating Extension Tables

Extensions should create their own tables with a prefix to avoid conflicts:

```php
// In database/migrations/create_loopnet_listings_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLoopnetListingsTable extends Migration
{
    public function up()
    {
        Schema::create('ext_loopnet_listings', function (Blueprint $table) {
            $table->id();
            $table->string('loopnet_id')->unique();
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('property_type');
            $table->string('status');
            $table->decimal('price', 15, 2)->nullable();
            $table->decimal('price_per_sqft', 10, 2)->nullable();
            $table->integer('building_size')->nullable();
            $table->integer('lot_size')->nullable();
            $table->string('address');
            $table->string('city');
            $table->string('state');
            $table->string('zip');
            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();
            $table->text('features')->nullable();
            $table->text('images')->nullable();
            $table->unsignedBigInteger('deal_id')->nullable();
            $table->unsignedBigInteger('asset_id')->nullable();
            $table->timestamp('imported_at')->nullable();
            $table->timestamps();
            
            $table->foreign('deal_id')
                  ->references('id')
                  ->on('deals')
                  ->onDelete('set null');
            $table->foreign('asset_id')
                  ->references('id')
                  ->on('assets')
                  ->onDelete('set null');
        });
    }
    
    public function down()
    {
        Schema::dropIfExists('ext_loopnet_listings');
    }
}
```

### 4.2 Relating to Core Models

Extensions can create relationships to core models:

```php
namespace Acme\Loopnet\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Deal;
use App\Models\Asset;

class LoopnetListing extends Model
{
    protected $table = 'ext_loopnet_listings';
    
    protected $fillable = [
        'loopnet_id', 'title', 'description', 'property_type', 'status',
        'price', 'price_per_sqft', 'building_size', 'lot_size',
        'address', 'city', 'state', 'zip', 'latitude', 'longitude',
        'features', 'images', 'deal_id', 'asset_id', 'imported_at'
    ];
    
    protected $casts = [
        'features' => 'array',
        'images' => 'array',
        'imported_at' => 'datetime'
    ];
    
    public function deal()
    {
        return $this->belongsTo(Deal::class);
    }
    
    public function asset()
    {
        return $this->belongsTo(Asset::class);
    }
}
```

Core models can also be extended to include relationships to extension models:

```php
// In LoopnetServiceProvider.php
public function boot()
{
    parent::boot();
    
    $this->registerHooks();
    $this->extendCoreModels();
}

protected function extendCoreModels()
{
    // Extend Deal model
    \App\Models\Deal::resolveRelationUsing('loopnet_listing', function ($deal) {
        return $deal->hasOne(\Acme\Loopnet\Models\LoopnetListing::class, 'deal_id');
    });
    
    // Extend Asset model
    \App\Models\Asset::resolveRelationUsing('loopnet_listing', function ($asset) {
        return $asset->hasOne(\Acme\Loopnet\Models\LoopnetListing::class, 'asset_id');
    });
}
```

## 5. API Integration

### 5.1 Extending API Routes

Extensions can add their own API routes:

```php
// In routes/api.php
Route::middleware(['auth:api'])->group(function () {
    Route::get('/listings', 'ApiController@listings');
    Route::get('/listings/{id}', 'ApiController@listing');
    Route::post('/listings/search', 'ApiController@search');
    Route::post('/listings/{id}/import', 'ApiController@import');
});
```

```php
// In LoopnetServiceProvider.php
public function boot()
{
    parent::boot();
    
    $this->registerHooks();
    $this->extendCoreModels();
    $this->registerRoutes();
}

protected function registerRoutes()
{
    // Register API routes
    Route::prefix('api/loopnet')
         ->middleware(['api', 'auth:api'])
         ->namespace('Acme\Loopnet\Http\Controllers')
         ->group(__DIR__ . '/../routes/api.php');
    
    // Register web routes
    Route::prefix('admin/loopnet')
         ->middleware(['web', 'auth', 'admin'])
         ->namespace('Acme\Loopnet\Http\Controllers')
         ->group(__DIR__ . '/../routes/web.php');
}
```

### 5.2 API Controllers

Extensions can create their own API controllers:

```php
namespace Acme\Loopnet\Http\Controllers;

use App\Http\Controllers\Controller;
use Acme\Loopnet\Models\LoopnetListing;
use Acme\Loopnet\Services\LoopnetApiClient;
use Illuminate\Http\Request;

class ApiController extends Controller
{
    protected $apiClient;
    
    public function __construct(LoopnetApiClient $apiClient)
    {
        $this->apiClient = $apiClient;
    }
    
    public function listings()
    {
        $listings = LoopnetListing::paginate(10);
        return response()->json($listings);
    }
    
    public function listing($id)
    {
        $listing = LoopnetListing::where('loopnet_id', $id)->firstOrFail();
        return response()->json($listing);
    }
    
    public function search(Request $request)
    {
        $results = $this->apiClient->search($request->all());
        return response()->json($results);
    }
    
    public function import(Request $request, $id)
    {
        $listing = LoopnetListing::where('loopnet_id', $id)->firstOrFail();
        
        // Import logic
        
        return response()->json(['message' => 'Listing imported successfully']);
    }
}
```

### 5.3 API Documentation

Extensions should provide API documentation:

```php
/**
 * @OA\Get(
 *     path="/api/loopnet/listings",
 *     summary="Get all Loopnet listings",
 *     tags={"Loopnet"},
 *     @OA\Response(
 *         response=200,
 *         description="Successful operation",
 *         @OA\JsonContent(
 *             type="array",
 *             @OA\Items(ref="#/components/schemas/LoopnetListing")
 *         )
 *     )
 * )
 */
public function listings()
{
    $listings = LoopnetListing::paginate(10);
    return response()->json($listings);
}
```

## 6. Settings Integration

### 6.1 Extension Settings

Extensions can define settings in their manifest:

```json
"settings": {
    "api_key": {
        "type": "string",
        "label": "API Key",
        "description": "Loopnet API Key",
        "required": true
    },
    "api_secret": {
        "type": "password",
        "label": "API Secret",
        "description": "Loopnet API Secret",
        "required": true
    },
    "results_per_page": {
        "type": "integer",
        "label": "Results Per Page",
        "description": "Number of results to display per page",
        "default": 10,
        "min": 5,
        "max": 50
    }
}
```

### 6.2 Settings Controller

Extensions can create a settings controller:

```php
namespace Acme\Loopnet\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Extensions\Extension;
use Illuminate\Http\Request;

class SettingsController extends Controller
{
    public function index()
    {
        $extension = app(Extension::class, ['code' => 'loopnet']);
        $settings = $extension->getManifest()['settings'];
        $values = [];
        
        foreach (array_keys($settings) as $key) {
            $values[$key] = $extension->getSetting($key);
        }
        
        return view('loopnet::settings', compact('settings', 'values'));
    }
    
    public function update(Request $request)
    {
        $extension = app(Extension::class, ['code' => 'loopnet']);
        $settings = $extension->getManifest()['settings'];
        
        $rules = [];
        foreach ($settings as $key => $setting) {
            if ($setting['required'] ?? false) {
                $rules[$key] = 'required';
            }
            
            if ($setting['type'] === 'integer') {
                $rules[$key] .= '|integer';
                
                if (isset($setting['min'])) {
                    $rules[$key] .= '|min:' . $setting['min'];
                }
                
                if (isset($setting['max'])) {
                    $rules[$key] .= '|max:' . $setting['max'];
                }
            } elseif ($setting['type'] === 'email') {
                $rules[$key] .= '|email';
            }
        }
        
        $validated = $request->validate($rules);
        
        foreach ($validated as $key => $value) {
            $extension->setSetting($key, $value);
        }
        
        return redirect()->back()->with('success', 'Settings updated successfully');
    }
}
```

### 6.3 Settings View

Extensions can create a settings view:

```php
// In resources/views/settings.blade.php
@extends('admin.layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Loopnet Settings</h3>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.loopnet.settings.update') }}" method="POST">
                @csrf
                
                @foreach($settings as $key => $setting)
                    <div class="form-group">
                        <label for="{{ $key }}">{{ $setting['label'] }}</label>
                        
                        @if($setting['type'] === 'password')
                            <input type="password" class="form-control" id="{{ $key }}" name="{{ $key }}" value="{{ $values[$key] }}" {{ $setting['required'] ? 'required' : '' }}>
                        @elseif($setting['type'] === 'integer')
                            <input type="number" class="form-control" id="{{ $key }}" name="{{ $key }}" value="{{ $values[$key] }}" {{ $setting['required'] ? 'required' : '' }} min="{{ $setting['min'] ?? '' }}" max="{{ $setting['max'] ?? '' }}">
                        @elseif($setting['type'] === 'boolean')
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="{{ $key }}" name="{{ $key }}" value="1" {{ $values[$key] ? 'checked' : '' }}>
                                <label class="custom-control-label" for="{{ $key }}"></label>
                            </div>
                        @elseif($setting['type'] === 'select')
                            <select class="form-control" id="{{ $key }}" name="{{ $key }}" {{ $setting['required'] ? 'required' : '' }}>
                                @foreach($setting['options'] as $option)
                                    <option value="{{ $option['value'] }}" {{ $values[$key] === $option['value'] ? 'selected' : '' }}>{{ $option['label'] }}</option>
                                @endforeach
                            </select>
                        @else
                            <input type="text" class="form-control" id="{{ $key }}" name="{{ $key }}" value="{{ $values[$key] }}" {{ $setting['required'] ? 'required' : '' }}>
                        @endif
                        
                        @if(isset($setting['description']))
                            <small class="form-text text-muted">{{ $setting['description'] }}</small>
                        @endif
                    </div>
                @endforeach
                
                <button type="submit" class="btn btn-primary">Save Settings</button>
            </form>
        </div>
    </div>
</div>
@endsection
```

## 7. Asset Integration

### 7.1 Publishing Assets

Extensions can publish assets during installation:

```php
// In LoopnetServiceProvider.php
public function boot()
{
    parent::boot();
    
    $this->registerHooks();
    $this->extendCoreModels();
    $this->registerRoutes();
    $this->publishAssets();
}

protected function publishAssets()
{
    $this->publishes([
        __DIR__ . '/../resources/js' => public_path('extensions/loopnet/js'),
        __DIR__ . '/../resources/css' => public_path('extensions/loopnet/css'),
        __DIR__ . '/../resources/images' => public_path('extensions/loopnet/images'),
    ], 'loopnet-assets');
}
```

### 7.2 Loading Assets

Extensions can load assets in views:

```php
// In resources/views/index.blade.php
@extends('admin.layouts.app')

@section('styles')
    <link rel="stylesheet" href="{{ asset('extensions/loopnet/css/loopnet.css') }}">
@endsection

@section('scripts')
    <script src="{{ asset('extensions/loopnet/js/loopnet.js') }}"></script>
@endsection

@section('content')
    <!-- Content -->
@endsection
```

## 8. Localization Integration

### 8.1 Translation Files

Extensions can provide translation files:

```php
// In resources/lang/en/loopnet.php
return [
    'title' => 'Loopnet Integration',
    'description' => 'Integrate with Loopnet to import property listings',
    'settings' => [
        'title' => 'Loopnet Settings',
        'api_key' => 'API Key',
        'api_secret' => 'API Secret',
        'results_per_page' => 'Results Per Page'
    ],
    'listings' => [
        'title' => 'Loopnet Listings',
        'import' => 'Import Listing',
        'view' => 'View Listing',
        'delete' => 'Delete Listing',
        'confirm_delete' => 'Are you sure you want to delete this listing?'
    ]
];
```

### 8.2 Loading Translations

Extensions can load translations in views:

```php
// In resources/views/index.blade.php
@extends('admin.layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">{{ __('loopnet::loopnet.listings.title') }}</h3>
        </div>
        <div class="card-body">
            <!-- Content -->
        </div>
    </div>
</div>
@endsection
```

### 8.3 Registering Translations

Extensions can register translations in their service provider:

```php
// In LoopnetServiceProvider.php
public function boot()
{
    parent::boot();
    
    $this->registerHooks();
    $this->extendCoreModels();
    $this->registerRoutes();
    $this->publishAssets();
    $this->loadTranslations();
}

protected function loadTranslations()
{
    $this->loadTranslationsFrom(__DIR__ . '/../resources/lang', 'loopnet');
}
```

## 9. Conclusion

By following the guidelines in this document, you can create extensions that integrate seamlessly with the core platform. The Extension System provides a flexible and powerful way to extend the platform's functionality without modifying the core code.
