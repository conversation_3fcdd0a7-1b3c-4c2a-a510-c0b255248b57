# Permission Middleware Enhancements for Organizational Hierarchy

## Overview

The `PermissionMiddleware` needs to be enhanced to implement hierarchical permissions that reflect the organizational structure. This includes special handling for C-Suite executives and department managers.

## Implementation Steps

### 1. Enhance the Middleware Handler

Update the `handle` method to implement hierarchical permission checks:

```php
public function handle($request, Closure $next, $permission)
{
    // Check if user is logged in
    if (!Auth::guard('admin')->check()) {
        return to_route('admin.login');
    }

    // Get the authenticated user
    $user = Auth::guard('admin')->user();
    
    // Debug logging (only in development environment)
    if (app()->environment('local', 'development')) {
        Log::info('Permission check for: ' . $permission);
        Log::info('User: ' . $user->name . ' (ID: ' . $user->id . ')');
        Log::info('User roles: ' . implode(', ', $user->getRoleNames()->toArray()));
        Log::info('Has Super Admin role: ' . ($user->hasRole('Super Admin', 'admin') ? 'Yes' : 'No'));
    }
    
    // Super Admin role should bypass all permission checks
    if ($user->hasRole('Super Admin', 'admin')) {
        if (app()->environment('local', 'development')) {
            Log::info('User has Super Admin role - bypassing permission check');
        }
        return $next($request);
    }
    
    // CEO role should have access to most things except system-level permissions
    if ($user->hasRole('CEO', 'admin') && !str_starts_with($permission, 'system.')) {
        if (app()->environment('local', 'development')) {
            Log::info('User has CEO role - bypassing most permission checks');
        }
        return $next($request);
    }
    
    // C-Suite executives have access to their departments
    if ($this->hasExecutiveAccess($user, $permission)) {
        if (app()->environment('local', 'development')) {
            Log::info('User has executive access to this permission');
        }
        return $next($request);
    }
    
    // Department managers have full access to their department
    if ($this->hasDepartmentManagerAccess($user, $permission)) {
        if (app()->environment('local', 'development')) {
            Log::info('User has department manager access to this permission');
        }
        return $next($request);
    }

    // Check if the user has the required permission
    $hasPermission = $user->hasPermissionTo($permission, 'admin');
    
    if (app()->environment('local', 'development')) {
        Log::info('User has permission ' . $permission . ': ' . ($hasPermission ? 'Yes' : 'No'));
    }
    
    if (!$hasPermission) {
        throw new UnauthorizedException(403, 'You do not have the required permission to access this resource.');
    }

    return $next($request);
}
```

### 2. Add Helper Methods for Hierarchical Checks

Add helper methods to check for executive and department-level access:

```php
/**
 * Check if user has executive-level access to the permission
 *
 * @param  \App\Models\Admin  $user
 * @param  string  $permission
 * @return bool
 */
private function hasExecutiveAccess($user, $permission)
{
    $executiveMap = [
        'CFO' => ['accounting.', 'investments.view', 'deals.view', 'assets.view'],
        'COO' => ['deals.', 'assets.', 'partnerships.', 'investor_relations.', 'marketing.', 'general_management.'],
        'CTO' => ['technology.', 'system.'],
        'CLO' => ['compliance_legal.']
    ];
    
    foreach ($executiveMap as $role => $permissionPrefixes) {
        if ($user->hasRole($role, 'admin')) {
            foreach ($permissionPrefixes as $prefix) {
                if (str_starts_with($permission, $prefix)) {
                    return true;
                }
            }
        }
    }
    
    return false;
}

/**
 * Check if user has department manager access to the permission
 *
 * @param  \App\Models\Admin  $user
 * @param  string  $permission
 * @return bool
 */
private function hasDepartmentManagerAccess($user, $permission)
{
    $managerMap = [
        'Accounting Manager' => 'accounting.',
        'Deals Manager' => 'deals.',
        'Asset Manager' => 'assets.',
        'Partnerships Manager' => 'partnerships.',
        'Investor Relations Manager' => 'investor_relations.',
        'Marketing Manager' => 'marketing.',
        'General Management Manager' => 'general_management.',
        'Technology Manager' => 'technology.',
        'Compliance & Legal Manager' => 'compliance_legal.'
    ];
    
    foreach ($managerMap as $role => $permissionPrefix) {
        if ($user->hasRole($role, 'admin') && str_starts_with($permission, $permissionPrefix)) {
            return true;
        }
    }
    
    return false;
}
```

### 3. Backward Compatibility Considerations

The enhanced middleware maintains backward compatibility by:

1. Preserving the existing Super Admin bypass
2. Continuing to check for specific permissions when no role-based bypass applies
3. Using the same permission format (e.g., 'deals.view', 'investments.create')

### 4. Testing the Enhanced Middleware

After implementing the middleware enhancements:

1. Clear the application cache:
   ```bash
   php artisan optimize:clear
   ```

2. Test with different user roles to ensure proper access control:
   - Super Admin should have access to everything
   - CEO should have access to most things except system-level permissions
   - C-Suite executives should have access to their departments
   - Department managers should have access to their departments
   - Staff should only have access to what they're explicitly granted
