<div x-data="KPIFormHandler()">

    <div class="d-flex justify-content-between">
        <p>Add KPIs to easily add charts and tables to LP update emails</p>
        <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addKPIModal">+ Add KPI's
            Collection</button>
    </div>
    <div class="table-responsive mt-3">
        <table class="table table-bordered mt-3" id="kpis-table">
            <thead>
                <tr>
                    <th> Name</th>
                    <th>Type</th>
                    <th># of KPI's</th>
                    <th># of Dates</th>
                    <th>Latest Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php $__currentLoopData = $deal->kpisCollections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $kpisCollection): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td><a href="<?php echo e(route($prefix . '.kpi.detailPage', ['deal' => $deal->id, 'kpicollection' => $kpisCollection->id])); ?>"><?php echo e($kpisCollection->kpi_collection_name); ?></a></td>
                    <td><?php echo e($kpisCollection->kpi_collection_type); ?></td>
                    <td><?php echo e($kpisCollection->kpis->count()); ?></td>
                    <td>4</td>
                    <td>1</td>
                    <td>1</td>
                    <td>
                        <span role="button" title="delete">
                            <i class="fas fa-trash"></i>
                        </span>
                    </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
    </div>


    <div class="deal-modal modal right fade" id="addKPIModal" tabindex="-1" aria-labelledby="addKPIModalLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content px-2">
                <div class="modal-header row bg-primary text-white" style="height:80px;">
                    <h5 class="modal-title col text-white">Add KPI collection</h5>
                    <button type="button" class="btn-close text-white" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    
                    <div>
                        <?php echo csrf_field(); ?>
                        <p>Add a KPI collection to contain a set of KPIs</p>
                        
                        <div class="mb-3">
                            <label for="kpi_collection_name" class="form-label">KPI collection name</label>
                            <input type="text" id="kpi_collection_name" name="kpi_collection_name"
                                class="form-control" x-model="kpiForm.kpi_collection_name">
                        </div>
                        <div>
                            <label for="kpi_collection_type" class="form-label">KPI collection type</label>
                            <select id="kpi_collection_type" name="kpi_collection_type" class="form-select"
                                x-model="kpiForm.kpi_collection_type">
                                <option value="">Select KPI Type</option>
                                <option value="daily">Daily</option>
                                <option value="yearly">Yearly</option>
                                <option value="monthly">Monthly(Recommended)</option>
                            </select>

                        </div>
                        <div class="d-flex pt-5 justify-content-end">
                            <button type="button" class="btn btn-secondary me-2"
                                data-bs-dismiss="modal">Cancel</button>
                            <span class="btn btn-primary deal-save" @click="submitKPIForm(kpiForm)">
                                Save
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<?php $__env->startPush('script'); ?>
    <script>
        function KPIFormHandler() {
            return {
                kpiForm: {
                    _token: csrf,
                    deal_id: "<?php echo e($deal->id); ?>",
                    kpi_collection_name: '',
                    kpi_collection_type: '',

                },

                errors: {},
                async submitKPIForm(data) {
                    this.loading = true;
                    let url = "<?php echo e(route($prefix . '.kpi.store', $deal->id)); ?>";

                    try {

                        let formData = new FormData();
                        for (const key in data) {
                            if (data.hasOwnProperty(key)) {
                                formData.append(key, data[key]);
                            }
                        }

                        const response = await fetch(url, {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': csrf
                            },
                            body: formData
                        });

                        this.loading = false;

                        if (response.status === 422) {
                            const responseData = await response.json();
                            // update errors in alpine data
                            this.errors = responseData.errors;
                            return;
                        }

                        const responseData = await response.json();
                        if (response.status === 200) {
                            const modalElement = document.querySelector('.modal.show');
                            const modalInstance = bootstrap.Modal.getInstance(modalElement);
                            modalInstance.hide();

                            // Reload the page
                            window.location.reload();

                        } else {
                            // alert(responseData.message);
                            console.log(responseData);
                        }

                    } catch (error) {
                        console.error('Error:', error);
                    }
                },




            }
        }
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\laragon\www\PLATFORM\resources\views/admin/deals/kpi.blade.php ENDPATH**/ ?>