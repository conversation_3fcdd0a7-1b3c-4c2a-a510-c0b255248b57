# Route Naming Convention Issue in Admin Panel

## Problem Description

When adding new routes to the admin panel, we encountered a persistent error:

```
Symfony\Component\Routing\Exception\RouteNotFoundException
Route [offering-questionnaires.index] not defined.
```

This error occurred despite correctly defining the routes in `routes/admin.php` and adding the corresponding entries in `resources/views/admin/partials/sidenav.json`.

## Root Cause

The issue stemmed from a mismatch between how routes are registered in the application and how they were referenced in the sidebar navigation:

1. In `app/Providers/RouteServiceProvider.php`, all routes defined in `admin.php` automatically get an "admin." prefix added to their names:

```php
Route::prefix("admin")
    ->middleware(["web", "admin", "check.permission"])
    ->namespace($this->namespace . "\Admin")
    ->name("admin.")  // This adds "admin." prefix to all route names
    ->group(base_path("routes/admin.php"));
```

2. However, in `sidenav.json`, we were referencing the routes without this prefix:

```json
"route_name": "offering-questionnaires.index",
"menu_active": "offering-questionnaires.index"
```

## Solution

The solution was to update the route references in `sidenav.json` to include the "admin." prefix:

```json
"route_name": "admin.offering-questionnaires.index",
"menu_active": "admin.offering-questionnaires.index"
```

This ensures that the sidebar navigation is looking for routes with the correct names as they are registered in the application.

## Lessons Learned

1. **Understand Route Registration**: Before adding new routes, understand how route registration works in the application, particularly any automatic prefixing.

2. **Follow Existing Patterns**: Look at existing routes in the codebase to understand the naming conventions and patterns being used.

3. **Check RouteServiceProvider**: The `RouteServiceProvider.php` file contains crucial information about how routes are registered and what prefixes are applied.

4. **Clear Route Cache**: After making changes to routes, always clear the route cache with `php artisan optimize:clear` to ensure changes are recognized.

5. **Maintain Separation**: Remember that the offering questionnaire system uses the "offering_questionnaire_" prefix for database tables to maintain separation from the existing questionnaire system, while routes follow the application's convention of adding the "admin." prefix.

## Reference

This issue was resolved on March 27, 2025, while implementing the Offering Questionnaire module for the CREI Investment Platform.
