# Partner Routes Modification Log

## Date: 2025-03-12

### Files to be modified:

1. `app/Providers/RouteServiceProvider.php` - Uncomment and update partner routes registration
2. `routes/admin.php` - Move partner portal routes to dedicated file
3. `routes/partner.php` - Update/verify routes structure

### Backup of original files:

#### RouteServiceProvider.php (relevant section):
```php
// Partner routes are now included in admin.php
// Route::prefix("admin/partner")
//     ->middleware(["web"])
//     ->namespace($this->namespace . "\\Admin")
//     ->name("admin.partner.")
//     ->group(base_path("routes/partner.php"));
```

#### admin.php (partner portal routes section):
```php
// Partner Portal Routes
Route::group(['prefix' => 'partner', 'middleware' => ['role:partner']], function () {
    Route::get('/dashboard', [PartnerController::class, 'dashboard'])->name('partner.dashboard');
    Route::get('/profile', [PartnerController::class, 'showProfile'])->name('partner.profile.index');
    Route::post('/profile', [PartnerController::class, 'updateProfile'])->name('partner.profile.update');
    
    // Partner Deal Management
    Route::group(['prefix' => 'deals', 'middleware' => ['partner.deal.access']], function () {
        Route::get('/', [PartnerController::class, 'deals'])->name('partner.deals.index');
        Route::get('/create', [PartnerController::class, 'createDeal'])->name('partner.deals.create');
        Route::post('/', [PartnerController::class, 'storeDeal'])->name('partner.deals.store');
        Route::get('/{deal}', [PartnerController::class, 'showDeal'])->name('partner.deals.show');
        Route::get('/{deal}/edit', [PartnerController::class, 'editDeal'])->name('partner.deals.edit');
        Route::put('/{deal}', [PartnerController::class, 'updateDeal'])->name('partner.deals.update');
        Route::delete('/{deal}', [PartnerController::class, 'destroyDeal'])->name('partner.deals.destroy');
    });
});
```

### Rollback Plan:

If the changes don't work as expected, we will:

1. Revert RouteServiceProvider.php to its original state (re-comment the partner routes registration)
2. Add back the partner portal routes to admin.php
3. Clear route cache using `php artisan route:clear`
4. Clear application cache using `php artisan optimize:clear`
