{"dashboard": {"keyword": ["Dashboard", "Home", "Panel", "Admin", "Control center", "Overview", "Main hub", "Management hub", "Administrative hub", "Central hub", "Command center", "Administrator portal", "Centralized interface", "Admin console", "Management dashboard", "Main screen", "Administrative dashboard", "Command dashboard", "Main control panel"], "title": "Dashboard", "icon": "las la-home", "route_name": "admin.dashboard", "menu_active": "admin.dashboard"}, "deals": {"title": " Deals", "icon": "las la-stream", "menu_active": "admin.deals*", "submenu": [{"keyword": ["Deals", "Deals"], "title": "Deals List", "route_name": "admin.deals.index", "menu_active": "admin.deals.index"}]}, "Emails": {"title": "Emails", "icon": "las la-envelope", "menu_active": "admin.emails*", "submenu": [{"keyword": ["Email Templates", "Email Template List"], "title": "Email Templates", "route_name": "admin.emails.index", "menu_active": "admin.emails.index"}]}, "offering_questionnaires": {"title": "Offering Questionnaires", "icon": "las la-clipboard-list", "menu_active": "admin.offering-questionnaires*", "permission": null, "submenu": [{"keyword": ["Questionnaires", "Forms", "Offering Questionnaires"], "title": "All Questionnaires", "route_name": "admin.offering-questionnaires.index", "menu_active": "admin.offering-questionnaires.index"}, {"keyword": ["Create Questionnaire", "New Questionnaire", "Add Questionnaire"], "title": "Create New", "route_name": "admin.offering-questionnaires.create", "menu_active": "admin.offering-questionnaires.create"}]}, "partner_management": {"title": "Partner Management", "icon": "las la-handshake", "menu_active": "admin.partner-management*", "submenu": [{"keyword": ["Partners", "Partner List", "All Partners"], "title": "All Partners", "route_name": "admin.partner-management.index", "menu_active": "admin.partner-management.index"}, {"keyword": ["Add Partner", "Create Partner", "New Partner"], "title": "Add New Partner", "route_name": "admin.partner-management.create", "menu_active": "admin.partner-management.create"}]}, "offerings": {"title": "Offerings", "icon": "las la-gift", "menu_active": "admin.deals.offerings*", "submenu": [{"title": "All Offerings", "route_name": "admin.deals.index", "menu_active": "admin.deals.index"}]}, "investment": {"title": "Investment", "icon": "las la-stream", "counters": ["pendingProfitCount"], "menu_active": "admin.invest*", "submenu": [{"keyword": ["Running Investment", "investment running"], "title": "Running Investment", "route_name": "admin.invest.running", "menu_active": "admin.invest.running"}, {"keyword": ["Completed Investment", "investment completed"], "title": "Completed Investment", "route_name": "admin.invest.completed", "menu_active": "admin.invest.completed"}, {"keyword": ["All Investment", "investment all"], "title": "All Investment", "route_name": "admin.invest.all", "menu_active": "admin.invest.all"}, {"keyword": ["Installment Log"], "title": "Installment Log", "route_name": "admin.invest.installment", "menu_active": "admin.invest.installment"}, {"keyword": ["Pending Profit", "user profit"], "title": "Pending Profit", "route_name": "admin.invest.profit.pending", "menu_active": "admin.invest.profit.pending", "counter": "pendingProfitCount"}, {"keyword": ["Profit Log", "profit history"], "title": "Profit Log", "route_name": "admin.invest.profit", "menu_active": "admin.invest.profit"}, {"keyword": ["Investment statistics", "invest statistics", "statistics"], "title": "Investment Statistics", "route_name": "admin.invest.report.dashboard", "menu_active": "admin.invest.report.dashboard"}]}, "assets": {"title": "Assets", "icon": "las la-building", "menu_active": "admin.assets*", "submenu": [{"title": "All Assets", "route_name": "admin.assets", "menu_active": "admin.assets"}, {"title": "Add New", "route_name": "admin.assets.create", "menu_active": "admin.assets.create"}]}, "manage_properties": {"title": "Manage Properties", "icon": "las la-city", "menu_active": "admin.manage*", "submenu": [{"keyword": ["All Properties", "property", "create property", "add property"], "title": "All Properties", "route_name": "admin.manage.property.index", "menu_active": "admin.manage.property.index"}, {"keyword": ["property", "invested property"], "title": "Invested Properties", "route_name": "admin.manage.property.invested", "menu_active": "admin.manage.property.invested"}, {"keyword": ["manage time", "time", "create time", "time setting"], "title": "Manage Time", "route_name": "admin.manage.time.index", "menu_active": "admin.manage.time*"}, {"keyword": ["location", "manage location", "create location", "location setting"], "title": "Manage Location", "route_name": "admin.manage.location.index", "menu_active": "admin.manage.location*"}, {"keyword": ["contract template", "manage contract template"], "title": "Contract Template", "route_name": "admin.manage.contract.template", "menu_active": "admin.manage.contract.template"}]}, "manage_users": {"title": "Manage Users", "icon": "las la-users", "counters": ["bannedUsersCount", "emailUnverifiedUsersCount", "mobileUnverifiedUsersCount", "kycUnverifiedUsersCount", "kycPendingUsersCount"], "menu_active": "admin.users*", "submenu": [{"keyword": ["active users", "Manage Users", "User management", "User control", "User status", "User activity", "User analytics"], "title": "Active Users", "route_name": "admin.users.active", "menu_active": "admin.users.active"}, {"keyword": ["banned users", "Manage Users", "User management", "Account bans", "User activity"], "title": "Banned Users", "route_name": "admin.users.banned", "menu_active": "admin.users.banned", "counter": "bannedUsersCount"}, {"keyword": ["email unverified users", "Manage Users", "User verification", "User authentication", "User management"], "title": "Email Unverified", "route_name": "admin.users.email.unverified", "menu_active": "admin.users.email.unverified", "counter": "emailUnverifiedUsersCount"}, {"keyword": ["mobile unverified users", "Manage Users", "User verification", "User authentication", "User management"], "title": "Mobile Unverified", "route_name": "admin.users.mobile.unverified", "menu_active": "admin.users.mobile.unverified", "counter": "mobileUnverifiedUsersCount"}, {"keyword": ["kyc unverified users", "Manage Users", "User verification", "User authentication", "User management"], "title": "KYC Unverified", "route_name": "admin.users.kyc.unverified", "menu_active": "admin.users.kyc.unverified", "counter": "kycUnverifiedUsersCount"}, {"keyword": ["kyc pending users", "Manage Users", "User verification", "User authentication", "User management"], "title": "KYC Pending", "route_name": "admin.users.kyc.pending", "menu_active": "admin.users.kyc.pending", "counter": "kycPendingUsersCount"}, {"keyword": ["with balance users", "Manage Users", "User management", "User activity", "Account management"], "title": "With Balance", "route_name": "admin.users.with.balance", "menu_active": "admin.users.with.balance"}, {"keyword": ["all users users", "Manage Users", "User management", "User control", "User activity", "User analytics"], "title": "All Users", "route_name": "admin.users.all", "menu_active": "admin.users.all"}, {"keyword": ["send notification users", "Manage Users", "User notifications", "User management", "User activity"], "title": "Send Notification", "route_name": "admin.users.notification.all", "menu_active": "admin.users.notification.all"}]}, "staff_management": {"title": "Staff Management", "icon": "las la-user-shield", "menu_active": "admin.staff*", "submenu": [{"title": "All Staff", "route_name": "admin.staff.index", "menu_active": "admin.staff.index"}, {"title": "Add New Staff", "route_name": "admin.staff.create", "menu_active": "admin.staff.create"}, {"title": "Roles & Permissions", "route_name": "admin.staff.roles", "menu_active": "admin.staff.roles"}]}, "deposits": {"title": "Deposits", "icon": "las la-file-invoice-dollar", "counters": ["pendingDepositsCount"], "menu_active": "admin.deposit*", "submenu": [{"keyword": ["Pending Deposits", "Deposits", "Deposit management", "Deposit control", "Deposit status", "Deposit activity"], "title": "Pending Deposits", "route_name": "admin.deposit.pending", "menu_active": "admin.deposit.pending", "counter": "pendingDepositsCount", "params": {"user_id": ""}}, {"keyword": ["Approved Deposits", "Deposits", "Deposit management", "Deposit activity"], "title": "Approved Deposits", "route_name": "admin.deposit.approved", "menu_active": "admin.deposit.approved", "params": {"user_id": ""}}, {"keyword": ["Successful Deposits", "Deposits", "Deposit management", "Deposit activity"], "title": "Successful Deposits", "route_name": "admin.deposit.successful", "menu_active": "admin.deposit.successful", "params": {"user_id": ""}}, {"keyword": ["Rejected Deposits", "Deposits", "Deposit management", "Deposit activity"], "title": "Rejected Deposits", "route_name": "admin.deposit.rejected", "menu_active": "admin.deposit.rejected", "params": {"user_id": ""}}, {"keyword": ["Initiated Deposits", "Deposits", "Deposit management", "Deposit activity"], "title": "Initiated Deposits", "route_name": "admin.deposit.initiated", "menu_active": "admin.deposit.initiated", "params": {"user_id": ""}}, {"keyword": ["All Deposits", "Deposits", "Deposit management", "Deposit control", "Deposit activity"], "title": "All Deposits", "route_name": "admin.deposit.list", "menu_active": "admin.deposit.list", "params": {"user_id": ""}}]}, "withdrawals": {"title": "<PERSON><PERSON><PERSON><PERSON>", "icon": "las la-money-bill-wave", "counters": ["pendingWithdrawCount"], "menu_active": "admin.withdraw*", "submenu": [{"keyword": ["Pending Withdrawals", "Pending withdrawal requests", "<PERSON><PERSON><PERSON> requests pending", "Pending withdrawal applications", "Withdrawal approvals pending", "Awaiting withdrawal approval", "Withdrawal verification pending", "Pending withdrawal transactions", "Withdrawal processing queue", "Pending withdrawal list"], "title": "Pending Withdrawals", "route_name": "admin.withdraw.data.pending", "menu_active": "admin.withdraw.data.pending"}, {"keyword": ["Approved Withdrawals", "Completed withdrawals", "Successful withdrawals", "Processed withdrawals", "Verified withdrawals", "Confirmed withdrawals", "Cleared withdrawals", "Validated withdrawals", "Accepted withdrawals", "Finalized withdrawals"], "title": "Approved Withdrawals", "route_name": "admin.withdraw.data.approved", "menu_active": "admin.withdraw.data.approved"}, {"keyword": ["Rejected <PERSON>s", "Declined withdrawals", "Failed withdrawals", "Cancelled withdrawals", "Denied withdrawals", "Unsuccessful withdrawals", "Voided withdrawals", "Invalidated withdrawals", "Disapproved withdrawals", "<PERSON><PERSON><PERSON> rejections"], "title": "Rejected <PERSON>s", "route_name": "admin.withdraw.data.rejected", "menu_active": "admin.withdraw.data.rejected"}, {"keyword": ["All Withdrawals", "Complete withdrawal list", "Full withdrawal history", "Withdrawal records", "Withdrawal transactions", "<PERSON><PERSON><PERSON> log", "Withdrawal archive", "Historical withdrawals", "Withdrawal database", "<PERSON><PERSON><PERSON> tracking"], "title": "All Withdrawals", "route_name": "admin.withdraw.data.all", "menu_active": "admin.withdraw.data.all"}, {"keyword": ["<PERSON><PERSON>wal Methods", "Payment methods", "Withdrawal options", "Payout methods", "Transfer methods", "Payment settings", "Withdrawal configuration", "Payment configuration", "Withdrawal setup", "Payment setup"], "title": "<PERSON><PERSON>wal Methods", "route_name": "admin.withdraw.method.index", "menu_active": "admin.withdraw.method.index"}]}, "documents": {"title": "Documents", "icon": "las la-file-alt", "menu_active": "admin.deals.documents*", "submenu": [{"title": "All Documents", "route_name": "admin.deals.index", "menu_active": "admin.deals.index"}]}, "emails": {"title": "Emails", "icon": "las la-envelope", "menu_active": "admin.emails*", "submenu": [{"title": "All Emails", "route_name": "admin.deals.index", "menu_active": "admin.deals.index"}]}, "updates": {"title": "Updates", "icon": "las la-sync", "menu_active": "admin.updates*", "submenu": [{"title": "Updates", "route_name": "admin.deals.index", "menu_active": "admin.deals.index"}, {"title": "KPIs", "route_name": "admin.deals.index", "menu_active": "admin.deals.index"}, {"title": "Valuation Forms", "route_name": "admin.deals.index", "menu_active": "admin.deals.index"}]}, "support_ticket": {"title": "Support Ticket", "icon": "la la-ticket", "counters": ["pendingTicketCount"], "menu_active": "admin.ticket*", "submenu": [{"keyword": ["Pending Ticket", "Support Ticket", "Ticket management", "Ticket control", "Ticket status", "Ticket activity"], "title": "Pending Ticket", "route_name": "admin.ticket.pending", "menu_active": "admin.ticket.pending", "counter": "pendingTicketCount"}, {"keyword": ["Closed Ticket", "Support Ticket", "Ticket management", "Ticket activity"], "title": "Closed Ticket", "route_name": "admin.ticket.closed", "menu_active": "admin.ticket.closed"}, {"keyword": ["Answered Ticket", "Support Ticket", "Ticket management", "Ticket activity"], "title": "Answered Ticket", "route_name": "admin.ticket.answered", "menu_active": "admin.ticket.answered"}, {"keyword": ["All Ticket", "Support Ticket", "Ticket management", "Ticket control", "Ticket activity"], "title": "All Ticket", "route_name": "admin.ticket.index", "menu_active": "admin.ticket.index"}]}, "reports": {"title": "Report", "icon": "la la-list", "menu_active": "admin.report*", "submenu": [{"keyword": ["Transaction Log", "Report", "Transaction report", "Transaction history", "Transaction activity", "balance sheet", "balance log", "balance history"], "title": "Transaction History", "route_name": "admin.report.transaction", "menu_active": ["admin.report.transaction", "admin.report.transaction.search"], "params": {"user_id": ""}}, {"keyword": ["Login History", "Report", "Login report", "Login history", "Login activity"], "title": "Login History", "route_name": "admin.report.login.history", "menu_active": ["admin.report.login.history", "admin.report.login.ipHistory"]}, {"keyword": ["Notification History", "Report", "Notification report", "Notification history", "Notification activity", "email log", "email history", "sms log", "sms history", "push notification log", "push notification history"], "title": "Notification History", "route_name": "admin.report.notification.history", "menu_active": "admin.report.notification.history"}]}, "subscriber": {"keyword": ["subscriber", "subscribers", "Subscription management", "Subscriber list", "Subscriber activity"], "title": "Subscribers", "icon": "las la-thumbs-up", "route_name": "admin.subscriber.index", "menu_active": "admin.subscriber.*"}, "referral": {"keyword": ["referral"], "title": "Manage Referral", "icon": "las la-tree", "route_name": "admin.referrals.index", "menu_active": "admin.referrals.index"}, "system_setting": {"keyword": ["System Setting", "setting", "System configuration", "System preferences", "Configuration management", "System setup"], "title": "System Setting", "icon": "las la-life-ring", "route_name": "admin.setting.system", "menu_active": ["admin.setting.system", "admin.setting.general", "admin.setting.socialite.credentials", "admin.cron*", "admin.setting.system.configuration", "admin.setting.logo.icon", "admin.extensions.index", "admin.language.manage", "admin.language.key", "admin.seo", "admin.kyc.setting", "admin.frontend.templates", "admin.frontend.manage.*", "admin.maintenance.mode", "admin.setting.cookie", "admin.setting.custom.css", "admin.setting.sitemap", "admin.setting.robot", "admin.setting.notification.global.email", "admin.setting.notification.global.sms", "admin.setting.notification.global.push", "admin.setting.notification.email", "admin.setting.notification.sms", "admin.setting.notification.push", "admin.setting.notification.templates", "admin.setting.notification.template.edit", "admin.frontend.index", "admin.frontend.sections*", "admin.gateway*", "admin.withdraw.method*", "admin.setting.app.purchase"]}, "extra": {"title": "Extra", "icon": "la la-server", "menu_active": ["admin.system*", "admin.backup*"], "counters": ["updateAvailable"], "submenu": [{"keyword": ["Application", "System", "Application management", "Application settings", "System information", "version", "laravel", "timezone"], "title": "Application", "route_name": "admin.system.info", "menu_active": "admin.system.info"}, {"keyword": ["Server", "System", "Server management", "Server settings", "System information", "version", "php version", "software", "ip address", "server ip address", "server port", "http host"], "title": "Server", "route_name": "admin.system.server.info", "menu_active": "admin.system.server.info"}, {"keyword": ["<PERSON><PERSON>", "System", "Cache management", "Cache optimization", "System performance", "clear cache"], "title": "<PERSON><PERSON>", "route_name": "admin.system.optimize", "menu_active": "admin.system.optimize"}, {"keyword": ["Update", "System", "Update management", "System update", "Software updates", "version update", "upgrade", "latest version"], "title": "Update", "route_name": "admin.system.update", "menu_active": "admin.system.update*", "counter": "updateAvailable"}, {"keyword": ["Backup", "System", "Backup management", "System backup", "Database backup", "Application backup", "Restore backup", "Download backup"], "title": "Backup", "route_name": "admin.backup.index", "menu_active": "admin.backup*"}]}, "report_and_request": {"keyword": ["Report & Request", "Report and Request", "Reports and Requests", "Reporting and Requests", "Report management", "Request management", "feature request", "bug report"], "title": "Report & Request", "icon": "las la-bug", "route_name": "admin.request.report", "menu_active": "admin.request.report"}}