# API Integrations Documentation

## Overview

The CRE Investment Platform includes a flexible API integration system that allows administrators to configure and manage external API connections. The system currently supports the Documenso E-Signature API, which enables electronic document signing functionality throughout the platform.

## API Integration Architecture

### Database Structure

API integrations are stored in the `api_integrations` table with the following structure:

- `id` - Primary key
- `name` - Display name of the API integration
- `code` - Unique identifier code for the integration
- `description` - Detailed description of the API's purpose
- `credentials` - JSON object storing API credentials (securely)
- `settings` - JSON object storing additional configuration settings
- `status` - Boolean flag indicating if the integration is active

### Model

The `ApiIntegration` model provides methods for accessing and managing API integrations:

```php
namespace App\Models;

use App\Traits\GlobalStatus;
use Illuminate\Database\Eloquent\Model;

class ApiIntegration extends Model
{
    use GlobalStatus;

    protected $guarded = ['id'];

    protected $casts = [
        'credentials' => 'object',
        'settings' => 'object',
    ];

    protected $hidden = ['credentials'];

    /**
     * Get a credential value by key
     *
     * @param string $key
     * @return mixed|null
     */
    public function getCredentialValue($key)
    {
        $credentials = json_decode(json_encode($this->credentials), true);
        return $credentials[$key]['value'] ?? null;
    }
}
```

### Controller

The `ApiIntegrationsController` handles the management of API integrations:

```php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ApiIntegration;
use Illuminate\Http\Request;

class ApiIntegrationsController extends Controller
{
    public function index()
    {
        $pageTitle = 'API Integrations';
        $apis = ApiIntegration::all();
        return view('admin.apis.index', compact('pageTitle', 'apis'));
    }
    
    public function edit($code)
    {
        $pageTitle = 'Edit API Integration';
        $api = ApiIntegration::where('code', $code)->firstOrFail();
        return view('admin.apis.edit', compact('pageTitle', 'api'));
    }
    
    public function update(Request $request, $code)
    {
        $api = ApiIntegration::where('code', $code)->firstOrFail();
        
        $credentials = json_decode(json_encode($api->credentials), true);
        
        foreach ($credentials as $key => $credential) {
            if ($request->has($key)) {
                $credentials[$key]['value'] = $request->$key;
            }
        }
        
        $api->credentials = $credentials;
        $api->status = $request->status ? 1 : 0;
        $api->save();
        
        $notify[] = ['success', $api->name . ' settings updated successfully'];
        return back()->withNotify($notify);
    }
}
```

### Routes

API integration routes are defined in `routes/admin.php`:

```php
// API Integrations
Route::name('other-apis.')->prefix('other-apis')->group(function () {
    Route::get('/', 'ApiIntegrationsController@index')->name('index');
    Route::get('/{code}/edit', 'ApiIntegrationsController@edit')->name('edit');
    Route::post('/{code}/update', 'ApiIntegrationsController@update')->name('update');
});
```

## Documenso E-Signature API

### Overview

The Documenso E-Signature API integration allows the platform to create, send, and manage electronic signature requests for documents. This integration is essential for investment agreements, contracts, and other legal documents that require signatures.

### Configuration

The Documenso API requires the following configuration:

- **API Key**: Your Documenso API key (format: api_xxxxxxxxxxxxxxxx)
- **API Base URL**: The base URL for the Documenso API (default: https://app.documenso.com/api/v1)

### Seeder

The Documenso API integration is initialized through a database seeder:

```php
namespace Database\Seeders;

use App\Models\ApiIntegration;
use Illuminate\Database\Seeder;

class DocumensoApiSeeder extends Seeder
{
    public function run()
    {
        // Define the credentials structure
        $credentials = [
            'api_key' => [
                'title' => 'API Key',
                'value' => '',
                'description' => 'Your Documenso API key (format: api_xxxxxxxxxxxxxxxx)',
                'type' => 'password'
            ],
            'api_url' => [
                'title' => 'API Base URL',
                'value' => 'https://app.documenso.com/api/v1',
                'description' => 'Base URL for Documenso API (e.g., https://your-domain.com/api/v1)',
                'type' => 'text'
            ]
        ];

        // Create or update the Documenso API integration
        ApiIntegration::updateOrCreate(
            ['code' => 'documenso'],
            [
                'name' => 'Documenso E-Signature',
                'description' => 'Integration with Documenso for electronic document signing',
                'credentials' => $credentials,
                'status' => 0
            ]
        );
    }
}
```

## User Interface

### API Integrations List

The API integrations list page (`/admin/other-apis`) displays all configured API integrations with their status and provides access to edit each integration.

### API Integration Edit

The API integration edit page (`/admin/other-apis/{code}/edit`) allows administrators to configure the API credentials and toggle the active status of the integration.

## Usage in the Application

To use the Documenso API in your code, you can access the API credentials as follows:

```php
// Get the Documenso API integration
$documenso = ApiIntegration::where('code', 'documenso')->first();

// Check if the integration is active
if ($documenso && $documenso->status) {
    // Get the API key
    $apiKey = $documenso->getCredentialValue('api_key');
    
    // Get the API base URL
    $apiUrl = $documenso->getCredentialValue('api_url');
    
    // Use the API credentials to make requests to Documenso
    // ...
}
```

## Security Considerations

- API keys are stored in the database and should be treated as sensitive information
- The `credentials` field in the `ApiIntegration` model is marked as `hidden` to prevent accidental exposure
- When displaying API keys in the UI, only a portion of the key is shown for security reasons
- Password fields are used for sensitive credential inputs to prevent shoulder surfing

## Troubleshooting

### Common Issues

1. **API Integration Not Working**
   - Verify that the API integration status is set to Active
   - Check that the API key is correctly entered
   - Ensure the API base URL is correct

2. **API Key Invalid**
   - Confirm that the API key format is correct (api_xxxxxxxxxxxxxxxx)
   - Verify that the API key is active in your Documenso account
   - Check for any whitespace or special characters that may have been accidentally included

## Future Improvements

- Add support for additional API integrations (payment gateways, KYC providers, etc.)
- Implement API request logging for debugging and auditing purposes
- Add API usage statistics and monitoring
- Create a test connection feature to verify API credentials before saving
