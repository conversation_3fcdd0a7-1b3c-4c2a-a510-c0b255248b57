# Role Seeder Implementation for Organizational Structure

## Overview

The `RoleAndPermissionSeeder` needs to be updated to create all organizational roles and assign appropriate permissions based on departmental responsibilities.

## Implementation Steps

### 1. Define Department-Specific Permissions

```php
// Create permissions based on organizational structure
$permissions = [
    // Executive level permissions
    'executive.view',
    'executive.manage',
    
    // Staff management
    'staff.view',
    'staff.create',
    'staff.edit',
    'staff.delete',
    
    // Role management
    'roles.view',
    'roles.create',
    'roles.edit',
    'roles.delete',
    
    // Deal management
    'deals.view',
    'deals.create',
    'deals.edit',
    'deals.delete',
    'deals.approve',
    'deals.underwrite',
    
    // Investment management
    'investments.view',
    'investments.create',
    'investments.edit',
    'investments.delete',
    'investments.approve',
    
    // Settings management
    'settings.view',
    'settings.edit',
    
    // User management
    'users.view',
    'users.create',
    'users.edit',
    'users.delete',
    
    // Asset management
    'assets.view',
    'assets.create',
    'assets.edit',
    'assets.delete',
    'assets.manage_metrics',
    
    // System management
    'system.view',
    'system.edit',
    
    // Accounting Department
    'accounting.view',
    'accounting.create',
    'accounting.edit',
    'accounting.delete',
    'accounting.approve_distributions',
    'accounting.manage_funding',
    
    // Partnerships Department
    'partnerships.view',
    'partnerships.create',
    'partnerships.edit',
    'partnerships.delete',
    'partnerships.approve',
    
    // Investor Relations Department
    'investor_relations.view',
    'investor_relations.create',
    'investor_relations.edit',
    'investor_relations.delete',
    
    // Marketing Department
    'marketing.view',
    'marketing.create',
    'marketing.edit',
    'marketing.delete',
    
    // General Management Department
    'general_management.view',
    'general_management.create',
    'general_management.edit',
    'general_management.delete',
    
    // Technology Department
    'technology.view',
    'technology.create',
    'technology.edit',
    'technology.delete',
    
    // Compliance & Legal Department
    'compliance_legal.view',
    'compliance_legal.create',
    'compliance_legal.edit',
    'compliance_legal.delete',
];
```

### 2. Create Organizational Roles with Appropriate Permissions

```php
// Create organizational roles
$roles = [
    'Super Admin' => Permission::where('guard_name', 'admin')->get(),
    'CEO' => Permission::where('guard_name', 'admin')
        ->whereNotIn('name', ['system.manage_super_admin'])
        ->get(),
    'CFO' => Permission::where('guard_name', 'admin')
        ->where(function($query) {
            $query->where('name', 'like', 'accounting.%')
                  ->orWhere('name', 'like', 'investments.view')
                  ->orWhere('name', 'like', 'deals.view')
                  ->orWhere('name', 'like', 'assets.view');
        })->get(),
    'COO' => Permission::where('guard_name', 'admin')
        ->where(function($query) {
            $query->where('name', 'like', 'deals.%')
                  ->orWhere('name', 'like', 'assets.%')
                  ->orWhere('name', 'like', 'partnerships.%')
                  ->orWhere('name', 'like', 'investor_relations.%')
                  ->orWhere('name', 'like', 'marketing.%')
                  ->orWhere('name', 'like', 'general_management.%');
        })->get(),
    'CTO' => Permission::where('guard_name', 'admin')
        ->where(function($query) {
            $query->where('name', 'like', 'technology.%')
                  ->orWhere('name', 'like', 'system.%');
        })->get(),
    'CLO' => Permission::where('guard_name', 'admin')
        ->where('name', 'like', 'compliance_legal.%')
        ->get(),
    'Accounting Manager' => Permission::where('guard_name', 'admin')
        ->where('name', 'like', 'accounting.%')
        ->get(),
    'Deals Manager' => Permission::where('guard_name', 'admin')
        ->where('name', 'like', 'deals.%')
        ->get(),
    'Asset Manager' => Permission::where('guard_name', 'admin')
        ->where('name', 'like', 'assets.%')
        ->get(),
    'Partnerships Manager' => Permission::where('guard_name', 'admin')
        ->where('name', 'like', 'partnerships.%')
        ->get(),
    'Investor Relations Manager' => Permission::where('guard_name', 'admin')
        ->where('name', 'like', 'investor_relations.%')
        ->get(),
    'Marketing Manager' => Permission::where('guard_name', 'admin')
        ->where('name', 'like', 'marketing.%')
        ->get(),
    'General Management Manager' => Permission::where('guard_name', 'admin')
        ->where('name', 'like', 'general_management.%')
        ->get(),
    'Technology Manager' => Permission::where('guard_name', 'admin')
        ->where('name', 'like', 'technology.%')
        ->get(),
    'Compliance & Legal Manager' => Permission::where('guard_name', 'admin')
        ->where('name', 'like', 'compliance_legal.%')
        ->get(),
    'Staff' => Permission::where('guard_name', 'admin')
        ->where(function($query) {
            $query->where('name', 'like', '%.view');
        })->get(),
];

foreach ($roles as $roleName => $rolePermissions) {
    $role = Role::findOrCreate($roleName, 'admin');
    $role->syncPermissions($rolePermissions);
}
```

### 3. Maintain Backward Compatibility

To ensure backward compatibility with existing code:

1. Keep the existing role names ('Super Admin', 'Staff', 'Manager')
2. Maintain the existing permission structure (e.g., 'deals.view', 'investments.create')
3. Add new organizational roles and permissions without removing existing ones

### 4. Running the Seeder

After implementing the seeder, run it with:

```bash
php artisan db:seed --class=RoleAndPermissionSeeder
```

This will:
1. Clear existing roles and permissions
2. Create all new permissions
3. Create organizational roles with appropriate permissions
4. Maintain the 'user' role for frontend users
