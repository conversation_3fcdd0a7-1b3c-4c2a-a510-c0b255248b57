# Loopnet ExtensionPlus Module Implementation Guide

## 1. Overview

The Loopnet ExtensionPlus module integrates the Loopnet commercial real estate listings API with the CRE Investment Platform, allowing administrators to search for properties, view details, and import listings as deals and offerings.

## 2. Features

- Search Loopnet listings with filters (location, property type, price range, etc.)
- View detailed property information and images
- Import listings as deals and offerings
- Automatically create assets with property details
- Track imported listings and their status

## 3. Implementation

### 3.1 Directory Structure

```
extensions/extensionplus/
  └── loopnet/
      ├── assets/
      │   ├── css/
      │   │   └── loopnet.css
      │   ├── js/
      │   │   └── loopnet.js
      │   └── images/
      │       └── loopnet-logo.png
      ├── src/
      │   ├── Controllers/
      │   │   ├── LoopnetController.php
      │   │   ├── SearchController.php
      │   │   └── ImportController.php
      │   ├── Models/
      │   │   ├── LoopnetListing.php
      │   │   └── LoopnetSearch.php
      │   ├── Services/
      │   │   └── LoopnetApiClient.php
      │   └── Providers/
      │       └── LoopnetServiceProvider.php
      ├── views/
      │   ├── index.blade.php
      │   ├── search.blade.php
      │   ├── listings.blade.php
      │   ├── view.blade.php
      │   └── import.blade.php
      ├── routes.php
      └── manifest.json
```

### 3.2 Database Schema

#### Loopnet Listings Table

```php
Schema::create('extensionplus_loopnet_listings', function (Blueprint $table) {
    $table->id();
    $table->string('loopnet_id')->unique();
    $table->string('title');
    $table->text('description')->nullable();
    $table->string('property_type');
    $table->string('status');
    $table->decimal('price', 15, 2)->nullable();
    $table->decimal('price_per_sqft', 10, 2)->nullable();
    $table->integer('building_size')->nullable();
    $table->integer('lot_size')->nullable();
    $table->string('address');
    $table->string('city');
    $table->string('state');
    $table->string('zip');
    $table->decimal('latitude', 10, 7)->nullable();
    $table->decimal('longitude', 10, 7)->nullable();
    $table->text('features')->nullable(); // JSON
    $table->text('images')->nullable(); // JSON
    $table->text('documents')->nullable(); // JSON
    $table->text('contacts')->nullable(); // JSON
    $table->unsignedBigInteger('deal_id')->nullable();
    $table->unsignedBigInteger('offering_id')->nullable();
    $table->unsignedBigInteger('asset_id')->nullable();
    $table->timestamp('imported_at')->nullable();
    $table->timestamps();
    
    $table->foreign('deal_id')->references('id')->on('deals')->onDelete('set null');
    $table->foreign('offering_id')->references('id')->on('offerings')->onDelete('set null');
    $table->foreign('asset_id')->references('id')->on('assets')->onDelete('set null');
});
```

#### Loopnet Searches Table

```php
Schema::create('extensionplus_loopnet_searches', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->text('parameters'); // JSON
    $table->unsignedBigInteger('user_id');
    $table->timestamps();
    
    $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
});
```

### 3.3 Models

#### LoopnetListing Model

```php
namespace ExtensionPlus\Loopnet\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Deal;
use App\Models\Offering;
use App\Models\Asset;

class LoopnetListing extends Model
{
    protected $table = 'extensionplus_loopnet_listings';
    
    protected $fillable = [
        'loopnet_id', 'title', 'description', 'property_type', 'status',
        'price', 'price_per_sqft', 'building_size', 'lot_size',
        'address', 'city', 'state', 'zip', 'latitude', 'longitude',
        'features', 'images', 'documents', 'contacts',
        'deal_id', 'offering_id', 'asset_id', 'imported_at'
    ];
    
    protected $casts = [
        'features' => 'array',
        'images' => 'array',
        'documents' => 'array',
        'contacts' => 'array',
        'imported_at' => 'datetime',
    ];
    
    public function deal()
    {
        return $this->belongsTo(Deal::class);
    }
    
    public function offering()
    {
        return $this->belongsTo(Offering::class);
    }
    
    public function asset()
    {
        return $this->belongsTo(Asset::class);
    }
}
```

#### LoopnetSearch Model

```php
namespace ExtensionPlus\Loopnet\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\User;

class LoopnetSearch extends Model
{
    protected $table = 'extensionplus_loopnet_searches';
    
    protected $fillable = ['name', 'parameters', 'user_id'];
    
    protected $casts = [
        'parameters' => 'array',
    ];
    
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
```

### 3.4 Services

#### LoopnetApiClient Service

```php
namespace ExtensionPlus\Loopnet\Services;

use Illuminate\Support\Facades\Http;
use ExtensionPlus\Loopnet\Models\LoopnetListing;

class LoopnetApiClient
{
    protected $apiKey;
    protected $apiSecret;
    protected $baseUrl = 'https://api.loopnet.com/v1';
    
    public function __construct()
    {
        $extensionPlus = app('extensionplus.registry')->get('loopnet');
        $settings = json_decode($extensionPlus->settings, true);
        
        $this->apiKey = $settings['api_key'] ?? null;
        $this->apiSecret = $settings['api_secret'] ?? null;
        
        if (!$this->apiKey || !$this->apiSecret) {
            throw new \Exception('Loopnet API credentials not configured');
        }
    }
    
    public function search(array $parameters)
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->getAccessToken(),
            'Content-Type' => 'application/json',
        ])->post($this->baseUrl . '/properties/search', $parameters);
        
        if ($response->failed()) {
            throw new \Exception('Loopnet API search failed: ' . $response->body());
        }
        
        return $response->json();
    }
    
    public function getProperty($id)
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->getAccessToken(),
            'Content-Type' => 'application/json',
        ])->get($this->baseUrl . '/properties/' . $id);
        
        if ($response->failed()) {
            throw new \Exception('Loopnet API property fetch failed: ' . $response->body());
        }
        
        return $response->json();
    }
    
    protected function getAccessToken()
    {
        // Implementation of OAuth token retrieval
        // This is a simplified example
        $response = Http::post('https://api.loopnet.com/oauth/token', [
            'grant_type' => 'client_credentials',
            'client_id' => $this->apiKey,
            'client_secret' => $this->apiSecret,
        ]);
        
        if ($response->failed()) {
            throw new \Exception('Failed to get Loopnet access token: ' . $response->body());
        }
        
        return $response->json()['access_token'];
    }
}
```

### 3.5 Controllers

#### LoopnetController

```php
namespace ExtensionPlus\Loopnet\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use ExtensionPlus\Loopnet\Models\LoopnetListing;

class LoopnetController extends Controller
{
    public function index()
    {
        $pageTitle = 'Loopnet Integration';
        $recentListings = LoopnetListing::latest()->take(5)->get();
        
        return view('extensionplus.loopnet.index', compact('pageTitle', 'recentListings'));
    }
    
    public function listings()
    {
        $pageTitle = 'Loopnet Listings';
        $listings = LoopnetListing::latest()->paginate(20);
        
        return view('extensionplus.loopnet.listings', compact('pageTitle', 'listings'));
    }
    
    public function view($id)
    {
        $listing = LoopnetListing::findOrFail($id);
        $pageTitle = 'View Listing: ' . $listing->title;
        
        return view('extensionplus.loopnet.view', compact('pageTitle', 'listing'));
    }
    
    public function settings()
    {
        $pageTitle = 'Loopnet Settings';
        $extensionPlus = app('extensionplus.registry')->get('loopnet');
        $settings = json_decode($extensionPlus->settings, true);
        
        return view('extensionplus.loopnet.settings', compact('pageTitle', 'settings'));
    }
    
    public function saveSettings(Request $request)
    {
        $extensionPlus = app('extensionplus.registry')->get('loopnet');
        $extensionPlus->settings = $request->settings;
        $extensionPlus->save();
        
        $notify[] = ['success', 'Loopnet settings updated successfully'];
        return back()->withNotify($notify);
    }
}
```

#### SearchController

```php
namespace ExtensionPlus\Loopnet\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use ExtensionPlus\Loopnet\Services\LoopnetApiClient;
use ExtensionPlus\Loopnet\Models\LoopnetSearch;
use ExtensionPlus\Loopnet\Models\LoopnetListing;

class SearchController extends Controller
{
    protected $loopnetApi;
    
    public function __construct(LoopnetApiClient $loopnetApi)
    {
        $this->loopnetApi = $loopnetApi;
    }
    
    public function index()
    {
        $pageTitle = 'Search Loopnet Listings';
        $savedSearches = LoopnetSearch::where('user_id', auth()->id())->latest()->get();
        
        return view('extensionplus.loopnet.search', compact('pageTitle', 'savedSearches'));
    }
    
    public function search(Request $request)
    {
        $request->validate([
            'location' => 'required',
            'property_type' => 'required',
        ]);
        
        // Build search parameters
        $parameters = [
            'location' => $request->location,
            'propertyType' => $request->property_type,
            'priceMin' => $request->price_min,
            'priceMax' => $request->price_max,
            'sizeMin' => $request->size_min,
            'sizeMax' => $request->size_max,
            // Additional parameters...
        ];
        
        // Save search if requested
        if ($request->save_search && $request->search_name) {
            LoopnetSearch::create([
                'name' => $request->search_name,
                'parameters' => $parameters,
                'user_id' => auth()->id(),
            ]);
        }
        
        // Perform search
        $results = $this->loopnetApi->search($parameters);
        
        return view('extensionplus.loopnet.search_results', [
            'pageTitle' => 'Search Results',
            'results' => $results['properties'] ?? [],
            'totalResults' => $results['totalResults'] ?? 0,
        ]);
    }
    
    public function loadSearch($id)
    {
        $search = LoopnetSearch::where('user_id', auth()->id())->findOrFail($id);
        
        return view('extensionplus.loopnet.search', [
            'pageTitle' => 'Search: ' . $search->name,
            'savedSearch' => $search,
            'savedSearches' => LoopnetSearch::where('user_id', auth()->id())->latest()->get(),
        ]);
    }
    
    public function import($id)
    {
        try {
            $propertyData = $this->loopnetApi->getProperty($id);
            
            // Create or update listing
            $listing = LoopnetListing::updateOrCreate(
                ['loopnet_id' => $id],
                [
                    'title' => $propertyData['name'],
                    'description' => $propertyData['description'] ?? null,
                    'property_type' => $propertyData['propertyType'],
                    'status' => $propertyData['status'],
                    'price' => $propertyData['price'] ?? null,
                    'price_per_sqft' => $propertyData['pricePerSqFt'] ?? null,
                    'building_size' => $propertyData['buildingSize'] ?? null,
                    'lot_size' => $propertyData['lotSize'] ?? null,
                    'address' => $propertyData['address']['street'],
                    'city' => $propertyData['address']['city'],
                    'state' => $propertyData['address']['state'],
                    'zip' => $propertyData['address']['zip'],
                    'latitude' => $propertyData['location']['latitude'] ?? null,
                    'longitude' => $propertyData['location']['longitude'] ?? null,
                    'features' => $propertyData['features'] ?? null,
                    'images' => $propertyData['images'] ?? null,
                    'documents' => $propertyData['documents'] ?? null,
                    'contacts' => $propertyData['contacts'] ?? null,
                ]
            );
            
            $notify[] = ['success', 'Listing imported successfully'];
            return redirect()->route('admin.extensionplus.loopnet.listings.view', $listing->id)->withNotify($notify);
            
        } catch (\Exception $e) {
            $notify[] = ['error', 'Error importing listing: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }
}
```

#### ImportController

```php
namespace ExtensionPlus\Loopnet\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use ExtensionPlus\Loopnet\Models\LoopnetListing;
use App\Models\Deal;
use App\Models\Offering;
use App\Models\Asset;
use Illuminate\Support\Str;

class ImportController extends Controller
{
    public function wizard($id)
    {
        $pageTitle = 'Import Listing';
        $listing = LoopnetListing::findOrFail($id);
        
        return view('extensionplus.loopnet.import', compact('pageTitle', 'listing'));
    }
    
    public function process(Request $request, $id)
    {
        $listing = LoopnetListing::findOrFail($id);
        
        // Create Deal
        $deal = Deal::create([
            'name' => $request->deal_name ?? $listing->title,
            'description' => $request->deal_description ?? $listing->description,
            'status' => $request->deal_status ?? 'active',
            'deal_type' => $request->deal_type ?? $this->mapPropertyTypeToDealType($listing->property_type),
            // Additional fields...
        ]);
        
        // Create Offering
        $offering = Offering::create([
            'deal_id' => $deal->id,
            'name' => $request->offering_name ?? $listing->title . ' Offering',
            'description' => $request->offering_description ?? $listing->description,
            'status' => $request->offering_status ?? 'active',
            'min_investment' => $request->min_investment ?? 0,
            'target_raise' => $request->target_raise ?? ($listing->price ?? 0),
            // Additional fields...
        ]);
        
        // Create Asset
        $asset = Asset::create([
            'deal_id' => $deal->id,
            'name' => $request->asset_name ?? $listing->title,
            'description' => $request->asset_description ?? $listing->description,
            'address' => $listing->address,
            'city' => $listing->city,
            'state' => $listing->state,
            'zip' => $listing->zip,
            'latitude' => $listing->latitude,
            'longitude' => $listing->longitude,
            'property_type' => $this->mapPropertyTypeToAssetType($listing->property_type),
            'size' => $listing->building_size,
            // Additional fields...
        ]);
        
        // Import images if selected
        if ($request->has('selected_images') && is_array($request->selected_images)) {
            $this->importImages($listing, $asset, $request->selected_images);
        }
        
        // Update listing with references
        $listing->update([
            'deal_id' => $deal->id,
            'offering_id' => $offering->id,
            'asset_id' => $asset->id,
            'imported_at' => now(),
        ]);
        
        $notify[] = ['success', 'Listing imported successfully as a deal, offering, and asset'];
        return redirect()->route('admin.deals.edit', $deal->id)->withNotify($notify);
    }
    
    protected function mapPropertyTypeToDealType($propertyType)
    {
        $map = [
            'Office' => 'office',
            'Retail' => 'retail',
            'Industrial' => 'industrial',
            'MultiFamily' => 'multifamily',
            'Land' => 'land',
            // Additional mappings...
        ];
        
        return $map[$propertyType] ?? 'other';
    }
    
    protected function mapPropertyTypeToAssetType($propertyType)
    {
        // Similar mapping logic as above
        return $this->mapPropertyTypeToDealType($propertyType);
    }
    
    protected function importImages($listing, $asset, $selectedImages)
    {
        if (!is_array($listing->images)) {
            return;
        }
        
        foreach ($listing->images as $index => $image) {
            if (!in_array($index, $selectedImages)) {
                continue;
            }
            
            $imageUrl = $image['url'];
            $imageName = Str::slug($listing->title) . '-' . ($index + 1) . '.jpg';
            $imagePath = 'assets/' . $imageName;
            
            // Download and store image
            $imageContent = file_get_contents($imageUrl);
            \Storage::put('public/' . $imagePath, $imageContent);
            
            // Create asset media
            $asset->media()->create([
                'type' => 'image',
                'path' => $imagePath,
                'title' => $listing->title . ' Image ' . ($index + 1),
            ]);
        }
    }
}
```

### 3.6 Routes

```php
Route::group([
    'prefix' => 'admin/extensionplus/loopnet',
    'as' => 'admin.extensionplus.loopnet.',
    'middleware' => ['auth', 'admin'],
], function () {
    // Main routes
    Route::get('/', 'LoopnetController@index')->name('index');
    Route::get('/listings', 'LoopnetController@listings')->name('listings');
    Route::get('/listings/{id}', 'LoopnetController@view')->name('listings.view');
    
    // Search routes
    Route::get('/search', 'SearchController@index')->name('search');
    Route::post('/search', 'SearchController@search')->name('search.post');
    Route::get('/search/{id}', 'SearchController@loadSearch')->name('search.load');
    Route::get('/search/import/{id}', 'SearchController@import')->name('search.import');
    
    // Import routes
    Route::get('/listings/{id}/import', 'ImportController@wizard')->name('listings.import');
    Route::post('/listings/{id}/import', 'ImportController@process')->name('listings.import.process');
    
    // Settings routes
    Route::get('/settings', 'LoopnetController@settings')->name('settings');
    Route::post('/settings', 'LoopnetController@saveSettings')->name('settings.save');
});
```

### 3.7 Service Provider

```php
namespace ExtensionPlus\Loopnet\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use ExtensionPlus\Loopnet\Services\LoopnetApiClient;

class LoopnetServiceProvider extends ServiceProvider
{
    public function register()
    {
        // Register Loopnet API client
        $this->app->singleton('loopnet.api', function ($app) {
            return new LoopnetApiClient();
        });
    }
    
    public function boot()
    {
        // Load migrations
        $this->loadMigrationsFrom(__DIR__.'/../Database/Migrations');
        
        // Load views
        $this->loadViewsFrom(__DIR__.'/../views', 'extensionplus.loopnet');
        
        // Load routes
        $this->loadRoutesFrom(__DIR__.'/../routes.php');
        
        // Register config
        $this->mergeConfigFrom(
            __DIR__.'/../Config/loopnet.php', 'extensionplus.loopnet'
        );
        
        // Publish assets
        $this->publishes([
            __DIR__.'/../assets' => public_path('extensionplus/loopnet'),
        ], 'extensionplus-loopnet-assets');
        
        // Register admin sidebar menu item
        if ($this->app->bound('extensionplus.hooks')) {
            app('extensionplus.hooks')->register('admin.sidebar', function ($menu) {
                return $menu->item('Loopnet', [
                    'icon' => 'las la-building',
                    'route' => 'admin.extensionplus.loopnet.index',
                    'order' => 100,
                    'submenu' => [
                        'Search' => [
                            'icon' => 'las la-search',
                            'route' => 'admin.extensionplus.loopnet.search',
                        ],
                        'Listings' => [
                            'icon' => 'las la-list',
                            'route' => 'admin.extensionplus.loopnet.listings',
                        ],
                        'Settings' => [
                            'icon' => 'las la-cog',
                            'route' => 'admin.extensionplus.loopnet.settings',
                        ],
                    ],
                ]);
            });
        }
    }
}
```

### 3.8 Module Manifest

The `manifest.json` file defines the module's metadata and configuration:

```json
{
  "code": "loopnet",
  "name": "Loopnet Integration",
  "description": "Integrate Loopnet commercial real estate listings with the CRE Investment Platform",
  "version": "1.0.0",
  "author": "CRE Investment Platform",
  "author_url": "https://www.creinvestmentplatform.com",
  "requires": {
    "platform": "^1.0.0",
    "php": "^8.0",
    "extensions": []
  },
  "settings": [
    {
      "name": "api_key",
      "label": "API Key",
      "type": "text",
      "required": true
    },
    {
      "name": "api_secret",
      "label": "API Secret",
      "type": "password",
      "required": true
    },
    {
      "name": "default_search_radius",
      "label": "Default Search Radius (miles)",
      "type": "number",
      "default": 25
    },
    {
      "name": "results_per_page",
      "label": "Results Per Page",
      "type": "number",
      "default": 20
    }
  ],
  "providers": [
    "ExtensionPlus\\Loopnet\\Providers\\LoopnetServiceProvider"
  ],
  "aliases": {
    "Loopnet": "ExtensionPlus\\Loopnet\\Facades\\Loopnet"
  }
}
```

## 4. User Interface

### 4.1 Main Dashboard

The main dashboard provides an overview of the Loopnet integration, showing recent listings and search statistics.

### 4.2 Search Interface

The search interface allows users to find properties using various criteria:

- Location (address, city, state, zip)
- Property type (office, retail, industrial, multifamily, etc.)
- Price range
- Building size
- Lot size
- Keywords

Users can save searches for future use and view results in a list or map view.

### 4.3 Listing Details

The listing details page displays comprehensive information about a property:

- Basic information (title, price, size, etc.)
- Description
- Features
- Images gallery
- Location map
- Contact information
- Documents
- Import options

### 4.4 Import Wizard

The import wizard guides users through the process of creating platform entities from a Loopnet listing:

1. Review listing information
2. Configure deal settings
3. Configure offering settings
4. Configure asset settings
5. Select images to import
6. Confirm and process import

### 4.5 Settings

The settings page allows administrators to configure the Loopnet integration:

- API credentials
- Default search parameters
- Display preferences
- Import defaults

## 5. Installation

1. Download the Loopnet module zip package
2. Go to Admin > ExtensionPlus
3. Click "Import Module" and select the downloaded zip file
4. After installation, configure the module with your Loopnet API credentials

## 6. Configuration

After installation, navigate to the module settings page to configure:

1. Loopnet API Key and Secret
2. Default search parameters
3. Results display preferences
4. Import settings

## 7. Usage

### 7.1 Searching for Properties

1. Navigate to Admin > ExtensionPlus > Loopnet > Search
2. Enter search criteria (location, property type, price range, etc.)
3. Click "Search" to view results
4. Optionally save the search for future use

### 7.2 Viewing Listings

1. Navigate to Admin > ExtensionPlus > Loopnet > Listings
2. Browse the list of imported listings
3. Click on a listing to view details

### 7.3 Importing a Listing

1. From the listing details page, click "Import"
2. Follow the import wizard steps
3. Review and confirm the import
4. Access the created deal, offering, and asset in their respective sections

## 8. Conclusion

The Loopnet ExtensionPlus module provides a powerful integration between the CRE Investment Platform and Loopnet's commercial real estate listings. By implementing the features described in this guide, administrators can seamlessly search for properties, view detailed information, and import listings as deals, offerings, and assets with just a few clicks.

The module's modular architecture ensures easy maintenance and future expansion, while the comprehensive user interface provides an intuitive experience for platform administrators. The streamlined import process saves time and reduces errors by automatically creating the necessary platform entities from Loopnet listing data.

For further assistance or customization, please refer to the ExtensionPlus System documentation or contact the platform support team.
