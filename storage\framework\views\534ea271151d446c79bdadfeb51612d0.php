<?php
    $offeringContent  = getContent('latest_offerings.content', true);
    $latestOfferings = App\Models\Offering::with('deal')
        ->where('public_offering', true)
        ->whereHas('manageoffering', function($query) {
            $query->where('display_offering', true);
        })
        ->orderByDesc('created_at')
        ->limit(4)
        ->get();
?>


<section class="latest-property py-120 bg-pattern bg-pattern-bottom-right">
    <div class="container ">
        <div class="section-heading style-left">
            <p class="section-heading__subtitle">Latest Deals</p>
            <div class="section-heading__wrapper">
                <h2 class="section-heading__title">Explore Latest Deals</h2>
                <a class="section-heading__link" href="<?php echo e(route('deals')); ?>">
                    <span><?php echo app('translator')->get('Explore'); ?></span>
                    <i class="las la-long-arrow-alt-right"></i>
                </a>
            </div>
        </div>
        <div class="row gy-4 g-sm-3 g-md-4 justify-content-center">
            <?php echo $__env->make($activeTemplate . 'partials.explore_all_deals', ['offerings' => @$latestOfferings, 'col' => '4'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>
</section>

<script>
    
</script><?php /**PATH C:\laragon\www\PLATFORM\resources\views/templates/basic/sections/explore_deals.blade.php ENDPATH**/ ?>