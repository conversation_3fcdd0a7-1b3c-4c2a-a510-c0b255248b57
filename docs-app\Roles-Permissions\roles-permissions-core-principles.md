# Core Principles for Roles and Permissions Implementation

## Fundamental Approach

> **IMPORTANT**: Roles and permissions are there to interact with existing modules and not the other way around. We want to ensure the Staff are created and assigned specific permissions on modules and features. The modules should NOT be changed to adjust to the roles and permissions structure.

This core principle guides our entire implementation strategy. The roles and permissions system must adapt to the existing architecture, not vice versa.

## Implementation Guidelines

### 1. Adapt to Existing Architecture

- **DO**: Create roles and permissions that map to existing functionality
- **DO NOT**: Modify controllers, models, or business logic to accommodate new permission structures
- **DO**: Use middleware to interpret permissions in the context of organizational hierarchy
- **DO NOT**: Change route definitions or module behavior to fit the permission system

### 2. Permission Naming Conventions

- Follow existing naming patterns (e.g., `module.action`)
- Map new permissions directly to existing routes and functions
- Maintain backward compatibility with existing permission checks

### 3. Role Assignment

- Assign permissions to roles based on organizational structure
- Ensure roles have appropriate access to existing functionality
- Maintain the Super Admin role's ability to bypass all permission checks

### 4. UI Enhancements

- Improve organization and display of existing permissions
- Group permissions by department or function
- Do not require changes to module interfaces or functionality

### 5. Testing Approach

- Verify that existing functionality works with new permission structure
- Ensure backward compatibility with existing roles
- Test that permission checks are correctly enforced

## Practical Examples

### Correct Approach

```php
// Adding a new permission to an existing role
$role = Role::findByName('Accounting Manager', 'admin');
$role->givePermissionTo('accounting.view', 'accounting.create');

// Enhancing middleware to check hierarchical permissions
public function handle($request, Closure $next, $permission)
{
    // Check if user has the permission or is in a role that should have access
    if ($user->hasRole('Super Admin', 'admin') || $this->hasHierarchicalPermission($user, $permission)) {
        return $next($request);
    }
    
    throw new UnauthorizedException(403, 'You do not have the required permission to access this resource.');
}
```

### Incorrect Approach

```php
// DON'T modify controllers to check for specific roles
public function index()
{
    // Don't do this - don't modify module logic for roles
    if (auth()->user()->hasRole('Accounting Manager')) {
        // Special logic for Accounting Manager
    } else {
        // Different logic for others
    }
}

// DON'T change route definitions to accommodate roles
Route::get('/accounting', [AccountingController::class, 'index'])
    ->middleware('role:Accounting Manager'); // Don't do this
```

## Summary

The roles and permissions implementation must be non-invasive to existing functionality. We are creating a permission structure that reflects the organizational hierarchy while maintaining compatibility with all existing modules and features. The system should be flexible enough to accommodate future changes to the organizational structure without requiring modifications to the underlying modules.
