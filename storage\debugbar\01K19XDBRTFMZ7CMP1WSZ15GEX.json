{"__meta": {"id": "01K19XDBRTFMZ7CMP1WSZ15GEX", "datetime": "2025-07-28 21:40:35", "utime": **********.227998, "method": "GET", "uri": "/user/dashboard", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753753232.643256, "end": **********.228025, "duration": 2.5847690105438232, "duration_str": "2.58s", "measures": [{"label": "Booting", "start": 1753753232.643256, "relative_start": 0, "end": **********.193673, "relative_end": **********.193673, "duration": 0.****************, "duration_str": "550ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.19369, "relative_start": 0.****************, "end": **********.228029, "relative_end": 4.0531158447265625e-06, "duration": 2.**************, "duration_str": "2.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.23971, "relative_start": 0.****************, "end": **********.253135, "relative_end": **********.253135, "duration": 0.013424873352050781, "duration_str": "13.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.475598, "relative_start": 0.****************, "end": **********.2237, "relative_end": **********.2237, "duration": 1.****************, "duration_str": "1.75s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: Template::user.dashboard", "start": **********.480463, "relative_start": 0.****************, "end": **********.480463, "relative_end": **********.480463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.layouts.master", "start": **********.312959, "relative_start": 1.****************, "end": **********.312959, "relative_end": **********.312959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.partials.header", "start": **********.432364, "relative_start": 1.7891080379486084, "end": **********.432364, "relative_end": **********.432364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.partials.language", "start": **********.44222, "relative_start": 1.798964023590088, "end": **********.44222, "relative_end": **********.44222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.partials.language", "start": **********.452611, "relative_start": 1.8093550205230713, "end": **********.452611, "relative_end": **********.452611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.partials.sidenav", "start": **********.458623, "relative_start": 1.8153669834136963, "end": **********.458623, "relative_end": **********.458623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.partials.footer", "start": **********.149715, "relative_start": 2.5064589977264404, "end": **********.149715, "relative_end": **********.149715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.layouts.app", "start": **********.188529, "relative_start": 2.5452730655670166, "end": **********.188529, "relative_end": **********.188529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.seo", "start": **********.196997, "relative_start": 2.553740978240967, "end": **********.196997, "relative_end": **********.196997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.notify", "start": **********.219935, "relative_start": 2.576678991317749, "end": **********.219935, "relative_end": **********.219935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 30803000, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.7", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/New_York", "Locale": "en"}}, "views": {"count": 10, "nb_templates": 10, "templates": [{"name": "Template::user.dashboard", "param_count": null, "params": [], "start": **********.480339, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/user/dashboard.blade.phpTemplate::user.dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fuser%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}}, {"name": "templates.basic.layouts.master", "param_count": null, "params": [], "start": **********.312842, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/layouts/master.blade.phptemplates.basic.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}}, {"name": "templates.basic.partials.header", "param_count": null, "params": [], "start": **********.432261, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/partials/header.blade.phptemplates.basic.partials.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "templates.basic.partials.language", "param_count": null, "params": [], "start": **********.442121, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/partials/language.blade.phptemplates.basic.partials.language", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Flanguage.blade.php&line=1", "ajax": false, "filename": "language.blade.php", "line": "?"}}, {"name": "templates.basic.partials.language", "param_count": null, "params": [], "start": **********.452523, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/partials/language.blade.phptemplates.basic.partials.language", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Flanguage.blade.php&line=1", "ajax": false, "filename": "language.blade.php", "line": "?"}}, {"name": "templates.basic.partials.sidenav", "param_count": null, "params": [], "start": **********.458529, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/partials/sidenav.blade.phptemplates.basic.partials.sidenav", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fsidenav.blade.php&line=1", "ajax": false, "filename": "sidenav.blade.php", "line": "?"}}, {"name": "templates.basic.partials.footer", "param_count": null, "params": [], "start": **********.149552, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/partials/footer.blade.phptemplates.basic.partials.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "templates.basic.layouts.app", "param_count": null, "params": [], "start": **********.188416, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/layouts/app.blade.phptemplates.basic.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "partials.seo", "param_count": null, "params": [], "start": **********.196874, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/partials/seo.blade.phppartials.seo", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fpartials%2Fseo.blade.php&line=1", "ajax": false, "filename": "seo.blade.php", "line": "?"}}, {"name": "partials.notify", "param_count": null, "params": [], "start": **********.219832, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/partials/notify.blade.phppartials.notify", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fpartials%2Fnotify.blade.php&line=1", "ajax": false, "filename": "notify.blade.php", "line": "?"}}]}, "queries": {"count": 27, "nb_statements": 26, "nb_visible_statements": 27, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.07687000000000001, "accumulated_duration_str": "76.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 193}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.309148, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:193", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 193}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=193", "ajax": false, "filename": "EloquentUserProvider.php", "line": "193"}, "connection": "main", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3281982, "duration": 0.03417, "duration_str": "34.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "main", "explain": null, "start_percent": 0, "width_percent": 44.452}, {"sql": "select sum(`amount`) as aggregate from `deposits` where `user_id` = 1 and `status` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.3829162, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "UserController.php:33", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=33", "ajax": false, "filename": "UserController.php", "line": "33"}, "connection": "main", "explain": null, "start_percent": 44.452, "width_percent": 4.93}, {"sql": "select sum(`amount`) as aggregate from `withdrawals` where `user_id` = 1 and `status` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.391737, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "UserController.php:34", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=34", "ajax": false, "filename": "UserController.php", "line": "34"}, "connection": "main", "explain": null, "start_percent": 49.382, "width_percent": 1.665}, {"sql": "select sum(`paid_amount`) as aggregate from `invests` where `user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.397678, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "UserController.php:35", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=35", "ajax": false, "filename": "UserController.php", "line": "35"}, "connection": "main", "explain": null, "start_percent": 51.047, "width_percent": 1.886}, {"sql": "select sum(`total_profit`) as aggregate from `invests` where `user_id` = 1 and `invest_status` = 2", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.402616, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "UserController.php:36", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=36", "ajax": false, "filename": "UserController.php", "line": "36"}, "connection": "main", "explain": null, "start_percent": 52.934, "width_percent": 1.418}, {"sql": "select count(*) as aggregate from `invests` where `user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.4070928, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "UserController.php:37", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=37", "ajax": false, "filename": "UserController.php", "line": "37"}, "connection": "main", "explain": null, "start_percent": 54.352, "width_percent": 1.249}, {"sql": "select count(*) as aggregate from `users` where `ref_by` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 38}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.4115942, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "UserController.php:38", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=38", "ajax": false, "filename": "UserController.php", "line": "38"}, "connection": "main", "explain": null, "start_percent": 55.6, "width_percent": 1.314}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `user_id` = 1 and `remark` = 'referral_commission'", "type": "query", "params": [], "bindings": [1, "referral_commission"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.416721, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "UserController.php:39", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=39", "ajax": false, "filename": "UserController.php", "line": "39"}, "connection": "main", "explain": null, "start_percent": 56.914, "width_percent": 2.524}, {"sql": "select count(*) as aggregate from `support_tickets` where `user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.423044, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "UserController.php:40", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=40", "ajax": false, "filename": "UserController.php", "line": "40"}, "connection": "main", "explain": null, "start_percent": 59.438, "width_percent": 2.836}, {"sql": "select * from `invests` where `user_id` = 1 and `profit_status` = 1 order by `next_profit_date` asc limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.438942, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "UserController.php:41", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=41", "ajax": false, "filename": "UserController.php", "line": "41"}, "connection": "main", "explain": null, "start_percent": 62.274, "width_percent": 1.873}, {"sql": "select * from `installments` where exists (select * from `invests` where `installments`.`invest_id` = `invests`.`id` and `user_id` = 1) and `status` = 2 order by `next_time` asc limit 1", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 45}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.449879, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "UserController.php:45", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=45", "ajax": false, "filename": "UserController.php", "line": "45"}, "connection": "main", "explain": null, "start_percent": 64.147, "width_percent": 2.979}, {"sql": "select SUM(amount) as amount, DATE_FORMAT(created_at,'%Y-%m-%d') as date from `transactions` where `user_id` = 1 and (`remark` = 'down_payment' or `remark` = 'installment') and `created_at` >= '2025-06-28 21:40:33' group by `date` order by `created_at` asc", "type": "query", "params": [], "bindings": [1, "down_payment", "installment", "2025-06-28 21:40:33"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 56}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.456928, "duration": 0.004889999999999999, "duration_str": "4.89ms", "memory": 0, "memory_str": null, "filename": "UserController.php:56", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=56", "ajax": false, "filename": "UserController.php", "line": "56"}, "connection": "main", "explain": null, "start_percent": 67.126, "width_percent": 6.361}, {"sql": "select SUM(amount) as amount, DATE_FORMAT(created_at,'%Y-%m-%d') as date from `transactions` where `user_id` = 1 and `remark` = 'profit' and `created_at` >= '2025-06-28 21:40:33' group by `date` order by `created_at` asc", "type": "query", "params": [], "bindings": [1, "profit", "2025-06-28 21:40:33"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 67}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.467255, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "UserController.php:67", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\UserController.php", "line": 67}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=67", "ajax": false, "filename": "UserController.php", "line": "67"}, "connection": "main", "explain": null, "start_percent": 73.488, "width_percent": 1.795}, {"sql": "select * from `frontends` where `tempname` = 'basic' and `data_keys` = 'kyc_instruction.content' order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["basic", "kyc_instruction.content"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 359}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.2868361, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "helpers.php:359", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=359", "ajax": false, "filename": "helpers.php", "line": "359"}, "connection": "main", "explain": null, "start_percent": 75.283, "width_percent": 3.551}, {"sql": "select * from `pages` where `tempname` = 'templates.basic.' and `is_default` = 0", "type": "query", "params": [], "bindings": ["templates.basic.", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "templates.basic.partials.header", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/partials/header.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.434167, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "templates.basic.partials.header:4", "source": {"index": 15, "namespace": "view", "name": "templates.basic.partials.header", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/partials/header.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fheader.blade.php&line=4", "ajax": false, "filename": "header.blade.php", "line": "4"}, "connection": "main", "explain": null, "start_percent": 78.834, "width_percent": 2.159}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "templates.basic.partials.language", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/partials/language.blade.php", "line": 3}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.44431, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "templates.basic.partials.language:3", "source": {"index": 16, "namespace": "view", "name": "templates.basic.partials.language", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/partials/language.blade.php", "line": 3}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Flanguage.blade.php&line=3", "ajax": false, "filename": "language.blade.php", "line": "3"}, "connection": "main", "explain": null, "start_percent": 80.994, "width_percent": 2.225}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "templates.basic.partials.language", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/partials/language.blade.php", "line": 3}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.4537868, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "templates.basic.partials.language:3", "source": {"index": 16, "namespace": "view", "name": "templates.basic.partials.language", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/partials/language.blade.php", "line": 3}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Flanguage.blade.php&line=3", "ajax": false, "filename": "language.blade.php", "line": "3"}, "connection": "main", "explain": null, "start_percent": 83.218, "width_percent": 1.236}, {"sql": "select * from `frontends` where `tempname` = 'basic' and `data_keys` = 'footer.content' order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["basic", "footer.content"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 359}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.15209, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "helpers.php:359", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=359", "ajax": false, "filename": "helpers.php", "line": "359"}, "connection": "main", "explain": null, "start_percent": 84.454, "width_percent": 1.769}, {"sql": "select * from `frontends` where `tempname` = 'basic' and `data_keys` = 'footer.element' order by `id` asc limit 4", "type": "query", "params": [], "bindings": ["basic", "footer.element"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 366}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.158206, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "helpers.php:366", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 366}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=366", "ajax": false, "filename": "helpers.php", "line": "366"}, "connection": "main", "explain": null, "start_percent": 86.223, "width_percent": 1.795}, {"sql": "select * from `frontends` where `tempname` = 'basic' and `data_keys` = 'subscribe.content' order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["basic", "subscribe.content"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 359}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.164994, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "helpers.php:359", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=359", "ajax": false, "filename": "helpers.php", "line": "359"}, "connection": "main", "explain": null, "start_percent": 88.019, "width_percent": 1.743}, {"sql": "select * from `frontends` where `tempname` = 'basic' and `data_keys` = 'contact_us.content' order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["basic", "contact_us.content"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 359}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.171026, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "helpers.php:359", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=359", "ajax": false, "filename": "helpers.php", "line": "359"}, "connection": "main", "explain": null, "start_percent": 89.762, "width_percent": 1.613}, {"sql": "select * from `frontends` where `tempname` = 'basic' and `data_keys` = 'policy_pages.element' order by `id` asc", "type": "query", "params": [], "bindings": ["basic", "policy_pages.element"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 366}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.177027, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "helpers.php:366", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 366}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=366", "ajax": false, "filename": "helpers.php", "line": "366"}, "connection": "main", "explain": null, "start_percent": 91.375, "width_percent": 1.925}, {"sql": "select * from `frontends` where `data_keys` = 'seo.data' limit 1", "type": "query", "params": [], "bindings": ["seo.data"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 84}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "templates.basic.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/layouts/app.blade.php", "line": 10}], "start": **********.191018, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:84", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 84}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=84", "ajax": false, "filename": "AppServiceProvider.php", "line": "84"}, "connection": "main", "explain": null, "start_percent": 93.3, "width_percent": 1.6}, {"sql": "select * from `extensions` where `act` = 'google-analytics' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["google-analytics", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 91}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.202526, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "helpers.php:91", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=91", "ajax": false, "filename": "helpers.php", "line": "91"}, "connection": "main", "explain": null, "start_percent": 94.9, "width_percent": 1.847}, {"sql": "select * from `frontends` where `data_keys` = 'cookie.data' limit 1", "type": "query", "params": [], "bindings": ["cookie.data"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "templates.basic.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/layouts/app.blade.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.207678, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "templates.basic.layouts.app:41", "source": {"index": 16, "namespace": "view", "name": "templates.basic.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/layouts/app.blade.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Flayouts%2Fapp.blade.php&line=41", "ajax": false, "filename": "app.blade.php", "line": "41"}, "connection": "main", "explain": null, "start_percent": 96.748, "width_percent": 1.574}, {"sql": "select * from `extensions` where `act` = 'tawk-chat' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["tawk-chat", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 91}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.214537, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "helpers.php:91", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=91", "ajax": false, "filename": "helpers.php", "line": "91"}, "connection": "main", "explain": null, "start_percent": 98.322, "width_percent": 1.678}]}, "models": {"data": {"App\\Models\\Frontend": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FModels%2FFrontend.php&line=1", "ajax": false, "filename": "Frontend.php", "line": "?"}}, "App\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Page": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FModels%2FPage.php&line=1", "ajax": false, "filename": "Page.php", "line": "?"}}}, "count": 16, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/user/dashboard", "action_name": "user.home", "controller_action": "App\\Http\\Controllers\\User\\UserController@home", "uri": "GET user/dashboard", "controller": "App\\Http\\Controllers\\User\\UserController@home<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=27\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\User", "prefix": "/user", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=27\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/User/UserController.php:27-74</a>", "middleware": "web, maintenance, auth, check.status, registration.complete", "duration": "2.59s", "peak_memory": "30MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-382976445 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-382976445\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-971900594 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-971900594\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2028039041 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://localhost:8000/user/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1212 characters\">next-auth.session-token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIn0..4f1spIFqSl3M-dRR.mtKUfpcPBCIp3t04wJj7PlMzCYoGawXmPipLFPfkRWzyk3_oyYc8CZhVMg6RRpBX8QP3NiToOLArIKhLiNt2mrcP4YdELbhMImU-5Ea9pdyE6xoRXDxDL2fCD5oc8gMCfviXclUsBHDD04SCK__w5EL92vVVaL-uBe5cadsOTYkNagMrAIwo_xCf-0wctNG3ZVGh1BXUEcCcI60x3xjUkm1wx1t4kZnejfeEd06TTQ2JFeVFwVdBDvfs02W87b8oqW1SxzWBpqeKcXDP4OKQ9s4ANAGpCYK38N5aqqQP9GPI3VZlzRbjQSeQ5_nivaI_o8f1PBmsFvjr7YdCIEZS9HEqH-zFsWqvOPNgfQVpfN2OYSoXPe_cGVO9CGcq2JCSa3QbCIao6xTEarx6VAJce3WXr-bZDPWdrs2ahN6qBQTVEE65yymC2VH0zYkJOs-P-gAt_ZxzJAqQEi04igg5rtUJ5Zjp2zKrjvP9Z-MZWdYUcl3EpiTmS-TVY3j0GsiS491FTsfmD4PaFXVwXrSxFBMpd3jGvSt58U2cdJTB9z7IJokE2wGtNv5vGAvYrGCy3q3YQiCbAHFflwvkiILqcpUW1Jlo_zEYK4Huxts1Ptpa71t0DrZl3mKWdH2tKYcZj5zhzVWDIIUcnZvXw8u9.deSxXzy8aVvrZILLCi30iQ; refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************.nYNOvjg5g5a1bwi9Np4jJE9bEAHK0v0dCu2v57cIYnU; XSRF-TOKEN=SYNUFbYXPIchAqag9y0hSdCjQiOov00cwklWWRFh; cre_session=V8Ncj3BjnzGmcnDkrp8i6plbOFgKyfbKlyEDwRLQ</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2028039041\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1696622754 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>next-auth_session-token</span>\" => \"<span class=sf-dump-str title=\"741 characters\">eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIn0..4f1spIFqSl3M-dRR.mtKUfpcPBCIp3t04wJj7PlMzCYoGawXmPipLFPfkRWzyk3_oyYc8CZhVMg6RRpBX8QP3NiToOLArIKhLiNt2mrcP4YdELbhMImU-5Ea9pdyE6xoRXDxDL2fCD5oc8gMCfviXclUsBHDD04SCK__w5EL92vVVaL-uBe5cadsOTYkNagMrAIwo_xCf-0wctNG3ZVGh1BXUEcCcI60x3xjUkm1wx1t4kZnejfeEd06TTQ2JFeVFwVdBDvfs02W87b8oqW1SxzWBpqeKcXDP4OKQ9s4ANAGpCYK38N5aqqQP9GPI3VZlzRbjQSeQ5_nivaI_o8f1PBmsFvjr7YdCIEZS9HEqH-zFsWqvOPNgfQVpfN2OYSoXPe_cGVO9CGcq2JCSa3QbCIao6xTEarx6VAJce3WXr-bZDPWdrs2ahN6qBQTVEE65yymC2VH0zYkJOs-P-gAt_ZxzJAqQEi04igg5rtUJ5Zjp2zKrjvP9Z-MZWdYUcl3EpiTmS-TVY3j0GsiS491FTsfmD4PaFXVwXrSxFBMpd3jGvSt58U2cdJTB9z7IJokE2wGtNv5vGAvYrGCy3q3YQiCbAHFflwvkiILqcpUW1Jlo_zEYK4Huxts1Ptpa71t0DrZl3mKWdH2tKYcZj5zhzVWDIIUcnZvXw8u9.deSxXzy8aVvrZILLCi30iQ</span>\"\n  \"<span class=sf-dump-key>refreshToken</span>\" => \"<span class=sf-dump-str title=\"325 characters\">eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************.nYNOvjg5g5a1bwi9Np4jJE9bEAHK0v0dCu2v57cIYnU</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SYNUFbYXPIchAqag9y0hSdCjQiOov00cwklWWRFh</span>\"\n  \"<span class=sf-dump-key>cre_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">V8Ncj3BjnzGmcnDkrp8i6plbOFgKyfbKlyEDwRLQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1696622754\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-545297240 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 01:40:33 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-545297240\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-504014369 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SYNUFbYXPIchAqag9y0hSdCjQiOov00cwklWWRFh</span>\"\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K19XD9731BFTKHXFBBP3A65R</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://localhost:8000/user/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-504014369\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/user/dashboard", "action_name": "user.home", "controller_action": "App\\Http\\Controllers\\User\\UserController@home"}, "badge": null}}