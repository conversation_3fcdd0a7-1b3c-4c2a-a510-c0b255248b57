# Database Schema for Organizational Roles & Permissions

## Current Schema (<PERSON><PERSON> Permission)

The application uses the Spatie Laravel Permission package with the following tables:

1. **permissions**: Stores individual permissions
   - `id`: Primary key
   - `name`: Permission name (e.g., 'deals.view')
   - `guard_name`: Guard name ('admin' or 'web')

2. **roles**: Stores role definitions
   - `id`: Primary key
   - `name`: Role name (e.g., 'Super Admin')
   - `guard_name`: Guard name ('admin' or 'web')

3. **role_has_permissions**: Maps permissions to roles
   - `permission_id`: Foreign key to permissions
   - `role_id`: Foreign key to roles

4. **model_has_roles**: Maps roles to users
   - `role_id`: Foreign key to roles
   - `model_type`: Model class name (e.g., 'App\Models\Admin')
   - `model_id`: ID of the model

5. **model_has_permissions**: Maps permissions directly to users
   - `permission_id`: Foreign key to permissions
   - `model_type`: Model class name
   - `model_id`: ID of the model

## Schema Compatibility

The existing schema is fully compatible with the organizational roles and permissions structure. No schema changes are required because:

1. The `roles` table can store all organizational roles (CEO, <PERSON><PERSON>, etc.)
2. The `permissions` table can store department-specific permissions
3. The `role_has_permissions` table can map appropriate permissions to each role
4. The `model_has_roles` table can assign organizational roles to users

## Permission Naming Convention

To align with the organizational structure, we'll use the following naming convention for permissions:

```
{department}.{action}
```

Examples:
- `accounting.view`: View accounting data
- `deals.create`: Create new deals
- `assets.manage_metrics`: Manage asset metrics

## Role Hierarchy Implementation

The role hierarchy will be implemented through permission assignment, not through database schema changes. This approach:

1. Maintains compatibility with Spatie Laravel Permission
2. Avoids complex schema modifications
3. Allows for flexible role definitions

The middleware will implement the hierarchical checks based on role names and permission assignments.
