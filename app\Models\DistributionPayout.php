<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DistributionPayout extends Model
{

    // Specify the table name if it's different from the model name
    protected $table = 'distribution_payouts';

    // Mass assignable attributes
    protected $fillable = [
        'distribution_id',
        'investor_id',
        'amount',
        'stripe_payout_id',
        'status',
    ];

    public function distribution()
    {
        return $this->belongsTo(Distribution::class);
    }
}
