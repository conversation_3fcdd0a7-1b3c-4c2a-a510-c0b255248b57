# Partners System Implementation Guide

## Overview

This document outlines the step-by-step process for implementing a Partners system in the CRE Investment Platform. Partners will be able to create and manage their own deals with the same capabilities as administrators, but with access limited to only their own deals.

## Implementation Principles

1. **No Modification to Core Models**: The Deal model and other core models remain untouched
2. **No Controller Changes**: Existing controllers continue to work as before
3. **Clean Separation**: Partner-specific logic is isolated in its own components
4. **Follows Platform Architecture**: Adheres to Laravel MVC and existing permission structure

## Implementation Steps

### Phase 1: Database Setup

#### Step 1: Create Partner-Deal Association Table

```php
// Create a new migration file
php artisan make:migration create_partner_deals_table

// In the migration file:
public function up()
{
    Schema::create('partner_deals', function (Blueprint $table) {
        $table->id();
        $table->unsignedBigInteger('admin_id'); // Partner ID (from admins table)
        $table->unsignedBigInteger('deal_id');  // Deal ID
        $table->timestamps();
        
        $table->foreign('admin_id')->references('id')->on('admins');
        $table->foreign('deal_id')->references('id')->on('deals');
        
        // Ensure a deal can only be associated with one partner
        $table->unique('deal_id');
    });
}
```

#### Step 2: Update Admin Table (Optional)

```php
// Create a new migration file
php artisan make:migration add_company_fields_to_admins_table

// In the migration file:
public function up()
{
    Schema::table('admins', function (Blueprint $table) {
        $table->string('company_name')->nullable(); // For partners
        $table->text('company_description')->nullable();
        $table->string('company_logo')->nullable();
        $table->string('company_website')->nullable();
    });
}
```

### Phase 2: Models and Services

#### Step 3: Create PartnerDeal Model

```php
// Create a new model file
php artisan make:model PartnerDeal

// In app/Models/PartnerDeal.php:
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PartnerDeal extends Model
{
    protected $table = 'partner_deals';
    
    protected $fillable = [
        'admin_id',
        'deal_id'
    ];
    
    public function admin()
    {
        return $this->belongsTo(Admin::class);
    }
    
    public function deal()
    {
        return $this->belongsTo(Deal::class);
    }
}
```

#### Step 4: Create PartnerDealService

```php
// Create a new directory if it doesn't exist
mkdir -p app/Services

// Create the service file
// In app/Services/PartnerDealService.php:
namespace App\Services;

use App\Models\Deal;
use App\Models\PartnerDeal;
use Illuminate\Support\Facades\Auth;

class PartnerDealService
{
    /**
     * Get deals for the current partner
     */
    public function getPartnerDeals($partnerId = null)
    {
        $partnerId = $partnerId ?? Auth::guard('admin')->id();
        
        $partnerDealIds = PartnerDeal::where('admin_id', $partnerId)
            ->pluck('deal_id')
            ->toArray();
            
        return Deal::whereIn('id', $partnerDealIds)->get();
    }
    
    /**
     * Associate a deal with a partner
     */
    public function associateDealWithPartner($dealId, $partnerId = null)
    {
        $partnerId = $partnerId ?? Auth::guard('admin')->id();
        
        return PartnerDeal::create([
            'admin_id' => $partnerId,
            'deal_id' => $dealId
        ]);
    }
    
    /**
     * Check if a deal belongs to a partner
     */
    public function isDealOwnedByPartner($dealId, $partnerId = null)
    {
        $partnerId = $partnerId ?? Auth::guard('admin')->id();
        
        return PartnerDeal::where('admin_id', $partnerId)
            ->where('deal_id', $dealId)
            ->exists();
    }
    
    /**
     * Get all deals that can be viewed by the current user
     * (all deals for admins, only owned deals for partners)
     */
    public function getAccessibleDeals()
    {
        $user = Auth::guard('admin')->user();
        
        if ($user->hasRole('Partner')) {
            return $this->getPartnerDeals();
        }
        
        return Deal::all();
    }
}
```

### Phase 3: Middleware and Controllers

#### Step 5: Create Partner Middleware

```php
// Create a new middleware
php artisan make:middleware PartnerDealAccess

// In app/Http/Middleware/PartnerDealAccess.php:
namespace App\Http\Middleware;

use Closure;
use App\Services\PartnerDealService;
use Illuminate\Support\Facades\Auth;

class PartnerDealAccess
{
    protected $partnerDealService;
    
    public function __construct(PartnerDealService $partnerDealService)
    {
        $this->partnerDealService = $partnerDealService;
    }
    
    public function handle($request, Closure $next)
    {
        // Only apply to partners
        if (!Auth::guard('admin')->user()->hasRole('Partner')) {
            return $next($request);
        }
        
        // Get deal ID from route parameters
        $dealId = $request->route('deal');
        
        // If no deal ID or partner owns the deal, proceed
        if (!$dealId || $this->partnerDealService->isDealOwnedByPartner($dealId)) {
            return $next($request);
        }
        
        // Partner doesn't own this deal
        return redirect()->route('partner.dashboard')
            ->with('error', 'You do not have access to this deal');
    }
}
```

#### Step 6: Register the Middleware

```php
// In app/Http/Kernel.php:
protected $routeMiddleware = [
    // Other middleware
    'partner.deal' => \App\Http\Middleware\PartnerDealAccess::class,
];
```

#### Step 7: Create Partner Controller

```php
// Create a new controller
php artisan make:controller Admin/PartnerController

// In app/Http/Controllers/Admin/PartnerController.php:
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use App\Models\Deal;
use App\Services\PartnerDealService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PartnerController extends Controller
{
    protected $partnerDealService;
    
    public function __construct(PartnerDealService $partnerDealService)
    {
        $this->partnerDealService = $partnerDealService;
    }
    
    public function dashboard()
    {
        $pageTitle = 'Partner Dashboard';
        $partner = Auth::guard('admin')->user();
        $deals = $this->partnerDealService->getPartnerDeals();
        
        return view('admin.partner.dashboard', compact('pageTitle', 'partner', 'deals'));
    }
    
    public function deals()
    {
        $pageTitle = 'My Deals';
        $deals = $this->partnerDealService->getPartnerDeals();
        
        // Reuse the existing deals.index view
        return view('admin.deals.index', compact('pageTitle', 'deals'));
    }
    
    // Create a new deal (this will automatically be associated with the partner)
    public function createDeal()
    {
        $pageTitle = 'Create New Deal';
        return view('admin.deals.create', compact('pageTitle'));
    }
}
```

#### Step 8: Create Partner Management Controller

```php
// Create a new controller
php artisan make:controller Admin/PartnerManagementController

// In app/Http/Controllers/Admin/PartnerManagementController.php:
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;

class PartnerManagementController extends Controller
{
    public function index()
    {
        $pageTitle = 'Manage Partners';
        $partners = Admin::role('Partner')->paginate(10);
        return view('admin.partners.index', compact('pageTitle', 'partners'));
    }
    
    public function create()
    {
        $pageTitle = 'Add New Partner';
        return view('admin.partners.create', compact('pageTitle'));
    }
    
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:admins,email',
            'password' => 'required|min:8|confirmed',
            'company_name' => 'required|string|max:255',
            'company_description' => 'nullable|string',
            'company_website' => 'nullable|url',
            // Add validation for company_logo if needed
        ]);
        
        $partner = Admin::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => bcrypt($validated['password']),
            'company_name' => $validated['company_name'],
            'company_description' => $validated['company_description'] ?? null,
            'company_website' => $validated['company_website'] ?? null,
        ]);
        
        // Assign Partner role
        $partnerRole = Role::findByName('Partner', 'admin');
        $partner->assignRole($partnerRole);
        
        return redirect()->route('admin.partners.index')
            ->with('success', 'Partner created successfully');
    }
    
    // Add edit, update, and delete methods as needed
}
```

### Phase 4: Routes and Views

#### Step 9: Add Routes

```php
// In routes/admin.php or routes/web.php:

// Partner routes (for partner users)
Route::group(['middleware' => ['auth:admin', 'role:Partner'], 'prefix' => 'partner'], function () {
    Route::get('/dashboard', [PartnerController::class, 'dashboard'])->name('partner.dashboard');
    Route::get('/deals', [PartnerController::class, 'deals'])->name('partner.deals');
    Route::get('/deals/create', [PartnerController::class, 'createDeal'])->name('partner.deals.create');
    
    // Apply the partner.deal middleware to deal-specific routes
    Route::group(['middleware' => ['partner.deal']], function () {
        // These routes use the existing controllers but are filtered by the middleware
        Route::get('/deals/{deal}', [DealController::class, 'show'])->name('partner.deal.show');
        Route::get('/deals/{deal}/edit', [DealController::class, 'edit'])->name('partner.deal.edit');
        Route::put('/deals/{deal}', [DealController::class, 'update'])->name('partner.deal.update');
        Route::delete('/deals/{deal}', [DealController::class, 'destroy'])->name('partner.deal.destroy');
        
        // Similar routes for offerings, assets, etc.
    });
});

// Partner management routes (for administrators)
Route::group(['middleware' => ['auth:admin', 'permission:partnerships.view']], function () {
    Route::get('/partners', [PartnerManagementController::class, 'index'])->name('admin.partners.index');
    Route::get('/partners/create', [PartnerManagementController::class, 'create'])->name('admin.partners.create');
    Route::post('/partners', [PartnerManagementController::class, 'store'])->name('admin.partners.store');
    Route::get('/partners/{partner}/edit', [PartnerManagementController::class, 'edit'])->name('admin.partners.edit');
    Route::put('/partners/{partner}', [PartnerManagementController::class, 'update'])->name('admin.partners.update');
    Route::delete('/partners/{partner}', [PartnerManagementController::class, 'destroy'])->name('admin.partners.destroy');
});
```

#### Step 10: Create Views

Create the following view files:

1. `resources/views/admin/partner/dashboard.blade.php` - Partner dashboard
2. `resources/views/admin/partners/index.blade.php` - List of partners (for admins)
3. `resources/views/admin/partners/create.blade.php` - Create partner form
4. `resources/views/admin/partners/edit.blade.php` - Edit partner form

### Phase 5: Event Handling

#### Step 11: Create Service Provider for Deal Creation Events

```php
// Create a new service provider
php artisan make:provider PartnerServiceProvider

// In app/Providers/PartnerServiceProvider.php:
namespace App\Providers;

use App\Models\Deal;
use App\Services\PartnerDealService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\ServiceProvider;

class PartnerServiceProvider extends ServiceProvider
{
    public function boot()
    {
        // Listen for deal creation events
        Event::listen('eloquent.created: App\Models\Deal', function (Deal $deal) {
            $user = Auth::guard('admin')->user();
            
            // If the creator is a partner, associate the deal
            if ($user && $user->hasRole('Partner')) {
                app(PartnerDealService::class)->associateDealWithPartner($deal->id, $user->id);
            }
        });
    }
    
    public function register()
    {
        //
    }
}
```

#### Step 12: Register the Service Provider

```php
// In config/app.php:
'providers' => [
    // Other service providers
    App\Providers\PartnerServiceProvider::class,
],
```

### Phase 6: Permissions Setup

#### Step 13: Update Role and Permission Seeder

```php
// In database/seeders/RoleAndPermissionSeeder.php:

// Add partner-specific permissions
$partnerPermissions = [
    'deals.view_own',
    'deals.create_own',
    'deals.edit_own',
    'deals.delete_own',
    'offerings.view_own',
    'offerings.create_own',
    'offerings.edit_own',
    'offerings.delete_own',
    'assets.view_own',
    'assets.create_own',
    'assets.edit_own',
    'assets.delete_own',
    'partner_dashboard.access'
];

// Add these to the $permissions array
foreach ($partnerPermissions as $permission) {
    Permission::findOrCreate($permission, 'admin');
}

// Create Partner role
$partnerRole = Role::findOrCreate('Partner', 'admin');
$partnerRole->givePermissionTo($partnerPermissions);
```

#### Step 14: Run Migrations and Seeders

```bash
php artisan migrate
php artisan db:seed --class=RoleAndPermissionSeeder
```

### Phase 7: Testing

#### Step 15: Test Partner Functionality

1. Create a partner account
2. Log in as the partner
3. Create a deal
4. Verify that the deal is associated with the partner
5. Verify that the partner can only see their own deals
6. Verify that administrators can see all deals

## Additional Implementation Considerations

### Navigation Menu Updates

```php
// In resources/views/admin/layouts/sidebar.blade.php or similar:

@role('Partner')
    <!-- Partner-specific menu items -->
    <li class="nav-item">
        <a href="{{ route('partner.dashboard') }}" class="nav-link">
            <i class="nav-icon fas fa-tachometer-alt"></i>
            <p>Partner Dashboard</p>
        </a>
    </li>
    <li class="nav-item">
        <a href="{{ route('partner.deals') }}" class="nav-link">
            <i class="nav-icon fas fa-building"></i>
            <p>My Deals</p>
        </a>
    </li>
@else
    <!-- Regular admin menu items -->
    <!-- Include partner management for admins with permission -->
    @can('partnerships.view')
        <li class="nav-item">
            <a href="{{ route('admin.partners.index') }}" class="nav-link">
                <i class="nav-icon fas fa-handshake"></i>
                <p>Manage Partners</p>
            </a>
        </li>
    @endcan
@endrole
```

### Partner Dashboard View Example

```php
<!-- resources/views/admin/partner/dashboard.blade.php -->
@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Partner Dashboard</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-info">
                                <div class="inner">
                                    <h3>{{ $deals->count() }}</h3>
                                    <p>Total Deals</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-building"></i>
                                </div>
                                <a href="{{ route('partner.deals') }}" class="small-box-footer">
                                    View Deals <i class="fas fa-arrow-circle-right"></i>
                                </a>
                            </div>
                        </div>
                        <!-- Add more dashboard widgets as needed -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
```

### Security Considerations

1. **Cross-Partner Data Protection**: Ensure that the middleware properly validates ownership of not just deals but all related entities (offerings, assets, etc.)

2. **API Endpoints**: If your platform has API endpoints, apply similar ownership checks to those endpoints

3. **File Access Control**: Ensure that uploaded files (documents, images) related to deals are properly secured and only accessible to the owning partner

```php
// Example file access middleware
class PartnerFileAccess
{
    protected $partnerDealService;
    
    public function __construct(PartnerDealService $partnerDealService)
    {
        $this->partnerDealService = $partnerDealService;
    }
    
    public function handle($request, Closure $next)
    {
        // Only apply to partners
        if (!Auth::guard('admin')->user()->hasRole('Partner')) {
            return $next($request);
        }
        
        $fileId = $request->route('file');
        $file = File::findOrFail($fileId);
        
        // Check if file belongs to a deal owned by the partner
        if ($file->fileable_type === 'App\Models\Deal') {
            if ($this->partnerDealService->isDealOwnedByPartner($file->fileable_id)) {
                return $next($request);
            }
        }
        
        return redirect()->route('partner.dashboard')
            ->with('error', 'You do not have access to this file');
    }
}
```

### Database Indexing

For performance optimization, add indexes to the partner_deals table:

```php
// In the migration file:
Schema::create('partner_deals', function (Blueprint $table) {
    $table->id();
    $table->unsignedBigInteger('admin_id');
    $table->unsignedBigInteger('deal_id');
    $table->timestamps();
    
    $table->foreign('admin_id')->references('id')->on('admins');
    $table->foreign('deal_id')->references('id')->on('deals');
    
    // Ensure a deal can only be associated with one partner
    $table->unique('deal_id');
    
    // Add indexes for performance
    $table->index('admin_id');
});
```

### Notification System

Implement notifications for partners when important events occur with their deals:

```php
// In app/Providers/PartnerServiceProvider.php:

// Listen for investment creation events
Event::listen('eloquent.created: App\Models\Investment', function (Investment $investment) {
    $deal = $investment->deal;
    
    // Find the partner who owns this deal
    $partnerDeal = PartnerDeal::where('deal_id', $deal->id)->first();
    
    if ($partnerDeal) {
        $partner = Admin::find($partnerDeal->admin_id);
        
        // Notify the partner
        $partner->notify(new NewInvestmentNotification($investment));
    }
});
```

### Partner Onboarding Process

Consider adding a guided onboarding process for new partners:

1. Welcome email with login credentials
2. First-time login tutorial
3. Sample deal templates
4. Documentation and training resources

### Data Migration Strategy

If you have existing deals that need to be assigned to partners:

```php
// Create a command to assign deals to partners
php artisan make:command AssignDealsToPartners

// In app/Console/Commands/AssignDealsToPartners.php:
protected function handle()
{
    $this->info('Starting deal assignment process...');
    
    // Get list of partners
    $partners = Admin::role('Partner')->get();
    
    foreach ($partners as $partner) {
        // Logic to determine which deals belong to this partner
        // This could be based on a field in the deals table, or external data
        $dealIds = $this->getDealsForPartner($partner);
        
        foreach ($dealIds as $dealId) {
            PartnerDeal::create([
                'admin_id' => $partner->id,
                'deal_id' => $dealId
            ]);
            
            $this->info("Assigned deal ID {$dealId} to partner {$partner->name}");
        }
    }
    
    $this->info('Deal assignment completed!');
}
```

## Alignment with Platform Architecture

This implementation aligns with your platform's architectural principles by:

1. **Respecting the Core Principle**: "Roles and permissions are there to interact with existing modules and not the other way around"

2. **Following Laravel MVC**: Maintains separation of concerns with models, views, controllers, and services

3. **Leveraging Existing Authentication**: Uses the admin guard and Spatie Permission package

4. **Maintaining Clean Code**: Isolates partner-specific logic in dedicated components

5. **Ensuring Scalability**: The design allows for easy addition of more partner features in the future
