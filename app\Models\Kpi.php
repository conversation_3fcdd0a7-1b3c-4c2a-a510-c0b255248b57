<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Kpi extends Model
{
    protected $fillable = [
        'kpis_collection_id',
        'kpi_name',
        'kpi_collection_format'

    ];
    public function kpi_collection()
    {
        return $this->belongsTo(kpisCollection::class);
    }

    public function kpi_values()
    {
        return $this->hasMany(KpiValue::class);
    }

    public function kpiName()
    {
        return $this->belongsTo(KpiName::class, 'kpi_name'); // or the actual foreign key column name
    }
}
