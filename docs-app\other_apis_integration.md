# Other APIs Integration Guide

## Overview

The CREI Investment Platform includes a centralized system for managing external API integrations, such as Documenso for e-signatures. This document explains how to use, configure, and extend the "Other APIs" feature.

## Table of Contents

1. [Accessing the Other APIs Section](#accessing-the-other-apis-section)
2. [Configuring API Integrations](#configuring-api-integrations)
3. [Documenso E-Signature Integration](#documenso-e-signature-integration)
4. [Adding New API Integrations](#adding-new-api-integrations)
5. [Technical Implementation Details](#technical-implementation-details)

## Accessing the Other APIs Section

The Other APIs section can be accessed through the admin panel:

1. Log in to the admin panel at `/admin`
2. Navigate to Settings > System
3. Click on "Other APIs" in the sidebar or navigate to `/admin/other-apis`

## Configuring API Integrations

Each API integration can be configured through the admin interface:

1. In the Other APIs section, find the API you want to configure
2. Click the "Configure" button to open the configuration modal
3. Enter the required credentials and settings
4. Click "Update" to save your changes
5. Use the "Enable/Disable" button to toggle the API's active status

## Documenso E-Signature Integration

### Overview

Documenso is an open-source e-signature solution that allows users to sign documents electronically. The CREI platform integrates with Documenso to provide e-signature capabilities for investment documents.

### Configuration

To configure the Documenso integration:

1. Navigate to the Other APIs section
2. Find the "Documenso E-Signature" entry
3. Click "Configure"
4. Enter the following information:
   - **API Key**: Your Documenso API key (format: `api_xxxxxxxxxxxxxxxx`)
   - **API Base URL**: The base URL for your Documenso instance (e.g., `https://app.documenso.com/api/v1`)
5. Click "Update" to save
6. Click "Enable" to activate the integration

### Creating an API Key in Documenso

To create an API key in Documenso:

1. Log in to your Documenso account
2. Click on your avatar in the top right corner
3. Select "User settings"
4. Navigate to the "API Tokens" tab
5. Click "Create new token"
6. Enter a name for the token and select an expiration date
7. Copy the generated API key (it will only be shown once)

### Using Documenso in the Platform

Once configured, the Documenso integration is used in the following areas:

1. **E-Sign Templates**: Admin users can create and manage e-signature templates
2. **Document Signing**: Users can sign documents electronically
3. **Document Verification**: Signed documents can be verified for authenticity

## Adding New API Integrations

Developers can add new API integrations to the platform by following these steps:

1. Create a database seeder for the new API integration
2. Create a service class for the API
3. Update controllers to use the service
4. Run the seeder to add the API to the database

### Example Seeder

```php
<?php

namespace Database\Seeders;

use App\Models\ApiIntegration;
use Illuminate\Database\Seeder;

class NewApiSeeder extends Seeder
{
    public function run()
    {
        ApiIntegration::updateOrCreate(
            ['code' => 'new-api'],
            [
                'name' => 'New API Integration',
                'description' => 'Description of the new API integration',
                'credentials' => json_encode([
                    'api_key' => [
                        'title' => 'API Key',
                        'value' => '',
                        'description' => 'Your API key',
                        'type' => 'password'
                    ],
                    'api_url' => [
                        'title' => 'API URL',
                        'value' => 'https://api.example.com',
                        'description' => 'API endpoint URL',
                        'type' => 'text'
                    ]
                ]),
                'settings' => json_encode([
                    'default_option' => null
                ]),
                'status' => 0
            ]
        );
    }
}
```

## Technical Implementation Details

### Database Structure

API integrations are stored in the `api_integrations` table with the following structure:

- `id`: Primary key
- `name`: Display name of the API
- `code`: Unique identifier code
- `description`: Description of the API's purpose
- `credentials`: JSON object containing API credentials
- `settings`: JSON object containing API settings
- `status`: Boolean indicating if the API is enabled (1) or disabled (0)
- `created_at`: Timestamp of creation
- `updated_at`: Timestamp of last update

### Service Classes

Each API integration has a corresponding service class that handles communication with the external API. These service classes are located in the `App\Services` namespace.

Example service class structure:

```php
<?php

namespace App\Services;

use App\Models\ApiIntegration;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ExampleApiService
{
    protected $api;
    protected $baseUrl;
    protected $apiKey;
    
    public function __construct()
    {
        $this->api = ApiIntegration::where('code', 'example-api')->first();
        
        if ($this->api && $this->api->status) {
            $this->baseUrl = $this->api->getCredentialValue('api_url');
            $this->apiKey = $this->api->getCredentialValue('api_key');
        }
    }
    
    public function isEnabled()
    {
        return $this->api && $this->api->status && $this->apiKey;
    }
    
    // API-specific methods
}
```

### Controller Integration

Controllers that use API integrations should inject the corresponding service class and check if the API is enabled before making requests.

Example controller method:

```php
public function someApiAction(Request $request, ExampleApiService $apiService)
{
    if (!$apiService->isEnabled()) {
        return response()->json(['error' => 'API is not enabled'], 400);
    }
    
    $response = $apiService->someMethod($request->input('param'));
    return response()->json($response);
}
