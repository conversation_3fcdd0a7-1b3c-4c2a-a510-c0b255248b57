# Staff Permissions System Documentation

## Overview

This document provides information about the staff permissions system implemented in the CREI Investment Platform. The system uses the Spatie Laravel Permission package to manage role-based access control (RBAC) for staff members.

## Architecture

### Components

1. **Permission Middleware**
   - Custom wrapper around <PERSON><PERSON>'s permission middleware
   - Located at `app/Http/Middleware/PermissionMiddleware.php`
   - Registered in `app/Http/Kernel.php` as 'permission'

2. **Role and Permission Seeder**
   - Located at `database/seeders/RoleAndPermissionSeeder.php`
   - Defines all available permissions and roles
   - Assigns permissions to roles

3. **Route Middleware**
   - Applied to sensitive routes in `routes/admin.php`
   - Controls access based on user permissions

### Permission Structure

Permissions are organized by functionality:

```
- staff.*
  - staff.view
  - staff.create
  - staff.edit
  - staff.delete

- roles.*
  - roles.view
  - roles.create
  - roles.edit
  - roles.delete

- deals.*
  - deals.view
  - deals.create
  - deals.edit
  - deals.delete

- investments.*
  - investments.view
  - investments.create
  - investments.edit
  - investments.delete

- settings.*
  - settings.view
  - settings.edit
```

## Implementation Details

### Custom Permission Middleware

The custom permission middleware wraps the Spatie middleware to provide better error handling and maintain compatibility with the application's authentication flow:

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Exceptions\UnauthorizedException;
use Spatie\Permission\Middleware\PermissionMiddleware as SpatiePermissionMiddleware;

class PermissionMiddleware
{
    protected $spatieMiddleware;

    public function __construct()
    {
        $this->spatieMiddleware = new SpatiePermissionMiddleware();
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $permission
     * @return mixed
     */
    public function handle($request, Closure $next, $permission)
    {
        try {
            return $this->spatieMiddleware->handle($request, $next, $permission);
        } catch (UnauthorizedException $e) {
            if (!Auth::guard('admin')->check()) {
                return to_route('admin.login');
            }
            
            abort(403, 'You do not have the required permission to access this resource.');
        }
    }
}
```

### Route Protection

Sensitive routes are protected using the permission middleware. Example:

```php
// Staff Management
Route::controller('StaffController')->name('staff.')->prefix('staff')->middleware('permission:staff.view')->group(function () {
    Route::get('/', 'index')->name('index');
    Route::get('/create', 'create')->middleware('permission:staff.create')->name('create');
    Route::post('/', 'store')->middleware('permission:staff.create')->name('store');
    Route::get('/{staff}/edit', 'edit')->middleware('permission:staff.edit')->name('edit');
    Route::put('/{staff}', 'update')->middleware('permission:staff.edit')->name('update');
    Route::delete('/{staff}', 'destroy')->middleware('permission:staff.delete')->name('destroy');
    
    // Roles & Permissions
    Route::get('/roles', 'roles')->middleware('permission:roles.view')->name('roles');
    Route::post('/roles', 'storeRole')->middleware('permission:roles.create')->name('roles.store');
    Route::get('/roles/{role}/edit', 'editRole')->middleware('permission:roles.edit')->name('roles.edit');
    Route::put('/roles/{role}', 'updateRole')->middleware('permission:roles.edit')->name('roles.update');
    Route::delete('/roles/{role}', 'destroyRole')->middleware('permission:roles.delete')->name('roles.destroy');
});
```

## Usage

### Assigning Roles to Staff

When creating a new staff member, a role must be assigned. This is handled in the `StaffController@store` method:

```php
$admin = Admin::create([
    'name' => $request->name,
    'email' => $request->email,
    'username' => $request->username,
    'password' => Hash::make($request->password),
    'email_verified_at' => now(),
]);
$role = Role::where('guard_name', 'admin')->findOrFail($request->role);
$admin->assignRole($role);
```

### Managing Roles and Permissions

Roles and permissions can be managed through the admin interface:

- View all roles: `/admin/staff/roles`
- Edit role permissions: `/admin/staff/roles/{role}/edit`
- Create new roles: `/admin/staff/roles` (POST)

## Security Considerations

1. **Guard Names**: Always ensure that roles and permissions use the correct guard name ('admin' for staff).

2. **Super Admin Protection**: The Super Admin role should have all permissions and be protected from deletion.

3. **Permission Checks**: All sensitive routes and actions should be protected with the appropriate permission middleware.

4. **Minimum Permissions**: Each role should have at least one permission assigned.

## Troubleshooting

### Common Issues

1. **Target class [permission] does not exist**
   - Solution: Ensure the custom PermissionMiddleware is properly registered in Kernel.php
   - Clear application caches: `php artisan cache:clear`, `php artisan config:clear`, `php artisan route:clear`

2. **Unauthorized access despite having the role**
   - Check if the role has the required permissions
   - Verify that the role and permissions use the correct guard name ('admin')
   - Run `php artisan db:seed --class=RoleAndPermissionSeeder` to reset permissions

3. **Permission middleware not working**
   - Check for typos in permission names in routes and seeder
   - Ensure the middleware is properly registered in Kernel.php
   - Verify that the custom PermissionMiddleware is correctly implemented

## Maintenance

When adding new functionality to the platform, remember to:

1. Define new permissions in the RoleAndPermissionSeeder
2. Apply the permission middleware to new routes
3. Update this documentation with new permission categories

## References

- [Spatie Laravel Permission Documentation](https://spatie.be/docs/laravel-permission)
- [Laravel Middleware Documentation](https://laravel.com/docs/middleware)
