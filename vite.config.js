import { defineConfig } from 'vite'
import laravel from 'laravel-vite-plugin'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
    plugins: [
        laravel({
            input: [ 'resources/js/waterfall.js'],
            refresh: true,
        }),
        vue(),
    ],
    optimizeDeps: {
        include: ['pdfjs-dist/build/pdf', 'pdfjs-dist/build/pdf.worker']
    },

    resolve: {
        alias: {
            vue: 'vue/dist/vue.esm-bundler.js',
        },
    },
})