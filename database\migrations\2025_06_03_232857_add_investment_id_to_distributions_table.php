<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddInvestmentIdToDistributionsTable extends Migration
{
    public function up()
    {
        Schema::table('distributions', function (Blueprint $table) {
            $table->unsignedBigInteger('investment_id')->nullable()->after('deal_id');
            $table->foreign('investment_id')->references('id')->on('investments')->onDelete('set null');
        });
    }

    public function down()
    {
        Schema::table('distributions', function (Blueprint $table) {
            $table->dropForeign(['investment_id']);
            $table->dropColumn('investment_id');
        });
    }
}