# Partner Database and Model Implementation Guide

## Overview

This document provides detailed instructions for implementing the database structure and models required for the Partner interface. The core of this implementation is the `partner_deals` table, which associates partners (admin users with the Partner role) with deals.

## Database Implementation

### 1. Create Migration for partner_deals Table

Create a new migration file:

```bash
php artisan make:migration create_partner_deals_table
```

Edit the migration file to define the table structure:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePartnerDealsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('partner_deals', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('admin_id'); // Partner ID
            $table->unsignedBigInteger('deal_id');  // Deal ID
            $table->timestamps();
            
            $table->foreign('admin_id')->references('id')->on('admins');
            $table->foreign('deal_id')->references('id')->on('deals');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('partner_deals');
    }
}
```

### 2. Run the Migration

Execute the migration to create the table:

```bash
php artisan migrate
```

## Model Implementation

### 1. Create PartnerDeal Model

Create a new model file at `app/Models/PartnerDeal.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PartnerDeal extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'admin_id',
        'deal_id'
    ];
    
    /**
     * Get the admin (partner) that owns the deal.
     */
    public function admin()
    {
        return $this->belongsTo(Admin::class);
    }
    
    /**
     * Get the deal that belongs to the partner.
     */
    public function deal()
    {
        return $this->belongsTo(Deal::class);
    }
}
```

### 2. Update Admin Model (Optional)

Add a relationship to the Admin model to easily access partner deals. Open `app/Models/Admin.php` and add:

```php
/**
 * Get the deals associated with the partner.
 */
public function partner_deals()
{
    return $this->hasMany(PartnerDeal::class);
}

/**
 * Get the deals that belong to the partner.
 */
public function deals()
{
    return $this->hasManyThrough(
        Deal::class,
        PartnerDeal::class,
        'admin_id', // Foreign key on partner_deals table
        'id',       // Foreign key on deals table
        'id',       // Local key on admins table
        'deal_id'   // Local key on partner_deals table
    );
}
```

### 3. Update Deal Model (Optional)

Add a relationship to the Deal model to easily access the partner that owns the deal. Open `app/Models/Deal.php` and add:

```php
/**
 * Get the partner deal association.
 */
public function partner_deal()
{
    return $this->hasOne(PartnerDeal::class);
}

/**
 * Get the partner that owns the deal.
 */
public function partner()
{
    return $this->hasOneThrough(
        Admin::class,
        PartnerDeal::class,
        'deal_id',  // Foreign key on partner_deals table
        'id',       // Foreign key on admins table
        'id',       // Local key on deals table
        'admin_id'  // Local key on partner_deals table
    );
}
```

## Testing the Implementation

To test the database and model implementation:

1. Create a partner user (admin with Partner role)
2. Create a deal and associate it with the partner
3. Verify the relationship works correctly

You can use tinker to test this:

```bash
php artisan tinker
```

```php
// Get a partner user
$partner = \App\Models\Admin::where('id', 1)->first();

// Get a deal
$deal = \App\Models\Deal::first();

// Associate the deal with the partner
\App\Models\PartnerDeal::create([
    'admin_id' => $partner->id,
    'deal_id' => $deal->id
]);

// Verify the relationship
$partnerDeals = $partner->partner_deals;
echo "Partner has " . count($partnerDeals) . " deals\n";
```

## Troubleshooting

### Common Issues

1. **Migration Failed**: Ensure that the `admins` and `deals` tables exist before running the migration.

2. **Foreign Key Constraint Failed**: Check that the referenced IDs exist in the respective tables.

3. **Model Relationship Not Working**: Verify that the relationship methods are defined correctly and that the foreign keys are specified correctly.

### Debugging Tips

Use tinker to debug relationship issues:

```php
// Check if partner_deals table has records
\App\Models\PartnerDeal::all();

// Check if a specific partner has deals
$partner = \App\Models\Admin::find(1);
$partner->partner_deals()->get();
```

This implementation follows the principle that "roles and permissions are there to interact with existing modules and not the other way around" by creating a simple association table without modifying the core Deal model.
