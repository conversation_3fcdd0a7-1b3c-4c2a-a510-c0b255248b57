{"__meta": {"id": "01K19XD9731BFTKHXFBBP3A65R", "datetime": "2025-07-28 21:40:32", "utime": **********.613533, "method": "POST", "uri": "/user/login", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753753231.593754, "end": **********.613559, "duration": 1.0198049545288086, "duration_str": "1.02s", "measures": [{"label": "Booting", "start": 1753753231.593754, "relative_start": 0, "end": **********.017683, "relative_end": **********.017683, "duration": 0.*****************, "duration_str": "424ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.017708, "relative_start": 0.****************, "end": **********.613563, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "596ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.050468, "relative_start": 0.****************, "end": **********.067281, "relative_end": **********.067281, "duration": 0.016813039779663086, "duration_str": "16.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.606507, "relative_start": 1.****************, "end": **********.607698, "relative_end": **********.607698, "duration": 0.0011909008026123047, "duration_str": "1.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.7", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/New_York", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 6, "nb_statements": 5, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03037, "accumulated_duration_str": "30.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 78}, {"index": 16, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 63}, {"index": 17, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 86}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.124563, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "Captcha.php:78", "source": {"index": 15, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FLib%2FCaptcha.php&line=78", "ajax": false, "filename": "Captcha.php", "line": "78"}, "connection": "main", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `extensions` where `act` = 'google-recaptcha2' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["google-recaptcha2", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 78}, {"index": 17, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 86}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.139953, "duration": 0.01777, "duration_str": "17.77ms", "memory": 0, "memory_str": null, "filename": "Captcha.php:78", "source": {"index": 16, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FLib%2FCaptcha.php&line=78", "ajax": false, "filename": "Captcha.php", "line": "78"}, "connection": "main", "explain": null, "start_percent": 0, "width_percent": 58.512}, {"sql": "select * from `extensions` where `act` = 'custom-captcha' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["custom-captcha", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 64}, {"index": 18, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 86}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.167032, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "Captcha.php:95", "source": {"index": 16, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FLib%2FCaptcha.php&line=95", "ajax": false, "filename": "Captcha.php", "line": "95"}, "connection": "main", "explain": null, "start_percent": 58.512, "width_percent": 4.774}, {"sql": "select * from `users` where `username` = 'pinecrest' limit 1", "type": "query", "params": [], "bindings": ["pinecrest"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 396}, {"index": 18, "namespace": null, "name": "vendor/laravel/ui/auth-backend/AuthenticatesUsers.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 86}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/User/Auth/LoginController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\Auth\\LoginController.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.191331, "duration": 0.0030099999999999997, "duration_str": "3.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "main", "explain": null, "start_percent": 63.286, "width_percent": 9.911}, {"sql": "select * from `user_logins` where `user_ip` = '127.0.0.1' limit 1", "type": "query", "params": [], "bindings": ["127.0.0.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/User/Auth/LoginController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\Auth\\LoginController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/ui/auth-backend/AuthenticatesUsers.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 114}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/User/Auth/LoginController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\Auth\\LoginController.php", "line": 51}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.569325, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "LoginController.php:106", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/User/Auth/LoginController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\Auth\\LoginController.php", "line": 106}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FUser%2FAuth%2FLoginController.php&line=106", "ajax": false, "filename": "LoginController.php", "line": "106"}, "connection": "main", "explain": null, "start_percent": 73.197, "width_percent": 5.334}, {"sql": "insert into `user_logins` (`longitude`, `latitude`, `city`, `country_code`, `country`, `user_id`, `user_ip`, `browser`, `os`, `updated_at`, `created_at`) values ('', '', '', '', '', 1, '127.0.0.1', 'Chrome', 'Windows 10', '2025-07-28 21:40:32', '2025-07-28 21:40:32')", "type": "query", "params": [], "bindings": ["", "", "", "", "", 1, "127.0.0.1", "Chrome", "Windows 10", "2025-07-28 21:40:32", "2025-07-28 21:40:32"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/User/Auth/LoginController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\Auth\\LoginController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/ui/auth-backend/AuthenticatesUsers.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 114}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/User/Auth/LoginController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\Auth\\LoginController.php", "line": 51}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.580857, "duration": 0.00652, "duration_str": "6.52ms", "memory": 0, "memory_str": null, "filename": "LoginController.php:129", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/User/Auth/LoginController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\User\\Auth\\LoginController.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FUser%2FAuth%2FLoginController.php&line=129", "ajax": false, "filename": "LoginController.php", "line": "129"}, "connection": "main", "explain": null, "start_percent": 78.531, "width_percent": 21.469}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\UserLogin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FModels%2FUserLogin.php&line=1", "ajax": false, "filename": "UserLogin.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://localhost:8000/user/login", "action_name": "user.", "controller_action": "App\\Http\\Controllers\\User\\Auth\\LoginController@login", "uri": "POST user/login", "controller": "App\\Http\\Controllers\\User\\Auth\\LoginController@login<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FUser%2FAuth%2FLoginController.php&line=32\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\User\\Auth", "prefix": "/user", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FUser%2FAuth%2FLoginController.php&line=32\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/User/Auth/LoginController.php:32-62</a>", "middleware": "web, maintenance, guest", "duration": "1.02s", "peak_memory": "32MB", "response": "Redirect to http://localhost:8000/user/dashboard", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1732758059 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1732758059\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-185071761 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sa5ygsqd5NJll5jJajzb2tyAC0MMJIOoda5C4XM2</span>\"\n  \"<span class=sf-dump-key>username</span>\" => \"<span class=sf-dump-str title=\"9 characters\">pinecrest</span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-185071761\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-306988645 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">88</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://localhost:8000/user/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1212 characters\">next-auth.session-token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIn0..4f1spIFqSl3M-dRR.mtKUfpcPBCIp3t04wJj7PlMzCYoGawXmPipLFPfkRWzyk3_oyYc8CZhVMg6RRpBX8QP3NiToOLArIKhLiNt2mrcP4YdELbhMImU-5Ea9pdyE6xoRXDxDL2fCD5oc8gMCfviXclUsBHDD04SCK__w5EL92vVVaL-uBe5cadsOTYkNagMrAIwo_xCf-0wctNG3ZVGh1BXUEcCcI60x3xjUkm1wx1t4kZnejfeEd06TTQ2JFeVFwVdBDvfs02W87b8oqW1SxzWBpqeKcXDP4OKQ9s4ANAGpCYK38N5aqqQP9GPI3VZlzRbjQSeQ5_nivaI_o8f1PBmsFvjr7YdCIEZS9HEqH-zFsWqvOPNgfQVpfN2OYSoXPe_cGVO9CGcq2JCSa3QbCIao6xTEarx6VAJce3WXr-bZDPWdrs2ahN6qBQTVEE65yymC2VH0zYkJOs-P-gAt_ZxzJAqQEi04igg5rtUJ5Zjp2zKrjvP9Z-MZWdYUcl3EpiTmS-TVY3j0GsiS491FTsfmD4PaFXVwXrSxFBMpd3jGvSt58U2cdJTB9z7IJokE2wGtNv5vGAvYrGCy3q3YQiCbAHFflwvkiILqcpUW1Jlo_zEYK4Huxts1Ptpa71t0DrZl3mKWdH2tKYcZj5zhzVWDIIUcnZvXw8u9.deSxXzy8aVvrZILLCi30iQ; refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************.nYNOvjg5g5a1bwi9Np4jJE9bEAHK0v0dCu2v57cIYnU; XSRF-TOKEN=sa5ygsqd5NJll5jJajzb2tyAC0MMJIOoda5C4XM2; cre_session=KacxwyxTFrNJY4nxwSAMoJxMNonDJOPfM6yStlPU</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-306988645\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-213166093 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>next-auth_session-token</span>\" => \"<span class=sf-dump-str title=\"741 characters\">eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIn0..4f1spIFqSl3M-dRR.mtKUfpcPBCIp3t04wJj7PlMzCYoGawXmPipLFPfkRWzyk3_oyYc8CZhVMg6RRpBX8QP3NiToOLArIKhLiNt2mrcP4YdELbhMImU-5Ea9pdyE6xoRXDxDL2fCD5oc8gMCfviXclUsBHDD04SCK__w5EL92vVVaL-uBe5cadsOTYkNagMrAIwo_xCf-0wctNG3ZVGh1BXUEcCcI60x3xjUkm1wx1t4kZnejfeEd06TTQ2JFeVFwVdBDvfs02W87b8oqW1SxzWBpqeKcXDP4OKQ9s4ANAGpCYK38N5aqqQP9GPI3VZlzRbjQSeQ5_nivaI_o8f1PBmsFvjr7YdCIEZS9HEqH-zFsWqvOPNgfQVpfN2OYSoXPe_cGVO9CGcq2JCSa3QbCIao6xTEarx6VAJce3WXr-bZDPWdrs2ahN6qBQTVEE65yymC2VH0zYkJOs-P-gAt_ZxzJAqQEi04igg5rtUJ5Zjp2zKrjvP9Z-MZWdYUcl3EpiTmS-TVY3j0GsiS491FTsfmD4PaFXVwXrSxFBMpd3jGvSt58U2cdJTB9z7IJokE2wGtNv5vGAvYrGCy3q3YQiCbAHFflwvkiILqcpUW1Jlo_zEYK4Huxts1Ptpa71t0DrZl3mKWdH2tKYcZj5zhzVWDIIUcnZvXw8u9.deSxXzy8aVvrZILLCi30iQ</span>\"\n  \"<span class=sf-dump-key>refreshToken</span>\" => \"<span class=sf-dump-str title=\"325 characters\">eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************.nYNOvjg5g5a1bwi9Np4jJE9bEAHK0v0dCu2v57cIYnU</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sa5ygsqd5NJll5jJajzb2tyAC0MMJIOoda5C4XM2</span>\"\n  \"<span class=sf-dump-key>cre_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KacxwyxTFrNJY4nxwSAMoJxMNonDJOPfM6yStlPU</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-213166093\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-609459997 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 01:40:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://localhost:8000/user/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-609459997\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1577255825 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SYNUFbYXPIchAqag9y0hSdCjQiOov00cwklWWRFh</span>\"\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://localhost:8000/user/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1577255825\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://localhost:8000/user/login", "action_name": "user.", "controller_action": "App\\Http\\Controllers\\User\\Auth\\LoginController@login"}, "badge": "302 Found"}}