<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

use Illuminate\Support\Facades\Validator;
use App\Models\Distribution;
use App\Models\Waterfall;
use App\Models\WaterfallHurdle;
use App\Models\Deal;
use App\Models\Investment;
use App\Models\Investor;
use App\Models\DealClass;
use App\Models\Document;
use App\Notifications\DistributionNotification;
use App\Services\DistributionService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Schema;

class DistributionsBackupController extends Controller
{
    protected $distributionService;

    public function __construct(DistributionService $distributionService)
    {
        $this->distributionService = $distributionService;
    }

    // public function index()
    // {
    //     $pageTitle = 'Distributions';
    //     // Retrieve all distributions or filter as needed
    //     $distributions = Distribution::paginate(10);

    //     // Return the view with the distributions data
    //     return view('admin.distributions.index', compact('distributions', 'pageTitle'));
    // }
    public function store(Request $request)
    {
        \Log::info('DistributionsController@store: Start', ['request' => $request->all()]);

        // Map frontend calculation methods to backend
        $methodMap = [
            'water_fall' => 'Waterfall',
            'simple_pro_rata' => 'SimpleProRata',
            'preferred_return' => 'PreferredReturn',
            'invested_payment' => 'Interest',
            'invested_amount' => 'ReturnInvested',
            'Custom' => 'Custom'
        ];

        // Update calculation_method in request
        $requestData = $request->all();
        $requestData['calculation_method'] = $methodMap[$requestData['calculation_method']] ?? $requestData['calculation_method'];
        // Normalize distribution_waterfall to distribution_waterfall_id
        if (isset($requestData['distribution_waterfall'])) {
            $requestData['distribution_waterfall_id'] = $requestData['distribution_waterfall'];
            unset($requestData['distribution_waterfall']);
        }
        $request->replace($requestData);

        \Log::info('DistributionsController@store: Normalized request', ['requestData' => $requestData]);

        // Validation rules
        $validate = Validator::make(
            $requestData,
            [
                'deal_id' => 'required|exists:deals,id',
                'source' => 'required|string',
                // 'distribution_type' => 'required|string',
                'count_toward' => 'required|string',
                'included_classes' => 'required|array|min:1',
                'included_classes.*' => 'exists:deal_classes,id',
                'start_date' => 'required|date',
                'end_date' => 'required|date|after_or_equal:start_date',
                'distribution_date' => 'required|date',
                // 'compounding_period' => 'required_if:calculation_method,PreferredReturn,Interest|in:No Compounding,Monthly,Quarterly,Biyearly,Yearly',
                'distribution_waterfall_id' => 'required_if:calculation_method,Waterfall|exists:waterfalls,id',
                'amount' => 'required_if:calculation_method,SimpleProRata,Custom,Waterfall|min:0',
                'is_visible' => 'required|boolean',
                // 'calculation_method' => 'required|in:SimpleProRata,Waterfall,PreferredReturn,Interest,ReturnInvested,Custom',
                'memo' => 'nullable|string',
                'day_count' => 'nullable',
                'custom_amounts' => 'required_if:calculation_method,Custom|array',
                'custom_amounts.*' => 'numeric|min:0',
            ],
            [
                'deal_id.required' => 'The deal ID is required.',
                'deal_id.exists' => 'The specified deal does not exist.',
                'source.required' => 'The source field is required.',
                'distribution_type.required' => 'The distribution type is required.',
                'count_toward.required' => 'The deducts from field is required.',
                'included_classes.required' => 'At least one class must be selected.',
                'included_classes.*.exists' => 'One or more selected classes are invalid.',
                'start_date.required' => 'The period start date is required.',
                'end_date.required' => 'The period end date is required.',
                'end_date.after_or_equal' => 'The end date must be on or after the start date.',
                'distribution_date.required' => 'The distribution date is required.',
                'compounding_period.required_if' => 'The compounding period is required for Preferred Return or Interest calculations.',
                'distribution_waterfall_id.required_if' => 'The waterfall is required for Waterfall calculations.',
                'distribution_waterfall_id.exists' => 'The specified waterfall does not exist.',
                'amount.required_if' => 'The amount is required for the selected calculation method.',
                'amount.numeric' => 'The amount must be a number.',
                'amount.min' => 'The amount cannot be negative.',
                'is_visible.required' => 'The visibility setting is required.',
                'calculation_method.required' => 'The calculation method is required.',
                'custom_amounts.required_if' => 'Custom amounts are required for Custom calculations.',
                'custom_amounts.*.numeric' => 'Each custom amount must be a number.',
                'custom_amounts.*.min' => 'Custom amounts cannot be negative.',
            ]
        );

        if ($validate->fails()) {
            \Log::warning('DistributionsController@store: Validation failed', ['errors' => $validate->errors()]);
            return response()->json(['errors' => $validate->errors()], 422);
        }

        try {
            $distribution = Distribution::create([
                'deal_id' => $request->deal_id,
                'source' => $request->source,
                'distribution_type' => $request->distribution_type ?? 'default',
                'count_toward' => $request->count_toward,
                'amount' => ($request->amount),
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'distribution_date' => $request->distribution_date,
                'calculation_method' => $request->calculation_method,
                'distribution_waterfall' => $request->distribution_waterfall ?? null,
                'is_visible' => $request->is_visible,
                'memo' => $request->memo ?? null,
                'compounding_period' => $request->compounding_period ?? null,
                'day_count' => $request->day_count ?? 'Actual/365',
                'approved' => false,
                'included_classes' => json_encode($request->included_classes),
            ]);
            \Log::info('DistributionsController@store: Created parent distribution', ['distribution_id' => $distribution->id]);

            $deal = $distribution->deal ?? Deal::find($request->deal_id);
            $investments = collect();

            if ($deal && $deal->relationLoaded('offerings')) {
                $offerings = $deal->offerings;
            } else {
                $offerings = method_exists($deal, 'offerings') ? $deal->offerings()->get() : [];
            }

            \Log::info('DistributionsController@store: Offerings fetched', ['offerings_count' => count($offerings)]);

            foreach ($offerings as $offering) {
                $offeringInvestments = $offering->investments()
                    ->whereIn('deal_class_id', $request->included_classes)
                    ->where('investment_status', 'fund_received')
                    ->with(['class', 'investor'])
                    ->get();
                $investments = $investments->merge($offeringInvestments);
            }

            $investments = $investments->unique('id')->values();

            \Log::info('DistributionsController@store: Investments fetched', ['investments_count' => $investments->count()]);

            if ($investments->isEmpty()) {
                \Log::warning('DistributionsController@store: No confirmed investments found');
                $distribution->delete(); // Clean up parent record
                return response()->json(['errors' => ['investments' => 'No confirmed investments found for the selected classes']], 422);
            }

            // Initialize investment IDs
            $investmentIds = [];

            if ($request->calculation_method === 'Waterfall') {
                \Log::info('DistributionsController@store: Waterfall calculation started');
                // Load waterfall and hurdles
                $waterfall = WaterFall::findOrFail($request->distribution_waterfall_id);
                $hurdles = $waterfall->hurdles()->orderBy('sort_order')->get();
                // dd((int) str_replace([',', '$'], '', $request->amount));
                $remainingAmount = (int) str_replace([',', '$'], '', $request->amount); // In dollars
                $investorDistributions = [];

                foreach ($hurdles as $hurdle) {
                    \Log::info('DistributionsController@store: Processing hurdle', ['hurdle_id' => $hurdle->id, 'type' => $hurdle->hurdle_type]);
                    if ($remainingAmount <= 0) {
                        break;
                    }

                    $applicableClasses = $hurdle->included_classes ?? $request->included_classes;
                    if (is_string($applicableClasses)) {
                        $applicableClasses = json_decode($applicableClasses, true);
                    }
                    if (!is_array($applicableClasses)) {
                        $applicableClasses = [];
                    }

                    if ($hurdle->hurdle_type === WaterFallHurdle::HURDLE_TYPE_PREFERRED_RETURN) {
                        foreach ($investments as $investment) {
                            if (!in_array($investment->deal_class_id, $applicableClasses)) {
                                continue;
                            }

                            $class = $investment->class;
                            $prefReturnRate = $class->preferred_return / 100;
                            $investmentAmount = $investment->investment_amount->getAmount() / 100; // In dollars

                            // Calculate preferred return
                            $days = Carbon::parse($distribution->start_date)->diffInDays($distribution->end_date);
                            $dailyRate = $prefReturnRate / ($distribution->day_count === 'Actual/365' ? 365 : 360);
                            $prefReturn = $investmentAmount * $dailyRate * $days;

                            // Apply compounding if specified
                            if ($distribution->compounding_period && $distribution->compounding_period !== 'No Compounding') {
                                $n = match ($distribution->compounding_period) {
                                    'Monthly' => 12,
                                    'Quarterly' => 4,
                                    'Biyearly' => 2,
                                    'Yearly' => 1,
                                    default => 1,
                                };
                                $t = $days / 365;
                                $prefReturn = $investmentAmount * (pow(1 + $prefReturnRate / $n, $n * $t) - 1);
                            }

                            // Cap at remaining amount
                            $allocAmount = min($prefReturn, $remainingAmount);
                            $remainingAmount -= $allocAmount;

                            if ($allocAmount > 0) {
                                $investorDistributions[$investment->investor_id][] = [
                                    'investment_id' => $investment->id,
                                    'amount' => $allocAmount,
                                    'type' => 'preferred_return',
                                ];
                                $investmentIds[] = $investment->id;
                            }
                        }
                    } elseif ($hurdle->hurdle_type === WaterFallHurdle::HURDLE_TYPE_SPLIT) {
                        $splits = $hurdle->splits ?? ['lp' => 80, 'gp' => 20];
                        $totalClassInvestment = $investments->whereIn('deal_class_id', $applicableClasses)
                            ->sum(function ($investment) {
                                return $investment->investment_amount->getAmount() / 100;
                            });

                        if ($totalClassInvestment == 0) {
                            continue;
                        }

                        foreach ($investments as $investment) {
                            if (!in_array($investment->deal_class_id, $applicableClasses)) {
                                continue;
                            }

                            $lpShare = ($splits['lp'] ?? 100) / 100;
                            if (is_object($investment->investment_amount) && method_exists($investment->investment_amount, 'getAmount')) {
                                $investmentAmount = $investment->investment_amount->getAmount() / 100;
                            } else {
                                // Already in dollars, so just cast to float
                                $investmentAmount = (float) $investment->investment_amount;
                            }
                            $allocAmount = ($investmentAmount / $totalClassInvestment) * $remainingAmount * $lpShare;
                            $allocAmount = min($allocAmount, $remainingAmount);
                            $remainingAmount -= $allocAmount;

                            if ($allocAmount > 0) {
                                $investorDistributions[$investment->investor_id][] = [
                                    'investment_id' => $investment->id,
                                    'amount' => $allocAmount,
                                    'type' => 'split',
                                ];
                                $investmentIds[] = $investment->id;
                            }
                        }
                    } elseif ($hurdle->hurdle_type === WaterFallHurdle::HURDLE_TYPE_RETURN_OF_CAPITAL) {
                        foreach ($investments as $investment) {
                            if (!in_array($investment->deal_class_id, $applicableClasses)) {
                                continue;
                            }
                            $s_amount = $investment->investment_amount;
                            // dd((int) str_replace(['$', ','], '', $s_amount) / 100);
                            $simpleFloat = (int) str_replace(['$', ','], '', $s_amount);
                            $investmentAmount = $simpleFloat;

                            $paidCapitalQuery = Distribution::where('included_classes', $investment->investor_id)
                                ->where('deal_id', $distribution->deal_id)
                                ->where('distribution_type', 'return_of_capital');

                            // If deal_class_id exists in Distribution, filter by it
                            if (Schema::hasColumn('distributions', 'deal_class_id')) {
                                $paidCapitalQuery->where('deal_class_id', $investment->deal_class_id);
                            }
                            // dd($paidCapitalQuery->sum('amount'));
                            $paidCapital = $paidCapitalQuery->sum('amount') / 100;
                            $remainingCapital = max(0, $investmentAmount - $paidCapital);

                            $allocAmount = min($remainingCapital, $remainingAmount);
                            $remainingAmount -= $allocAmount;
                            // dd($remainingAmount);
                            if ($allocAmount > 0) {
                                $investorDistributions[$investment->investor_id][] = [
                                    'investment_id' => $investment->id,
                                    'amount' => $allocAmount,
                                    'type' => 'return_of_capital',
                                ];
                                $investmentIds[] = $investment->id;
                            }
                        }

                    } elseif ($hurdle->hurdle_type === WaterFallHurdle::HURDLE_TYPE_CATCH_UP) {
                        $catchUpRate = $hurdle->catch_up_rate ?? 0; // e.g., 0.20 for 20%
                        $targetClasses = $hurdle->target_classes ?? $applicableClasses;

                        foreach ($investments as $investment) {
                            if (!in_array($investment->deal_class_id, $targetClasses)) {
                                continue;
                            }

                            $investmentAmount = $investment->investment_amount->getAmount() / 100;
                            $totalReturnPaid = Distribution::where('investment_id', $investment->id)
                                ->whereIn('distribution_type', ['preferred_return', 'split'])
                                ->sum('amount') / 100;
                            $targetReturn = $investmentAmount * $catchUpRate;
                            $catchUpAmount = max(0, $targetReturn - $totalReturnPaid);

                            $allocAmount = min($catchUpAmount, $remainingAmount);
                            $remainingAmount -= $allocAmount;

                            if ($allocAmount > 0) {
                                $investorDistributions[$investment->investor_id][] = [
                                    'investment_id' => $investment->id,
                                    'amount' => $allocAmount,
                                    'type' => 'catch_up',
                                ];
                                $investmentIds[] = $investment->id;
                            }
                        }
                    } elseif ($hurdle->hurdle_type === WaterFallHurdle::HURDLE_TYPE_PROMOTE) {
                        $promoteShare = $hurdle->promote_share ?? 20; // e.g., 20%
                        $totalClassInvestment = $investments->whereIn('deal_class_id', $applicableClasses)
                            ->sum(function ($investment) {
                                return $investment->investment_amount->getAmount() / 100;
                            });

                        if ($totalClassInvestment == 0) {
                            continue;
                        }

                        foreach ($investments as $investment) {
                            if (!in_array($investment->deal_class_id, $applicableClasses)) {
                                continue;
                            }

                            $investmentAmount = $investment->investment_amount->getAmount() / 100;
                            $allocAmount = ($investmentAmount / $totalClassInvestment) * $remainingAmount * ($promoteShare / 100);
                            $allocAmount = min($allocAmount, $remainingAmount);
                            $remainingAmount -= $allocAmount;

                            if ($allocAmount > 0) {
                                $investorDistributions[$investment->investor_id][] = [
                                    'investment_id' => $investment->id,
                                    'amount' => $allocAmount,
                                    'type' => 'promote',
                                ];
                                $investmentIds[] = $investment->id;
                            }
                        }
                    } elseif ($hurdle->hurdle_type === WaterFallHurdle::HURDLE_TYPE_FIXED_AMOUNT) {
                        $fixedAmount = $hurdle->fixed_amount ?? 0; // In dollars
                        $totalClassInvestment = $investments->whereIn('deal_class_id', $applicableClasses)
                            ->sum(function ($investment) {
                                return $investment->investment_amount->getAmount() / 100;
                            });

                        if ($totalClassInvestment == 0) {
                            continue;
                        }

                        $allocTotal = min($fixedAmount, $remainingAmount);
                        $remainingAmount -= $allocTotal;

                        foreach ($investments as $investment) {
                            if (!in_array($investment->deal_class_id, $applicableClasses)) {
                                continue;
                            }

                            $investmentAmount = $investment->investment_amount->getAmount() / 100;
                            $allocAmount = ($investmentAmount / $totalClassInvestment) * $allocTotal;

                            if ($allocAmount > 0) {
                                $investorDistributions[$investment->investor_id][] = [
                                    'investment_id' => $investment->id,
                                    'amount' => $allocAmount,
                                    'type' => 'fixed_amount',
                                ];
                                $investmentIds[] = $investment->id;
                            }
                        }
                    }
                }

                // Remove duplicate investment IDs
                $investmentIds = array_unique($investmentIds);

                // Update parent distribution with investment IDs
                // $distribution->update(['investment_id' => json_encode($investmentIds)]);

                // Save child distributions
                // Instead of creating distributions here, just show which investments should be distributed
                $investmentsToDistribute = [];
                foreach ($investorDistributions as $investorId => $allocations) {
                    foreach ($allocations as $alloc) {
                        $investmentsToDistribute[] = [
                            'investor_id' => $investorId,
                            'investment_id' => json_encode($alloc['investment_id'] ?? null),
                            'amount' => $alloc['amount'],
                            'type' => $alloc['type'],
                        ];
                    }
                }
                // dd($investmentsToDistribute);
                // You can return or log $investmentsToDistribute for inspection
                \Log::info('DistributionsController@store: Investments to distribute', [
                    'investments' => $investmentsToDistribute
                ]);
                return response()->json([
                    'investments_to_distribute' => $investmentsToDistribute
                ], 200);
                \Log::info(message: 'DistributionsController@store: Waterfall calculation completed');
            } elseif ($request->calculation_method === 'SimpleProRata') {
                \Log::info('DistributionsController@store: SimpleProRata calculation started');
                $totalAmount = (int) str_replace([',', '$'], '', $request->amount);
                $classShares = DealClass::whereIn('id', $request->included_classes)
                    ->pluck('distribution_share', 'id');
                foreach ($request->included_classes as $classId) {
                    // $classShares[$classId] might be a string like "64.00%"
                    $shareValue = $classShares[$classId];
                    if (is_string($shareValue) && str_contains($shareValue, '%')) {
                        $shareValue = floatval(str_replace('%', '', $shareValue)) / 100;
                    } else {
                        $shareValue = floatval($shareValue);
                        if ($shareValue > 1) {
                            $shareValue = $shareValue / 100;
                        }
                    }
                    $classShare = $shareValue;
                    // $classShare = ($classShares[$classId] ?? 100) / 100;
                    $classAmount = $totalAmount * $classShare;
                    $classInvestments = $investments->where('deal_class_id', $classId);
                    $totalClassInvestment = $classInvestments->sum(callback: function ($investment) {
                        $amountValue = $investment->investment_amount;
                        if (is_object($amountValue) && method_exists($amountValue, 'getAmount')) {
                            $amountValue = $amountValue->getAmount();
                        } elseif (is_array($amountValue)) {
                            $amountValue = isset($amountValue['amount']) ? $amountValue['amount'] : 0;
                        } else {
                            $amountValue = (int) str_replace(['$', ','], '', $amountValue);
                        }
                        return (int) $amountValue;
                    });

                    if ($totalClassInvestment == 0) {
                        continue;
                    }

                    foreach ($classInvestments as $investment) {
                        // Handle investment_amount as object or string
                        $amountValue = $investment->investment_amount;
                        if (is_object($amountValue) && method_exists($amountValue, 'getAmount')) {
                            $amountValue = $amountValue->getAmount();
                        } elseif (is_array($amountValue)) {
                            $amountValue = isset($amountValue['amount']) ? $amountValue['amount'] : 0;
                        } else {
                            $amountValue = (int) str_replace(['$', ','], '', $amountValue);
                        }
                        $allocAmount = ($amountValue / 100 / $totalClassInvestment) * $classAmount;

                        if ($allocAmount > 0) {
                            $investmentsToDistribute[] = [
                                'investor_id' => $investment->investor_id,
                                'investment_id' => $investment->id,
                                'amount' => $allocAmount,
                                'type' => 'pro_rata',
                            ];
                            $investmentIds[] = $investment->id;
                        }
                    }
                }

                $investmentIds = array_unique($investmentIds);
                \Log::info('DistributionsController@store: Investments to distribute', [
                    'investments' => $investmentsToDistribute
                ]);
                return response()->json([
                    'investments_to_distribute' => $investmentsToDistribute
                ], 200);
            } elseif ($distribution->calculation_method === 'PreferredReturn') {
                \Log::info('DistributionsController@store: PreferredReturn calculation started');
                $investmentsToDistribute = [];
                $investmentIds = [];

                foreach ($investments as $investment) {
                    \Log::info('Processing investment', [
                        'investment_id' => $investment->id,
                        'deal_class_id' => $investment->deal_class_id,
                        'investor_id' => $investment->investor_id
                    ]);

                    $class = $investment->class;
                    $prefReturnRate = $class->preferred_return ? (float) $class->preferred_return / 100 : 0;
                    \Log::info('Preferred return rate', ['rate' => $prefReturnRate, 'class_id' => $class->id]);

                    if ($prefReturnRate <= 0) {
                        \Log::warning('Invalid or zero preferred return rate for investment', [
                            'investment_id' => $investment->id,
                            'class_id' => $class->id
                        ]);
                        continue;
                    }

                    $amountSValue = $investment->investment_amount;
                    if (is_object($amountSValue) && method_exists($amountSValue, 'getAmount')) {
                        $amountSValue = $amountSValue->getAmount();
                    } elseif (is_array($amountSValue)) {
                        $amountSValue = isset($amountSValue['amount']) ? $amountSValue['amount'] : 0;
                    } else {
                        $amountSValue = (float) str_replace(['$', ','], '', $amountSValue);
                    }

                    $investmentAmount = $amountSValue;
                    \Log::info('Investment amount', [
                        'investment_id' => $investment->id,
                        'amount' => $investmentAmount
                    ]);

                    if ($investmentAmount <= 0) {
                        \Log::warning('Invalid or zero investment amount', [
                            'investment_id' => $investment->id
                        ]);
                        continue;
                    }

                    $days = Carbon::parse($distribution->start_date)->diffInDays($distribution->end_date);
                    \Log::info('Period days', ['days' => $days]);

                    // Day count logic
                    $dayCountValue = 365; // default
                    $dayCountMethod = strtolower(trim($distribution->day_count));

                    if ($dayCountMethod === '30/360') {
                        $dayCountValue = 360;
                    } elseif ($dayCountMethod === '30/365') {
                        $dayCountValue = 365;
                    } elseif ($dayCountMethod === 'actual/360') {
                        $dayCountValue = 360;
                    } elseif ($dayCountMethod === 'actual/365' || $dayCountMethod === 'actual/365 (most common)') {
                        $dayCountValue = 365;
                    } elseif ($dayCountMethod === 'actual/actual') {
                        $year = Carbon::parse($distribution->start_date)->year;
                        $dayCountValue = Carbon::parse("$year-12-31")->dayOfYear;
                    } elseif (is_numeric($distribution->day_count)) {
                        $dayCountValue = (float) $distribution->day_count;
                    } elseif (preg_match('/(\d+)\s*\/\s*(\d+)/', $distribution->day_count, $matches)) {
                        $dayCountValue = (float) $matches[2] ?: 365;
                    }

                    \Log::info('Day count value', ['day_count' => $dayCountValue]);

                    $dailyRate = $prefReturnRate / $dayCountValue;
                    $allocAmount = $investmentAmount * $dailyRate * $days;
                    \Log::info('Simple interest calculation', [
                        'investment_id' => $investment->id,
                        'allocAmount' => $allocAmount
                    ]);

                    if ($distribution->compounding_period && $distribution->compounding_period !== 'No Compounding') {
                        $n = match ($distribution->compounding_period) {
                            'Monthly' => 12,
                            'Quarterly' => 4,
                            'Biyearly' => 2,
                            'Yearly' => 1,
                            default => 1,
                        };
                        $t = $days / 365;
                        $allocAmount = $investmentAmount * (pow(1 + $prefReturnRate / $n, $n * $t) - 1);
                        \Log::info('Compounded interest calculation', [
                            'investment_id' => $investment->id,
                            'compounding_period' => $distribution->compounding_period,
                            'n' => $n,
                            't' => $t,
                            'allocAmount' => $allocAmount
                        ]);
                    }

                    // Remove the problematic small amount adjustment
                    // if ($allocAmount > 0 && $allocAmount < 1) {
                    //     $allocAmount = $investmentAmount * $allocAmount;
                    // }

                    if ($allocAmount > 0) {
                        $investmentsToDistribute[] = [
                            'investor_id' => $investment->investor_id,
                            'investment_id' => $investment->id,
                            'amount' => $allocAmount,
                            'type' => 'preferred_return', // Corrected type
                        ];
                        $investmentIds[] = $investment->id;
                        \Log::info('Investment added to distribution', [
                            'investment_id' => $investment->id,
                            'amount' => $allocAmount
                        ]);
                    } else {
                        \Log::warning('Zero or negative allocAmount, skipping investment', [
                            'investment_id' => $investment->id,
                            'allocAmount' => $allocAmount
                        ]);
                    }
                }

                $investmentIds = array_unique($investmentIds);
                \Log::info('DistributionsController@store: Investments to distribute', [
                    'investments' => $investmentsToDistribute
                ]);
                return response()->json([
                    'investments_to_distribute' => $investmentsToDistribute
                ], 200);

            } elseif ($distribution->calculation_method === 'Interest') {
                \Log::info('DistributionsController@store: Interest calculation started');
                foreach ($investments as $investment) {
                    $class = $investment->class;
                    $interestRate = $class->interest_rate / 100; // Assume interest_rate column
                    $investmentAmount = $investment->investment_amount->getAmount() / 100;

                    $days = Carbon::parse($distribution->start_date)->diffInDays($distribution->end_date);
                    $dailyRate = $interestRate / ($distribution->day_count === 'Actual/365' ? 365 : 360);
                    $allocAmount = $investmentAmount * $dailyRate * $days;

                    if ($distribution->compounding_period && $distribution->compounding_period !== 'No Compounding') {
                        $n = match ($distribution->compounding_period) {
                            'Monthly' => 12,
                            'Quarterly' => 4,
                            'Biyearly' => 2,
                            'Yearly' => 1,
                            default => 1,
                        };
                        $t = $days / 365;
                        $allocAmount = $investmentAmount * (pow(1 + $interestRate / $n, $n * $t) - 1);
                    }

                    if ($allocAmount > 0) {
                        $investmentIds[] = $investment->id;
                        Distribution::create([
                            'deal_id' => $distribution->deal_id,
                            'investor_id' => $investment->investor_id,
                            'investment_id' => $investment->id,
                            'amount' => $allocAmount * 100,
                            'distribution_date' => $distribution->distribution_date,
                            'distribution_type' => 'interest',
                            'is_visible' => $distribution->is_visible,
                            'parent_distribution_id' => $distribution->id,
                            'source' => $distribution->source,
                            'count_toward' => $distribution->count_toward,
                            'calculation_method' => $distribution->calculation_method,
                        ]);
                    }
                }

                $investmentIds = array_unique($investmentIds);
                $distribution->update(['investment_id' => json_encode($investmentIds)]);
                \Log::info('DistributionsController@store: Interest calculation completed');
            } elseif ($distribution->calculation_method === 'ReturnInvested') {
                \Log::info('DistributionsController@store: ReturnInvested calculation started');
                foreach ($investments as $investment) {
                    $s_amount = $investment->investment_amount;
                    // dd((int) str_replace(['$', ','], '', $s_amount) / 100);
                    $simpleFloat = (int) str_replace(['$', ','], '', $s_amount);
                    $investmentAmount = $simpleFloat;
                    // Since investor_id and investment_id are not stored in Distribution,
                    // we need to sum all distributions for this deal, class, and investor
                    $paidCapital = Distribution::where('deal_id', $distribution->deal_id)
                        ->where('distribution_type', 'return_of_capital')
                        ->where(function ($query) use ($investment) {
                            $query->whereJsonContains('included_classes', $investment->deal_class_id);
                        })
                        ->sum('amount') ;
                    $allocAmount = max(0, $investmentAmount - $paidCapital);
                    dd($allocAmount);
                    if ($allocAmount > 0) {
                        $investmentIds[] = $investment->id;
                        Distribution::create([
                            'deal_id' => $distribution->deal_id,
                            'investor_id' => $investment->investor_id,
                            'investment_id' => $investment->id,
                            'amount' => $allocAmount * 100,
                            'distribution_date' => $distribution->distribution_date,
                            'distribution_type' => 'return_of_capital',
                            'is_visible' => $distribution->is_visible,
                            'parent_distribution_id' => $distribution->id,
                            'source' => $distribution->source,
                            'count_toward' => $distribution->count_toward,
                            'calculation_method' => $distribution->calculation_method,
                        ]);
                    }
                }

                $investmentIds = array_unique($investmentIds);
                $distribution->update(['investment_id' => json_encode($investmentIds)]);
                \Log::info('DistributionsController@store: ReturnInvested calculation completed');
            } elseif ($distribution->calculation_method === 'Custom') {
                \Log::info('DistributionsController@store: Custom calculation started');
                foreach ($request->custom_amounts ?? [] as $investmentId => $amount) {
                    $investment = $investments->firstWhere('id', $investmentId);
                    if (!$investment) {
                        continue;
                    }

                    $allocAmount = (float) $amount;
                    if ($allocAmount > 0) {
                        $investmentIds[] = $investment->id;
                        Distribution::create([
                            'deal_id' => $distribution->deal_id,
                            'investor_id' => $investment->investor_id,
                            'investment_id' => $investment->id,
                            'amount' => $allocAmount * 100,
                            'distribution_date' => $distribution->distribution_date,
                            'distribution_type' => $distribution->distribution_type ?? 'custom',
                            'is_visible' => $distribution->is_visible,
                            'parent_distribution_id' => $distribution->id,
                            'source' => $distribution->source,
                            'count_toward' => $distribution->count_toward,
                            'calculation_method' => $distribution->calculation_method,
                        ]);
                    }
                }

                $investmentIds = array_unique($investmentIds);
                $distribution->update(['investment_id' => json_encode($investmentIds)]);
                \Log::info('DistributionsController@store: Custom calculation completed');
            }


            \Log::info('DistributionsController@store: Completed successfully', [
                'distribution_id' => $distribution->id,
                'investment_ids' => $investmentIds,
            ]);

            return response()->json([
                'message' => 'Distribution created successfully',
                'distribution_id' => $distribution->id,
                'investment_ids' => $investmentIds,
            ], 200);
        } catch (\Exception $e) {
            \Log::error('DistributionsController@store: Exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function toggleVisibility(Request $request, $id)
    {
        $distribution = Distribution::findOrFail($id);
        $distribution->is_visible = $request->is_visible;
        $distribution->save();

        return response()->json(['success' => true]);
    }

    public function destroy($id)
    {
        $distribution = Distribution::find($id);
        if ($distribution) {
            $distribution->delete();
            return response()->json(['success' => true]);
        }
        return response()->json(['success' => false, 'message' => 'Distribution not found.']);
    }


}
