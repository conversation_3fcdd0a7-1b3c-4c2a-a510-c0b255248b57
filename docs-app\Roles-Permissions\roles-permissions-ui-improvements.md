# UI Improvements for Organizational Role Management

## Overview

The role management UI needs improvements to better reflect the organizational hierarchy and provide clearer context for permissions.

## Implementation Steps

### 1. Update the Roles Blade Template

Enhance the `roles.blade.php` template to display roles by organizational hierarchy:

```blade
@extends('admin.layouts.app')
@section('panel')
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive--md table-responsive">
                        <table class="table table--light style--two">
                            <thead>
                                <tr>
                                    <th>@lang('Name')</th>
                                    <th>@lang('Permissions')</th>
                                    <th>@lang('Action')</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($organizationalHierarchy as $category => $roleNames)
                                    <tr>
                                        <td colspan="3" class="bg-primary text-white">{{ $category }}</td>
                                    </tr>
                                    @foreach($roles as $role)
                                        @if(in_array($role->name, $roleNames))
                                            <tr>
                                                <td>{{ $role->name }}</td>
                                                <td>
                                                    @php
                                                        $rolePermissions = $role->permissions->pluck('name')->toArray();
                                                        $permissionCount = count($rolePermissions);
                                                        $displayLimit = 3;
                                                    @endphp
                                                    
                                                    @if($permissionCount > 0)
                                                        @foreach(array_slice($rolePermissions, 0, $displayLimit) as $permission)
                                                            <span class="badge badge--primary">{{ $permission }}</span>
                                                        @endforeach
                                                        
                                                        @if($permissionCount > $displayLimit)
                                                            <span class="badge badge--dark">+{{ $permissionCount - $displayLimit }} more</span>
                                                        @endif
                                                    @else
                                                        <span class="badge badge--danger">No permissions</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($role->name != 'Super Admin')
                                                        <a href="{{ route('admin.staff.roles.edit', $role->id) }}" class="btn btn-sm btn--primary">
                                                            <i class="las la-edit"></i> @lang('Edit')
                                                        </a>
                                                        
                                                        @if(!in_array($role->name, ['CEO', 'CFO', 'COO', 'CTO', 'CLO']) || auth()->guard('admin')->user()->hasRole('Super Admin'))
                                                            <button type="button" class="btn btn-sm btn--danger deleteBtn" data-id="{{ $role->id }}">
                                                                <i class="las la-trash"></i> @lang('Delete')
                                                            </button>
                                                        @endif
                                                    @else
                                                        <span class="badge badge--dark">Protected</span>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endif
                                    @endforeach
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Role Creation Modal -->
    <div id="createModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">@lang('Create New Role')</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form action="{{ route('admin.staff.roles.store') }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="form-group">
                            <label>@lang('Role Name')</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="form-group">
                            <label>@lang('Permissions')</label>
                            @foreach($permissions as $category => $categoryPermissions)
                                <div class="permission-category mt-3">
                                    <h6 class="font-weight-bold">{{ ucfirst($category) }}</h6>
                                    <div class="row">
                                        @foreach($categoryPermissions as $permission)
                                            <div class="col-md-6">
                                                <div class="custom-control custom-checkbox">
                                                    <input type="checkbox" class="custom-control-input" id="permission-{{ $permission->id }}" name="permissions[]" value="{{ $permission->name }}">
                                                    <label class="custom-control-label" for="permission-{{ $permission->id }}">{{ $permission->name }}</label>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn--dark" data-dismiss="modal">@lang('Cancel')</button>
                        <button type="submit" class="btn btn--primary">@lang('Create')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Role Deletion Modal -->
    <div id="deleteModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">@lang('Delete Role')</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form action="" method="POST">
                    @csrf
                    @method('DELETE')
                    <div class="modal-body">
                        <p>@lang('Are you sure you want to delete this role?')</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn--dark" data-dismiss="modal">@lang('Cancel')</button>
                        <button type="submit" class="btn btn--danger">@lang('Delete')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('breadcrumb-plugins')
    <button class="btn btn-sm btn--primary box--shadow1 text--small" data-toggle="modal" data-target="#createModal">
        <i class="las la-plus"></i> @lang('Create New Role')
    </button>
@endpush

@push('script')
<script>
    (function($){
        "use strict";
        
        $('.deleteBtn').on('click', function() {
            var modal = $('#deleteModal');
            modal.find('form').attr('action', '{{ route('admin.staff.roles.destroy', '') }}/' + $(this).data('id'));
            modal.modal('show');
        });
    })(jQuery);
</script>
@endpush
```

### 2. Update the Edit Role Blade Template

Enhance the `edit_role.blade.php` template to display permissions by department:

```blade
@extends('admin.layouts.app')
@section('panel')
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <form action="{{ route('admin.staff.roles.update', $role->id) }}" method="POST">
                        @csrf
                        @method('PUT')
                        <div class="form-group">
                            <label>@lang('Role Name')</label>
                            <input type="text" class="form-control" name="name" value="{{ $role->name }}" required>
                        </div>
                        
                        <div class="form-group">
                            <label>@lang('Permissions')</label>
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="checkAll">
                                        <label class="custom-control-label font-weight-bold" for="checkAll">@lang('Select All')</label>
                                    </div>
                                </div>
                            </div>
                            
                            @foreach($permissions as $category => $categoryPermissions)
                                <div class="permission-category mt-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="font-weight-bold">{{ isset($departments[$category]) ? $departments[$category] : ucfirst($category) }}</h6>
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input check-category" id="category-{{ $category }}">
                                            <label class="custom-control-label" for="category-{{ $category }}">@lang('Select All')</label>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        @foreach($categoryPermissions as $permission)
                                            <div class="col-md-6 mb-2">
                                                <div class="custom-control custom-checkbox">
                                                    <input type="checkbox" class="custom-control-input permission-checkbox category-{{ $category }}" id="permission-{{ $permission->id }}" name="permissions[]" value="{{ $permission->name }}" {{ $role->hasPermissionTo($permission->name, 'admin') ? 'checked' : '' }}>
                                                    <label class="custom-control-label" for="permission-{{ $permission->id }}">{{ $permission->name }}</label>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn--primary btn-block">@lang('Update')</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
<script>
    (function($){
        "use strict";
        
        // Check all permissions
        $('#checkAll').on('change', function() {
            var isChecked = $(this).prop('checked');
            $('.permission-checkbox').prop('checked', isChecked);
            $('.check-category').prop('checked', isChecked);
        });
        
        // Check all permissions in a category
        $('.check-category').on('change', function() {
            var categoryClass = $(this).attr('id').replace('category-', 'category-');
            var isChecked = $(this).prop('checked');
            $('.' + categoryClass).prop('checked', isChecked);
            
            // Update checkAll status
            updateCheckAllStatus();
        });
        
        // Update category checkbox status when individual permissions change
        $('.permission-checkbox').on('change', function() {
            var categoryClass = $(this).attr('class').split(' ').find(cls => cls.startsWith('category-'));
            var totalInCategory = $('.' + categoryClass).length;
            var checkedInCategory = $('.' + categoryClass + ':checked').length;
            
            $('#' + categoryClass).prop('checked', totalInCategory === checkedInCategory);
            
            // Update checkAll status
            updateCheckAllStatus();
        });
        
        // Update checkAll status based on all permissions
        function updateCheckAllStatus() {
            var totalPermissions = $('.permission-checkbox').length;
            var checkedPermissions = $('.permission-checkbox:checked').length;
            
            $('#checkAll').prop('checked', totalPermissions === checkedPermissions);
        }
        
        // Initialize category checkboxes
        $('.check-category').each(function() {
            var categoryClass = $(this).attr('id').replace('category-', 'category-');
            var totalInCategory = $('.' + categoryClass).length;
            var checkedInCategory = $('.' + categoryClass + ':checked').length;
            
            $(this).prop('checked', totalInCategory === checkedInCategory);
        });
        
        // Initialize checkAll
        updateCheckAllStatus();
    })(jQuery);
</script>
@endpush
```

### 3. Update the Create Staff Blade Template

Enhance the `create.blade.php` template to group roles by organizational hierarchy:

```blade
@extends('admin.layouts.app')
@section('panel')
    <div class="row mb-none-30">
        <div class="col-lg-12 mb-30">
            <div class="card">
                <div class="card-body">
                    <form action="{{ route('admin.staff.store') }}" method="POST">
                        @csrf
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>@lang('Name')</label>
                                    <input type="text" name="name" class="form-control" value="{{ old('name') }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>@lang('Email')</label>
                                    <input type="email" name="email" class="form-control" value="{{ old('email') }}" required>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>@lang('Username')</label>
                                    <input type="text" name="username" class="form-control" value="{{ old('username') }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>@lang('Role')</label>
                                    <select name="role" class="form-control" required>
                                        <option value="">@lang('Select Role')</option>
                                        
                                        @foreach($roleGroups as $category => $groupRoles)
                                            <optgroup label="{{ $category }}">
                                                @foreach($roles as $role)
                                                    @if(in_array($role->name, $groupRoles))
                                                        <option value="{{ $role->id }}">{{ $role->name }}</option>
                                                    @endif
                                                @endforeach
                                            </optgroup>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>@lang('Password')</label>
                                    <input type="password" name="password" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>@lang('Confirm Password')</label>
                                    <input type="password" name="password_confirmation" class="form-control" required>
                                </div>
                            </div>
                        </div>
                        <div class="form-group mt-3">
                            <button type="submit" class="btn btn--primary btn-block">@lang('Create Staff')</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
```
