# Roles & Permissions Implementation Guide

## Core Principle

> **IMPORTANT**: Roles and permissions are there to interact with existing modules and not the other way around. We want to ensure the Staff are created and assigned specific permissions on modules and features. The modules should NOT be changed to adjust to the roles and permissions structure.

This core principle guides our entire implementation strategy. The roles and permissions system must adapt to the existing architecture, not vice versa.

## Overview

This guide provides a comprehensive roadmap for implementing the organizational roles and permissions system in the CREI Investment Platform. The implementation aligns the application's permission structure with the organizational hierarchy while maintaining compatibility with existing functionality.

## Implementation Steps

### Step 1: Update Database Seeder

The first step is to update the `RoleAndPermissionSeeder` to include all organizational roles and their corresponding permissions. This establishes the foundation for the entire permission system.

Refer to: [Role Seeder Implementation](./roles-permissions-seeder-implementation.md)

### Step 2: Enhance Permission Middleware

Next, enhance the `PermissionMiddleware` to implement hierarchical permission checks based on organizational roles. This ensures that executives and department managers have appropriate access to their areas of responsibility.

Refer to: [Middleware Enhancements](./roles-permissions-middleware-enhancements.md)

### Step 3: Update Staff Controller

Update the `StaffController` to support the organizational hierarchy by adding protection for executive roles and improving the role management interface.

Refer to: [Controller Updates](./roles-permissions-controller-updates.md)

### Step 4: Improve UI Templates

Enhance the role management UI to better reflect the organizational hierarchy and provide clearer context for permissions.

Refer to: [UI Improvements](./roles-permissions-ui-improvements.md)

### Step 5: Test Implementation

Thoroughly test the implementation to ensure that all roles have appropriate access levels and that the hierarchical permission structure is properly enforced.

Refer to: [Testing Plan](./roles-permissions-testing-plan.md)

## Execution Order

For a smooth implementation, follow this order:

1. **Backup Database**: Create a backup of the current database
2. **Update Seeder**: Implement the enhanced `RoleAndPermissionSeeder`
3. **Run Seeder**: Execute the seeder to populate roles and permissions
4. **Enhance Middleware**: Implement the hierarchical permission checks
5. **Update Controller**: Modify the `StaffController` for organizational roles
6. **Update UI**: Enhance the role management templates
7. **Test Implementation**: Verify that everything works as expected
8. **Deploy Changes**: Roll out the changes to production

## Important Considerations

### Laravel MVC Architecture

All changes must follow Laravel's MVC implementation as specified in the global rules:
- Use Eloquent ORM for data access
- Implement middleware for request handling
- Maintain proper separation of concerns

### Authentication Guards

As specified in the Staff Management System documentation:
- Admin roles and permissions use 'admin' guard
- Regular user roles use 'web' guard
- Guard names must be explicitly set during creation

### Protected Systems

Be careful when modifying permission checks for protected systems:
- Waterfall Distribution System
- Investment Processing
- Asset Management
- Payment Gateways
- Document Systems
- KYC/AML Systems

### Backward Compatibility

Ensure backward compatibility by:
- Preserving existing role names ('Super Admin', 'Staff', 'Manager')
- Maintaining the existing permission structure (e.g., 'deals.view', 'investments.create')
- Adding new organizational roles and permissions without removing existing ones
- Ensuring all existing functionality continues to work

## Reference Documentation

- [Database Schema](./roles-permissions-database-schema.md)
- [Role Seeder Implementation](./roles-permissions-seeder-implementation.md)
- [Middleware Enhancements](./roles-permissions-middleware-enhancements.md)
- [Controller Updates](./roles-permissions-controller-updates.md)
- [UI Improvements](./roles-permissions-ui-improvements.md)
- [Testing Plan](./roles-permissions-testing-plan.md)
