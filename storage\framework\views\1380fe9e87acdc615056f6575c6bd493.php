<?php
    $footerContent = getContent('footer.content', true);
    $footerElements = getContent('footer.element', limit: 4, orderById: true);
    $subscribeContent = getContent('subscribe.content', true);
    $contactContent = getContent('contact_us.content', true);
    $policyPages = getContent('policy_pages.element', false, null, true);
?>

<footer class="footer bg-pattern2">
    <div class="container ">
        <div class="footer-top pt-60">
            <div class="row g-4 g-lg-5">
                <div class="col-lg-3 col-sm-6">
                    <div class="footer-item">
                        <a href="<?php echo e(route('home')); ?>" class="footer-item__logo">
                            <img src="<?php echo e(siteLogo('dark')); ?>" alt="logo-image">
                        </a>
                        <p class="footer-item__desc"><?php echo e(__(@$footerContent->data_values->description)); ?></p>
                        <ul class="social-list">
                            <?php $__currentLoopData = @$footerElements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $footerElement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="social-list__item">
                                    <a href="<?php echo e(@$footerElement->data_values->social_link); ?>" target="_blank"
                                        class="social-list__link">
                                        <?php echo @$footerElement->data_values->social_icon ?>
                                    </a>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-2 col-sm-6 col-xsm-6">
                    <div class="footer-item">
                        <h6 class="footer-item__title"><?php echo app('translator')->get('Quick links'); ?></h6>
                        <ul class="footer-menu style-two">
                            <li class="footer-menu__item">
                                <a href="<?php echo e(route('blog')); ?>" class="footer-menu__link"><?php echo app('translator')->get('Home'); ?></a>
                            </li>
                            <li class="footer-menu__item">
                                <a href="<?php echo e(route('blog')); ?>" class="footer-menu__link"><?php echo app('translator')->get('Blog'); ?></a>
                            </li>
                            <li class="footer-menu__item">
                                <a href="<?php echo e(route('contact')); ?>" class="footer-menu__link"><?php echo app('translator')->get('Contact Us'); ?></a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-3 col-sm-6 col-xsm-6">
                    <div class="footer-item">
                        <h6 class="footer-item__title"><?php echo app('translator')->get('Contact us'); ?></h6>
                        <ul class="footer-menu">
                            <li class="footer-menu__item">
                                <span class="footer-menu__link">
                                    <?php
                                        echo @$contactContent->data_values->address_icon;
                                    ?>
                                    <?php echo e(__(@$contactContent->data_values->address)); ?>

                                </span>
                            </li>
                            <li class="footer-menu__item">
                                <span class="footer-menu__link">
                                    <?php echo @$contactContent->data_values->email_icon; ?>
                                    <a
                                        href="mailto:<?php echo e(@$contactContent->data_values->email); ?>"><?php echo e(@$contactContent->data_values->email); ?></a>
                                </span>
                            </li>
                            <li class="footer-menu__item">
                                <span class="footer-menu__link">
                                    <?php echo @$contactContent->data_values->mobile_icon;?>
                                    <a href="tel:<?php echo e(str_replace(' ', '', @$contactContent->data_values->mobile_number)); ?>"
                                        class="text--body">
                                        <?php echo e(@$contactContent->data_values->mobile_number); ?>

                                    </a>
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-4 col-sm-6">
                    <div class="footer-item">
                        <h6 class="footer-item__title"><?php echo e(__(@$subscribeContent->data_values->heading)); ?></h6>
                        <p class="footer-item__desc"><?php echo e(__(@$subscribeContent->data_values->subheading)); ?></p>
                        <form class="subscribe-form" id="subscribeForm">
                            <?php echo csrf_field(); ?>
                            <input class="form--control" type="email" name="email" placeholder="<?php echo app('translator')->get('Email Address'); ?>">
                            <button class="btn btn--base btn--sm" type="submit"><?php echo app('translator')->get('Subscribe'); ?></button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p class="copyright">
                &copy; <?php echo now()->year; ?>
                <a href="<?php echo e(route('home')); ?>"><?php echo e(gs('site_name')); ?></a>
                . <?php echo app('translator')->get('All Rights Reserved'); ?>
            </p>
            <div class="footer-links">
                <?php $__currentLoopData = $policyPages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $policy): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <a class="footer-link"
                        href="<?php echo e(route('policy.pages', slug($policy->data_values->title))); ?>"><?php echo e(__($policy->data_values->title)); ?></a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
</footer>
<?php $__env->startPush('script'); ?>
    <script>
        "use strict";
        (function($) {
            var form = $("#subscribeForm");
            form.on('submit', function(e) {
                e.preventDefault();
                var data = form.serialize();
                $.ajax({
                    url: `<?php echo e(route('subscribe')); ?>`,
                    method: 'post',
                    data: data,
                    success: function(response) {
                        if (response.success) {
                            form.find('input[name=email]').val('');
                            notify('success', response.message);
                        } else {
                            notify('error', response.error);
                        }
                    }
                });
            });
        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\laragon\www\PLATFORM\resources\views/templates/basic/partials/footer.blade.php ENDPATH**/ ?>