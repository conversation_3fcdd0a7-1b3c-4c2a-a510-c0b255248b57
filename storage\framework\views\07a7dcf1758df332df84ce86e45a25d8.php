<?php $__env->startSection('main-content'); ?>
    <?php echo $__env->make($activeTemplate . 'partials.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <div class="dashboard position-relative">
        <div class="mx-3">
            <div class="dashboard__wrapper">
                <?php if(auth()->check()): ?>
                    <?php echo $__env->make($activeTemplate . 'partials.sidenav', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <?php endif; ?>
                <div class="dashboard-body">
                    <?php echo $__env->yieldContent('content'); ?>
                </div>
            </div>
        </div>
    </div>
    <?php echo $__env->make($activeTemplate . 'partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('style'); ?>
    <style>
        /* Hide breadcrumb completely */
        section.breadcrumb {
            display: none !important;
        }
        
        /* Match live reference header size */
        .header {
            padding: 0.5rem 0;
            min-height: auto;
        }
        
        /* Match live reference spacing */
        .dashboard {
            padding: 0;
        }
        
        .dashboard-body {
            padding-top: 1rem;
        }
        
        /* Adjust container spacing */
        .container {
            padding-top: 0.5rem;
        }
        
        /* Clean up extra margins */
        .mb-5 {
            margin-bottom: 2rem !important;
        }
        
        .mt-5 {
            margin-top: 2rem !important;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        $('.showFilterBtn').on('click', function() {
            $('.responsive-filter-card').slideToggle();
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\PLATFORM\resources\views/templates/basic/layouts/master.blade.php ENDPATH**/ ?>