# Comprehensive Deal Access Strategy for Partners

## Overview

To allow partners to access all deal-related functionality (distributions, classes, documents, investments, KYC, manual gateway, deposits, etc.) through the partner interface while maintaining proper access controls, we propose extending our shared view strategy across all deal-related modules.

## Core Principle

Following our established principle: "Roles and permissions are there to interact with existing modules and not the other way around," we will adapt the permissions system to work with existing admin modules rather than modifying the modules themselves.

## Implementation Strategy

### 1. Extend the Admin Partner Routes

In the `admin_partner.php` file, we'll define routes that mirror the admin routes for all deal-related functionality:

```php
// In admin_partner.php

// Deal Management
Route::group(['prefix' => 'deals', 'middleware' => ['partner.deal.access']], function () {
    // Main deal routes
    Route::get('/', [DealController::class, 'index'])->name('deals.index');
    Route::get('/{id}/edit', [DealController::class, 'edit'])->name('deals.edit');
    Route::post('/{id}/update', [DealController::class, 'update'])->name('deals.update');
    
    // Deal Classes
    Route::get('/{deal_id}/classes', [DealClassController::class, 'index'])->name('deals.classes.index');
    Route::get('/{deal_id}/classes/{id}', [DealClassController::class, 'show'])->name('deals.classes.show');
    
    // Distributions
    Route::get('/{deal_id}/distributions', [DistributionsController::class, 'index'])->name('deals.distributions.index');
    Route::get('/{deal_id}/distributions/{id}', [DistributionsController::class, 'show'])->name('deals.distributions.show');
    
    // Documents
    Route::get('/{deal_id}/documents', [DocumentController::class, 'index'])->name('deals.documents.index');
    Route::get('/{deal_id}/documents/{id}', [DocumentController::class, 'show'])->name('deals.documents.show');
    
    // Investments
    Route::get('/{deal_id}/investments', [InvestmentController::class, 'index'])->name('deals.investments.index');
    Route::get('/{deal_id}/investments/{id}', [InvestmentController::class, 'show'])->name('deals.investments.show');
    
    // KYC
    Route::get('/{deal_id}/kyc', [KycController::class, 'index'])->name('deals.kyc.index');
    
    // Manual Gateway
    Route::get('/{deal_id}/gateways', [GatewayController::class, 'index'])->name('deals.gateways.index');
    
    // Deposits
    Route::get('/{deal_id}/deposits', [DepositController::class, 'index'])->name('deals.deposits.index');
});
```

### 2. Enhance the PartnerDealAccess Middleware

Expand the `PartnerDealAccess` middleware to handle all deal-related resources:

```php
public function handle($request, Closure $next)
{
    $partnerId = Auth::guard('admin')->id();
    $partnerDealService = app(PartnerDealService::class);
    
    // Get all deals this partner has access to
    $partnerDealIds = $partnerDealService->getPartnerDealIds($partnerId);
    
    // Store the partner's deal IDs in the request for controllers to use
    $request->attributes->set('partner_deal_ids', $partnerDealIds);
    
    // Check if we're accessing a specific deal
    if ($request->route('id') || $request->route('deal_id')) {
        $dealId = $request->route('id') ?? $request->route('deal_id');
        
        // Verify this partner has access to this deal
        if (!in_array($dealId, $partnerDealIds)) {
            $notify[] = ['error', 'You do not have access to this deal'];
            return redirect()->route('admin.deals.index')->withNotify($notify);
        }
    }
    
    // For nested resources (classes, distributions, etc.), apply additional checks
    if ($request->route('class_id') || $request->route('distribution_id') || $request->route('document_id') || $request->route('investment_id')) {
        // Implement specific checks for each resource type
        // ...
    }
    
    return $next($request);
}
```

### 3. Create Partner-Specific Controller Wrappers

For more complex functionality, create wrapper controllers in the Partner namespace that delegate to admin controllers but apply partner-specific filtering:

```php
namespace App\Http\Controllers\Partner;

use App\Http\Controllers\Admin\DistributionsController as AdminDistributionsController;
use App\Services\PartnerDealService;

class DistributionsController extends Controller
{
    protected $adminController;
    protected $partnerDealService;
    
    public function __construct(AdminDistributionsController $adminController, PartnerDealService $partnerDealService)
    {
        $this->adminController = $adminController;
        $this->partnerDealService = $partnerDealService;
        $this->middleware(['auth:admin', 'role:partner', 'partner.deal.access']);
    }
    
    public function index($dealId)
    {
        // Verify access (already done by middleware, but double-check)
        $partnerId = Auth::guard('admin')->id();
        if (!$this->partnerDealService->checkPartnerHasDealAccess($partnerId, $dealId)) {
            $notify[] = ['error', 'You do not have access to this deal'];
            return redirect()->route('admin.deals.index')->withNotify($notify);
        }
        
        // Call the admin controller method
        return $this->adminController->index($dealId);
    }
    
    // Similar methods for other actions
}
```

### 4. Implement a Global Partner Deal Scope

Create an Eloquent global scope that automatically filters deal-related queries for partners:

```php
namespace App\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Auth;
use App\Services\PartnerDealService;

class PartnerDealScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        // Only apply this scope when authenticated as a partner
        if (Auth::guard('admin')->check() && Auth::guard('admin')->user()->hasRole('partner')) {
            $partnerId = Auth::guard('admin')->id();
            $partnerDealService = app(PartnerDealService::class);
            $partnerDealIds = $partnerDealService->getPartnerDealIds($partnerId);
            
            // Apply the scope based on the model type
            if ($model instanceof Deal) {
                $builder->whereIn('id', $partnerDealIds);
            } elseif ($model instanceof DealClass || $model instanceof Distribution || $model instanceof Document) {
                $builder->whereIn('deal_id', $partnerDealIds);
            } elseif ($model instanceof Investment) {
                $builder->whereIn('deal_id', $partnerDealIds);
            }
            // Add more model types as needed
        }
    }
}
```

### 5. Modify Admin Views for Partner Context

Add conditional logic in admin views to adjust UI elements based on the user's role:

```php
@if(auth()->guard('admin')->user()->hasRole('admin'))
    <!-- Admin-only actions -->
    <a href="{{ route('admin.deals.create') }}" class="btn btn-sm btn-primary">Create Deal</a>
@endif

<!-- Actions available to both admins and partners -->
<a href="{{ route('admin.deals.show', $deal->id) }}" class="btn btn-sm btn-info">View Deal</a>
```

## Implementation Plan

1. **Phase 1: Core Deal Access**
   - Implement the admin_partner.php routes for basic deal functionality
   - Enhance the PartnerDealAccess middleware
   - Test with deal listing and viewing

2. **Phase 2: Deal Classes and Distributions**
   - Extend access to deal classes and distributions
   - Create necessary wrapper controllers
   - Implement view modifications

3. **Phase 3: Documents and Investments**
   - Extend access to documents and investments
   - Update middleware for these resource types
   - Test access controls

4. **Phase 4: KYC, Gateways, and Deposits**
   - Complete the implementation for remaining modules
   - Comprehensive testing
   - Documentation updates

## Benefits of This Approach

1. **Unified Experience**: Partners see the same interface as admins for all deal-related functionality
2. **Proper Access Control**: Partners can only access their own deals and related resources
3. **Code Reuse**: Minimal duplication of controllers and views
4. **Maintainability**: Changes to admin functionality automatically apply to partner access
5. **Scalability**: New deal-related features can be easily made available to partners

## Potential Challenges and Solutions

1. **Complex Permissions**: Some admin actions may not be appropriate for partners
   - Solution: Use role-based conditionals in views and controllers

2. **Performance Concerns**: Additional filtering may impact performance
   - Solution: Optimize queries and consider caching partner deal IDs

3. **UI Consistency**: Some admin UI elements may not make sense for partners
   - Solution: Use view composers to customize specific parts of the admin view for partners

## Conclusion

By extending our shared view strategy across all deal-related modules, we can provide partners with comprehensive access to their deals while maintaining proper access controls and leveraging existing admin functionality. This approach aligns with our core principle of adapting permissions to work with existing modules rather than modifying the modules themselves.
