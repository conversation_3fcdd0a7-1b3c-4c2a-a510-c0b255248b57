<?php
namespace App\Http\Controllers\Admin;
use App\Models\Template;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;

class TemplateController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:admin'); 
    }

    public function addTemplate(Request $request)
    {

        try {
            $design = $request->input('design');
            Template::create([
                'design' => json_encode($design),
                'name' => $request->input('name', 'Untitled Template')
            ]);
            return response()->json(['success' => true, 'message' => 'Template saved successfully']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}