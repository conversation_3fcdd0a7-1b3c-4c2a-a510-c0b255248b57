# Partner Management System Documentation

## Overview
The Partner Management System is a module within the CREI Investment Platform that allows administrators to manage partner profiles, including their basic information, company details, and logos. Partners can be assigned to deals and have specific permissions within the system.

## Key Features

### 1. Partner CRUD Operations
- **Create**: Add new partners with company information and logo
- **Read**: View partner details and associated deals
- **Update**: Modify partner information, including company details and logo
- **Delete**: Remove partners from the system with proper permission checks

### 2. Logo Management
- Upload and store partner logos in the appropriate directory
- Replace existing logos with new ones, removing old files
- Display logos on partner profile pages

### 3. Status Management
- Toggle partner status (active/inactive)
- Visual indicators for partner status

## Technical Implementation

### File Structure
```
app/
├── Http/
│   ├── Controllers/
│   │   └── Admin/
│   │       └── PartnerManagementController.php
│   └── Helpers/
│       └── ImagePathHelper.php
resources/
├── views/
│   └── admin/
│       └── partner-management/
│           ├── create.blade.php
│           ├── edit.blade.php
│           ├── index.blade.php
│           └── show.blade.php
routes/
└── admin.php
```

### Routes
Partner management routes are defined in `routes/admin.php` with proper middleware for authentication and permissions:

```php
Route::group(['middleware' => ['auth:admin', 'permission']], function () {
    Route::resource('partner', 'PartnerManagementController');
    Route::post('partner/status/{id}', 'PartnerManagementController@changeStatus')->name('partner.status');
    Route::delete('partner/delete/{id}', 'PartnerManagementController@destroy')->name('partner.delete');
});
```

### Controller Methods

#### `index()`
Displays a list of all partners with pagination and search functionality.

#### `create()`
Shows the form for creating a new partner.

#### `store(Request $request)`
Validates and stores a new partner in the database. Handles logo upload using native PHP file handling.

#### `show($id)`
Displays detailed information about a specific partner.

#### `edit($id)`
Shows the form for editing an existing partner.

#### `update(Request $request, $id)`
Validates and updates an existing partner. Handles logo replacement, removing old files when necessary.

#### `destroy($id)`
Removes a partner from the system, with proper permission checks and AJAX response handling.

#### `changeStatus($id)`
Toggles a partner's status between active and inactive.

### Logo Upload Implementation

The system uses native PHP file handling for logo uploads:

```php
// Handle logo upload if provided
if ($request->hasFile("company_logo")) {
    $location = imagePath()['profile']['partner']['path'];
    
    // Create directory if it doesn't exist
    if (!file_exists($location)) {
        mkdir($location, 0755, true);
    }
    
    // Remove old image if exists
    if ($partner->company_logo && file_exists($location . '/' . $partner->company_logo)) {
        @unlink($location . '/' . $partner->company_logo);
    }
    
    // Generate a unique filename
    $filename = time() . '_' . $request->file('company_logo')->getClientOriginalName();
    
    // Move the uploaded file to the destination
    $request->file('company_logo')->move($location, $filename);
    
    // Save the filename to the database
    $partner->company_logo = $filename;
}
```

### Logo Display

Partner logos are displayed using the `getImage()` helper function:

```php
<img src="{{ getImage(imagePath()['profile']['partner']['path'].'/'. $partner->company_logo, imagePath()['profile']['partner']['size']) }}" alt="@lang('Company Logo')" class="w-50">
```

## Challenges and Solutions

### 1. Logo Upload and GD Extension Dependency

**Challenge:**
Initially, we attempted to use the `fileUploader()` helper function which relies on the Intervention Image library for image processing. This resulted in an error because the GD PHP extension was not installed or enabled on the server:

```
Intervention\Image\Exceptions\DriverException: GD PHP extension must be installed to use this driver.
```

**Solution:**
We implemented a simplified approach using native PHP file handling functions that don't rely on external extensions:

```php
// Handle logo upload if provided
if ($request->hasFile("company_logo")) {
    $location = imagePath()['profile']['partner']['path'];
    
    // Create directory if it doesn't exist
    if (!file_exists($location)) {
        mkdir($location, 0755, true);
    }
    
    // Remove old image if exists
    if ($partner->company_logo && file_exists($location . '/' . $partner->company_logo)) {
        @unlink($location . '/' . $partner->company_logo);
    }
    
    // Generate a unique filename
    $filename = time() . '_' . $request->file('company_logo')->getClientOriginalName();
    
    // Move the uploaded file to the destination
    $request->file('company_logo')->move($location, $filename);
    
    // Save the filename to the database
    $partner->company_logo = $filename;
}
```

This approach avoids the dependency on the GD extension while still providing the necessary functionality.

### 2. Undefined Function Error

**Challenge:**
When attempting to use the `removeFile()` function to delete old logo files, we encountered an error:

```
Call to undefined function App\Http\Controllers\Admin\removeFile()
```

**Solution:**
We discovered that `removeFile()` is not a global helper function but a method of the FileManager class. Instead of trying to use this method directly, we implemented file removal using PHP's native `unlink()` function, which is more appropriate for our use case and doesn't require additional dependencies.

### 3. Incorrect Image Path

**Challenge:**
The partner logo was not displaying correctly because the view was using the admin profile path instead of the partner profile path:

```php
<img src="{{ getImage(imagePath()['profile']['admin']['path'].'/'. $partner->company_logo, imagePath()['profile']['admin']['size']) }}" alt="@lang('Company Logo')" class="w-50">
```

**Solution:**
We updated the image path in the show.blade.php file to use the correct partner profile path:

```php
<img src="{{ getImage(imagePath()['profile']['partner']['path'].'/'. $partner->company_logo, imagePath()['profile']['partner']['size']) }}" alt="@lang('Company Logo')" class="w-50">
```

This ensures that the system looks for the logo in the correct directory.

### 4. Route Naming Consistency

**Challenge:**
We encountered a `RouteNotFoundException` for routes in the admin sidebar because of a double prefix issue. Routes were defined with the full 'admin.' prefix, but the RouteServiceProvider was already configured to add this prefix.

**Solution:**
We removed the redundant 'admin.' prefix from route names in admin.php since RouteServiceProvider already adds this prefix. For example, we changed:

```php
->name('admin.partner.dashboard')
```

to:

```php
->name('partner.dashboard')
```

This allowed the RouteServiceProvider to correctly add the "admin." prefix, resulting in the final route name "admin.partner.dashboard" that matched what the sidebar expected.

## Recent Improvements

### 1. Logo Upload and Display
- Implemented native PHP file handling for logo uploads, avoiding dependency on GD extension
- Added proper file cleanup to remove old logos when replaced
- Ensured correct path usage for storing and displaying partner logos

### 2. UI Enhancements
- Updated dropdown menus to use Bootstrap 5 syntax
- Improved visual indicators for partner status

### 3. Security and Permissions
- Added proper permission checks for delete operations
- Implemented AJAX response handling for better user experience
- Added validation for all input fields

## Future Enhancements

1. Partner-Deal relationship management interface
2. Batch operations for partners (bulk status change, delete, etc.)
3. Enhanced filtering and sorting options
4. Partner activity logs and history tracking

## Troubleshooting

### Common Issues

1. **Logo Upload Fails**
   - Check directory permissions for the logo storage path
   - Ensure the web server has write access to the directory

2. **Logo Not Displaying**
   - Verify the correct path is being used in the view
   - Check if the file exists in the specified location
   - Ensure the `getImage()` helper function is being used correctly

3. **Permission Errors**
   - Verify that the user has the correct permissions assigned
   - Check the middleware configuration in the routes file
