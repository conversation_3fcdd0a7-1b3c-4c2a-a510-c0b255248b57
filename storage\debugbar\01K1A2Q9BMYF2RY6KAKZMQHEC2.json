{"__meta": {"id": "01K1A2Q9BMYF2RY6KAKZMQHEC2", "datetime": "2025-07-28 23:13:23", "utime": **********.317102, "method": "GET", "uri": "/admin/setting/system", "ip": "127.0.0.1"}, "messages": {"count": 8, "messages": [{"message": "[23:13:22] LOG.info: Permission check for: settings.view", "message_html": null, "is_string": false, "label": "info", "time": **********.836852, "xdebug_link": null, "collector": "log"}, {"message": "[23:13:22] LOG.info: User: Super admin (ID: 5)", "message_html": null, "is_string": false, "label": "info", "time": **********.837074, "xdebug_link": null, "collector": "log"}, {"message": "[23:13:22] LOG.info: User roles: Super Admin", "message_html": null, "is_string": false, "label": "info", "time": **********.844193, "xdebug_link": null, "collector": "log"}, {"message": "[23:13:22] LOG.info: User has Super Admin role - bypassing permission check for: settings.view", "message_html": null, "is_string": false, "label": "info", "time": **********.844416, "xdebug_link": null, "collector": "log"}, {"message": "[23:13:23] LOG.warning: strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\\laragon\\www\\PLATFORM\\storage\\framework\\views\\0a55897e01daddb40d816b22b2c6c8e8.php on line 10", "message_html": null, "is_string": false, "label": "warning", "time": **********.306677, "xdebug_link": null, "collector": "log"}, {"message": "[23:13:23] LOG.warning: strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\\laragon\\www\\PLATFORM\\storage\\framework\\views\\0a55897e01daddb40d816b22b2c6c8e8.php on line 10", "message_html": null, "is_string": false, "label": "warning", "time": **********.306798, "xdebug_link": null, "collector": "log"}, {"message": "[23:13:23] LOG.warning: strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\\laragon\\www\\PLATFORM\\storage\\framework\\views\\0a55897e01daddb40d816b22b2c6c8e8.php on line 10", "message_html": null, "is_string": false, "label": "warning", "time": **********.308509, "xdebug_link": null, "collector": "log"}, {"message": "[23:13:23] LOG.warning: strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\\laragon\\www\\PLATFORM\\storage\\framework\\views\\0a55897e01daddb40d816b22b2c6c8e8.php on line 10", "message_html": null, "is_string": false, "label": "warning", "time": **********.308608, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.51239, "end": **********.317123, "duration": 0.8047330379486084, "duration_str": "805ms", "measures": [{"label": "Booting", "start": **********.51239, "relative_start": 0, "end": **********.754677, "relative_end": **********.754677, "duration": 0.*****************, "duration_str": "242ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.754688, "relative_start": 0.*****************, "end": **********.317125, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "562ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.77005, "relative_start": 0.****************, "end": **********.772713, "relative_end": **********.772713, "duration": 0.0026628971099853516, "duration_str": "2.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.845664, "relative_start": 0.*****************, "end": **********.315216, "relative_end": **********.315216, "duration": 0.*****************, "duration_str": "470ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin.setting.system", "start": **********.847535, "relative_start": 0.****************, "end": **********.847535, "relative_end": **********.847535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget", "start": **********.153401, "relative_start": 0.****************, "end": **********.153401, "relative_end": **********.153401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget-2", "start": **********.154716, "relative_start": 0.6423261165618896, "end": **********.154716, "relative_end": **********.154716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget", "start": **********.243059, "relative_start": 0.7306690216064453, "end": **********.243059, "relative_end": **********.243059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget-2", "start": **********.244006, "relative_start": 0.7316160202026367, "end": **********.244006, "relative_end": **********.244006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget", "start": **********.244773, "relative_start": 0.7323830127716064, "end": **********.244773, "relative_end": **********.244773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget-2", "start": **********.245523, "relative_start": 0.73313307762146, "end": **********.245523, "relative_end": **********.245523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget", "start": **********.246179, "relative_start": 0.7337892055511475, "end": **********.246179, "relative_end": **********.246179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget-2", "start": **********.246873, "relative_start": 0.734483003616333, "end": **********.246873, "relative_end": **********.246873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget", "start": **********.247452, "relative_start": 0.7350621223449707, "end": **********.247452, "relative_end": **********.247452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget-2", "start": **********.248116, "relative_start": 0.7357261180877686, "end": **********.248116, "relative_end": **********.248116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget", "start": **********.249049, "relative_start": 0.736659049987793, "end": **********.249049, "relative_end": **********.249049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget-2", "start": **********.24992, "relative_start": 0.737529993057251, "end": **********.24992, "relative_end": **********.24992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget", "start": **********.250685, "relative_start": 0.7382950782775879, "end": **********.250685, "relative_end": **********.250685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget-2", "start": **********.251289, "relative_start": 0.7388989925384521, "end": **********.251289, "relative_end": **********.251289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget", "start": **********.251838, "relative_start": 0.739448070526123, "end": **********.251838, "relative_end": **********.251838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget-2", "start": **********.252434, "relative_start": 0.740044116973877, "end": **********.252434, "relative_end": **********.252434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget", "start": **********.253043, "relative_start": 0.7406530380249023, "end": **********.253043, "relative_end": **********.253043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget-2", "start": **********.253695, "relative_start": 0.7413051128387451, "end": **********.253695, "relative_end": **********.253695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget", "start": **********.254597, "relative_start": 0.7422070503234863, "end": **********.254597, "relative_end": **********.254597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget-2", "start": **********.255344, "relative_start": 0.7429540157318115, "end": **********.255344, "relative_end": **********.255344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget", "start": **********.255971, "relative_start": 0.7435810565948486, "end": **********.255971, "relative_end": **********.255971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget-2", "start": **********.256592, "relative_start": 0.7442021369934082, "end": **********.256592, "relative_end": **********.256592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget", "start": **********.257148, "relative_start": 0.744758129119873, "end": **********.257148, "relative_end": **********.257148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget-2", "start": **********.257749, "relative_start": 0.7453591823577881, "end": **********.257749, "relative_end": **********.257749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget", "start": **********.258302, "relative_start": 0.7459120750427246, "end": **********.258302, "relative_end": **********.258302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget-2", "start": **********.258903, "relative_start": 0.7465131282806396, "end": **********.258903, "relative_end": **********.258903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget", "start": **********.259429, "relative_start": 0.7470390796661377, "end": **********.259429, "relative_end": **********.259429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget-2", "start": **********.26, "relative_start": 0.7476100921630859, "end": **********.26, "relative_end": **********.26, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget", "start": **********.260538, "relative_start": 0.7481482028961182, "end": **********.260538, "relative_end": **********.260538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget-2", "start": **********.261105, "relative_start": 0.7487151622772217, "end": **********.261105, "relative_end": **********.261105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget", "start": **********.261653, "relative_start": 0.7492630481719971, "end": **********.261653, "relative_end": **********.261653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget-2", "start": **********.262217, "relative_start": 0.7498271465301514, "end": **********.262217, "relative_end": **********.262217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget", "start": **********.26275, "relative_start": 0.7503600120544434, "end": **********.26275, "relative_end": **********.26275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget-2", "start": **********.263378, "relative_start": 0.7509880065917969, "end": **********.263378, "relative_end": **********.263378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget", "start": **********.263908, "relative_start": 0.7515180110931396, "end": **********.263908, "relative_end": **********.263908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget-2", "start": **********.264471, "relative_start": 0.7520811557769775, "end": **********.264471, "relative_end": **********.264471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget", "start": **********.265582, "relative_start": 0.7531921863555908, "end": **********.265582, "relative_end": **********.265582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget-2", "start": **********.266219, "relative_start": 0.7538290023803711, "end": **********.266219, "relative_end": **********.266219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget", "start": **********.266809, "relative_start": 0.7544190883636475, "end": **********.266809, "relative_end": **********.266809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget-2", "start": **********.267398, "relative_start": 0.7550082206726074, "end": **********.267398, "relative_end": **********.267398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget", "start": **********.267935, "relative_start": 0.7555451393127441, "end": **********.267935, "relative_end": **********.267935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.widget-2", "start": **********.268511, "relative_start": 0.7561211585998535, "end": **********.268511, "relative_end": **********.268511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.layouts.app", "start": **********.269609, "relative_start": 0.7572190761566162, "end": **********.269609, "relative_end": **********.269609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.partials.sidenav", "start": **********.290905, "relative_start": 0.778515100479126, "end": **********.290905, "relative_end": **********.290905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.partials.topnav", "start": **********.305398, "relative_start": 0.7930080890655518, "end": **********.305398, "relative_end": **********.305398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.partials.breadcrumb", "start": **********.310644, "relative_start": 0.7982540130615234, "end": **********.310644, "relative_end": **********.310644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.layouts.master", "start": **********.311184, "relative_start": 0.7987940311431885, "end": **********.311184, "relative_end": **********.311184, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.notify", "start": **********.31273, "relative_start": 0.8003401756286621, "end": **********.31273, "relative_end": **********.31273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 31682064, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.7", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/New_York", "Locale": "en"}}, "views": {"count": 49, "nb_templates": 49, "templates": [{"name": "admin.setting.system", "param_count": null, "params": [], "start": **********.847501, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/admin/setting/system.blade.phpadmin.setting.system", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fadmin%2Fsetting%2Fsystem.blade.php&line=1", "ajax": false, "filename": "system.blade.php", "line": "?"}}, {"name": "components.widget", "param_count": null, "params": [], "start": **********.153349, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget.blade.phpcomponents.widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}}, {"name": "components.widget-2", "param_count": null, "params": [], "start": **********.15468, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget-2.blade.phpcomponents.widget-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget-2.blade.php&line=1", "ajax": false, "filename": "widget-2.blade.php", "line": "?"}}, {"name": "components.widget", "param_count": null, "params": [], "start": **********.243013, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget.blade.phpcomponents.widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}}, {"name": "components.widget-2", "param_count": null, "params": [], "start": **********.243945, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget-2.blade.phpcomponents.widget-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget-2.blade.php&line=1", "ajax": false, "filename": "widget-2.blade.php", "line": "?"}}, {"name": "components.widget", "param_count": null, "params": [], "start": **********.244722, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget.blade.phpcomponents.widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}}, {"name": "components.widget-2", "param_count": null, "params": [], "start": **********.245477, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget-2.blade.phpcomponents.widget-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget-2.blade.php&line=1", "ajax": false, "filename": "widget-2.blade.php", "line": "?"}}, {"name": "components.widget", "param_count": null, "params": [], "start": **********.246143, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget.blade.phpcomponents.widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}}, {"name": "components.widget-2", "param_count": null, "params": [], "start": **********.246837, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget-2.blade.phpcomponents.widget-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget-2.blade.php&line=1", "ajax": false, "filename": "widget-2.blade.php", "line": "?"}}, {"name": "components.widget", "param_count": null, "params": [], "start": **********.247418, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget.blade.phpcomponents.widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}}, {"name": "components.widget-2", "param_count": null, "params": [], "start": **********.248081, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget-2.blade.phpcomponents.widget-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget-2.blade.php&line=1", "ajax": false, "filename": "widget-2.blade.php", "line": "?"}}, {"name": "components.widget", "param_count": null, "params": [], "start": **********.248983, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget.blade.phpcomponents.widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}}, {"name": "components.widget-2", "param_count": null, "params": [], "start": **********.249873, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget-2.blade.phpcomponents.widget-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget-2.blade.php&line=1", "ajax": false, "filename": "widget-2.blade.php", "line": "?"}}, {"name": "components.widget", "param_count": null, "params": [], "start": **********.250653, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget.blade.phpcomponents.widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}}, {"name": "components.widget-2", "param_count": null, "params": [], "start": **********.251259, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget-2.blade.phpcomponents.widget-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget-2.blade.php&line=1", "ajax": false, "filename": "widget-2.blade.php", "line": "?"}}, {"name": "components.widget", "param_count": null, "params": [], "start": **********.251807, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget.blade.phpcomponents.widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}}, {"name": "components.widget-2", "param_count": null, "params": [], "start": **********.252401, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget-2.blade.phpcomponents.widget-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget-2.blade.php&line=1", "ajax": false, "filename": "widget-2.blade.php", "line": "?"}}, {"name": "components.widget", "param_count": null, "params": [], "start": **********.253012, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget.blade.phpcomponents.widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}}, {"name": "components.widget-2", "param_count": null, "params": [], "start": **********.253661, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget-2.blade.phpcomponents.widget-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget-2.blade.php&line=1", "ajax": false, "filename": "widget-2.blade.php", "line": "?"}}, {"name": "components.widget", "param_count": null, "params": [], "start": **********.254546, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget.blade.phpcomponents.widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}}, {"name": "components.widget-2", "param_count": null, "params": [], "start": **********.25531, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget-2.blade.phpcomponents.widget-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget-2.blade.php&line=1", "ajax": false, "filename": "widget-2.blade.php", "line": "?"}}, {"name": "components.widget", "param_count": null, "params": [], "start": **********.255939, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget.blade.phpcomponents.widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}}, {"name": "components.widget-2", "param_count": null, "params": [], "start": **********.25656, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget-2.blade.phpcomponents.widget-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget-2.blade.php&line=1", "ajax": false, "filename": "widget-2.blade.php", "line": "?"}}, {"name": "components.widget", "param_count": null, "params": [], "start": **********.257116, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget.blade.phpcomponents.widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}}, {"name": "components.widget-2", "param_count": null, "params": [], "start": **********.257717, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget-2.blade.phpcomponents.widget-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget-2.blade.php&line=1", "ajax": false, "filename": "widget-2.blade.php", "line": "?"}}, {"name": "components.widget", "param_count": null, "params": [], "start": **********.258271, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget.blade.phpcomponents.widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}}, {"name": "components.widget-2", "param_count": null, "params": [], "start": **********.258871, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget-2.blade.phpcomponents.widget-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget-2.blade.php&line=1", "ajax": false, "filename": "widget-2.blade.php", "line": "?"}}, {"name": "components.widget", "param_count": null, "params": [], "start": **********.259399, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget.blade.phpcomponents.widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}}, {"name": "components.widget-2", "param_count": null, "params": [], "start": **********.259969, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget-2.blade.phpcomponents.widget-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget-2.blade.php&line=1", "ajax": false, "filename": "widget-2.blade.php", "line": "?"}}, {"name": "components.widget", "param_count": null, "params": [], "start": **********.260507, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget.blade.phpcomponents.widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}}, {"name": "components.widget-2", "param_count": null, "params": [], "start": **********.261073, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget-2.blade.phpcomponents.widget-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget-2.blade.php&line=1", "ajax": false, "filename": "widget-2.blade.php", "line": "?"}}, {"name": "components.widget", "param_count": null, "params": [], "start": **********.261623, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget.blade.phpcomponents.widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}}, {"name": "components.widget-2", "param_count": null, "params": [], "start": **********.262186, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget-2.blade.phpcomponents.widget-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget-2.blade.php&line=1", "ajax": false, "filename": "widget-2.blade.php", "line": "?"}}, {"name": "components.widget", "param_count": null, "params": [], "start": **********.26272, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget.blade.phpcomponents.widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}}, {"name": "components.widget-2", "param_count": null, "params": [], "start": **********.263313, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget-2.blade.phpcomponents.widget-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget-2.blade.php&line=1", "ajax": false, "filename": "widget-2.blade.php", "line": "?"}}, {"name": "components.widget", "param_count": null, "params": [], "start": **********.263878, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget.blade.phpcomponents.widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}}, {"name": "components.widget-2", "param_count": null, "params": [], "start": **********.264441, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget-2.blade.phpcomponents.widget-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget-2.blade.php&line=1", "ajax": false, "filename": "widget-2.blade.php", "line": "?"}}, {"name": "components.widget", "param_count": null, "params": [], "start": **********.26555, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget.blade.phpcomponents.widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}}, {"name": "components.widget-2", "param_count": null, "params": [], "start": **********.266188, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget-2.blade.phpcomponents.widget-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget-2.blade.php&line=1", "ajax": false, "filename": "widget-2.blade.php", "line": "?"}}, {"name": "components.widget", "param_count": null, "params": [], "start": **********.266778, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget.blade.phpcomponents.widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}}, {"name": "components.widget-2", "param_count": null, "params": [], "start": **********.267367, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget-2.blade.phpcomponents.widget-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget-2.blade.php&line=1", "ajax": false, "filename": "widget-2.blade.php", "line": "?"}}, {"name": "components.widget", "param_count": null, "params": [], "start": **********.267904, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget.blade.phpcomponents.widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}}, {"name": "components.widget-2", "param_count": null, "params": [], "start": **********.26848, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/components/widget-2.blade.phpcomponents.widget-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fcomponents%2Fwidget-2.blade.php&line=1", "ajax": false, "filename": "widget-2.blade.php", "line": "?"}}, {"name": "admin.layouts.app", "param_count": null, "params": [], "start": **********.269576, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.phpadmin.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "admin.partials.sidenav", "param_count": null, "params": [], "start": **********.290858, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/admin/partials/sidenav.blade.phpadmin.partials.sidenav", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fadmin%2Fpartials%2Fsidenav.blade.php&line=1", "ajax": false, "filename": "sidenav.blade.php", "line": "?"}}, {"name": "admin.partials.topnav", "param_count": null, "params": [], "start": **********.305364, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/admin/partials/topnav.blade.phpadmin.partials.topnav", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fadmin%2Fpartials%2Ftopnav.blade.php&line=1", "ajax": false, "filename": "topnav.blade.php", "line": "?"}}, {"name": "admin.partials.breadcrumb", "param_count": null, "params": [], "start": **********.31061, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/admin/partials/breadcrumb.blade.phpadmin.partials.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fadmin%2Fpartials%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}}, {"name": "admin.layouts.master", "param_count": null, "params": [], "start": **********.311121, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/master.blade.phpadmin.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}}, {"name": "partials.notify", "param_count": null, "params": [], "start": **********.312681, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/partials/notify.blade.phppartials.notify", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fpartials%2Fnotify.blade.php&line=1", "ajax": false, "filename": "notify.blade.php", "line": "?"}}]}, "queries": {"count": 14, "nb_statements": 13, "nb_visible_statements": 14, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03584, "accumulated_duration_str": "35.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 193}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "admin", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Middleware\\RedirectIfNotAdmin.php", "line": 18}], "start": **********.796577, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:193", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 193}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=193", "ajax": false, "filename": "EloquentUserProvider.php", "line": "193"}, "connection": "main", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `admins` where `id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Middleware\\RedirectIfNotAdmin.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.804069, "duration": 0.02855, "duration_str": "28.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "main", "explain": null, "start_percent": 0, "width_percent": 79.66}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (5) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": ["App\\Models\\Admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 395}, {"index": 23, "namespace": "middleware", "name": "permission", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Middleware\\PermissionMiddleware.php", "line": 34}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 25, "namespace": "middleware", "name": "admin", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Middleware\\RedirectIfNotAdmin.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.840794, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:395", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 395}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=395", "ajax": false, "filename": "HasRoles.php", "line": "395"}, "connection": "main", "explain": null, "start_percent": 79.66, "width_percent": 3.348}, {"sql": "select count(*) as aggregate from `users` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 63}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 23}], "start": **********.2726011, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:63", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=63", "ajax": false, "filename": "AppServiceProvider.php", "line": "63"}, "connection": "main", "explain": null, "start_percent": 83.008, "width_percent": 2.316}, {"sql": "select count(*) as aggregate from `users` where `ev` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 64}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 23}], "start": **********.275181, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:64", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=64", "ajax": false, "filename": "AppServiceProvider.php", "line": "64"}, "connection": "main", "explain": null, "start_percent": 85.324, "width_percent": 0.977}, {"sql": "select count(*) as aggregate from `users` where `sv` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 65}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 23}], "start": **********.276585, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:65", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=65", "ajax": false, "filename": "AppServiceProvider.php", "line": "65"}, "connection": "main", "explain": null, "start_percent": 86.3, "width_percent": 0.893}, {"sql": "select count(*) as aggregate from `users` where `kv` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 23}], "start": **********.277917, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:66", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=66", "ajax": false, "filename": "AppServiceProvider.php", "line": "66"}, "connection": "main", "explain": null, "start_percent": 87.193, "width_percent": 0.614}, {"sql": "select count(*) as aggregate from `users` where `kv` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 67}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 23}], "start": **********.279147, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:67", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 67}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=67", "ajax": false, "filename": "AppServiceProvider.php", "line": "67"}, "connection": "main", "explain": null, "start_percent": 87.807, "width_percent": 0.698}, {"sql": "select count(*) as aggregate from `support_tickets` where `status` in (0, 2)", "type": "query", "params": [], "bindings": [0, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 68}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 23}], "start": **********.281233, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:68", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=68", "ajax": false, "filename": "AppServiceProvider.php", "line": "68"}, "connection": "main", "explain": null, "start_percent": 88.504, "width_percent": 1.563}, {"sql": "select count(*) as aggregate from `deposits` where `method_code` >= 1000 and `status` = 2", "type": "query", "params": [], "bindings": [1000, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 69}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 23}], "start": **********.283345, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:69", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=69", "ajax": false, "filename": "AppServiceProvider.php", "line": "69"}, "connection": "main", "explain": null, "start_percent": 90.067, "width_percent": 1.563}, {"sql": "select count(*) as aggregate from `withdrawals` where `status` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 23}], "start": **********.2853842, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:70", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=70", "ajax": false, "filename": "AppServiceProvider.php", "line": "70"}, "connection": "main", "explain": null, "start_percent": 91.629, "width_percent": 1.256}, {"sql": "select * from `profits` where `status` = 0 group by `property_id`", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 72}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 22, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 23}], "start": **********.288672, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:72", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=72", "ajax": false, "filename": "AppServiceProvider.php", "line": "72"}, "connection": "main", "explain": null, "start_percent": 92.885, "width_percent": 2.762}, {"sql": "select * from `admin_notifications` where `is_read` = 0 order by `id` desc limit 10", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 78}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 22, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 24}], "start": **********.3013759, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:78", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=78", "ajax": false, "filename": "AppServiceProvider.php", "line": "78"}, "connection": "main", "explain": null, "start_percent": 95.647, "width_percent": 3.125}, {"sql": "select count(*) as aggregate from `admin_notifications` where `is_read` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 79}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 24}], "start": **********.303852, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:79", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=79", "ajax": false, "filename": "AppServiceProvider.php", "line": "79"}, "connection": "main", "explain": null, "start_percent": 98.772, "width_percent": 1.228}]}, "models": {"data": {"App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/setting/system", "action_name": "admin.setting.system", "controller_action": "App\\Http\\Controllers\\Admin\\GeneralSettingController@systemSetting", "uri": "GET admin/setting/system", "controller": "App\\Http\\Controllers\\Admin\\GeneralSettingController@systemSetting<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FAdmin%2FGeneralSettingController.php&line=13\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "/admin", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FAdmin%2FGeneralSettingController.php&line=13\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/GeneralSettingController.php:13-18</a>", "middleware": "web, admin, permission:settings.view", "duration": "806ms", "peak_memory": "32MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1948604006 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1948604006\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2056094288 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2056094288\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2030035335 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/admin/deals/2/summary</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"105 characters\">XSRF-TOKEN=pLm3TihqRtb34dGMldUxyW5DOGNvHVKdyksoPZZ8; cre_session=aMheYUN422CXHKbhZMa5iyXxDfyUJaMeBlHhboSA</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2030035335\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-551261573 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pLm3TihqRtb34dGMldUxyW5DOGNvHVKdyksoPZZ8</span>\"\n  \"<span class=sf-dump-key>cre_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aMheYUN422CXHKbhZMa5iyXxDfyUJaMeBlHhboSA</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-551261573\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1987181538 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 03:13:22 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1987181538\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1989411256 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pLm3TihqRtb34dGMldUxyW5DOGNvHVKdyksoPZZ8</span>\"\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/placeholder-image/400x400</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1989411256\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/setting/system", "action_name": "admin.setting.system", "controller_action": "App\\Http\\Controllers\\Admin\\GeneralSettingController@systemSetting"}, "badge": null}}