# Partner Status Management System

## Overview

The Partner Status Management System allows administrators to control the status of partners in the CRE Investment Platform. This system supports multiple status types to provide granular control over partner access and visibility.

## Status Types

| Status Code | Status Name | Description | UI Indicator |
|-------------|-------------|-------------|-------------|
| 1 | Active | Partner has full access to the platform and can view/manage assigned deals | Green badge |
| 0 | Inactive | Partner account exists but cannot access the platform | Red badge |
| 2 | Paused | Partner account is temporarily suspended (e.g., pending review or temporary issue) | Yellow badge |
| 3 | Terminated | Partner account has been permanently terminated due to policy violations | Dark badge |

## Technical Implementation

### Database Structure

The status is stored in the `admins` table as a `tinyInteger` column with the following values:

```php
// Status values in the database
1 = active
0 = inactive
2 = paused
3 = terminated
```

### Controller Logic

The status management is handled by the `PartnerManagementController::toggleStatus()` method, which supports both simple toggling and explicit status setting:

```php
public function toggleStatus($id, Request $request)
{
    $partner = Admin::findOrFail($id);
    $newStatus = $request->input('status', null);
    
    // If no specific status is provided, toggle between active and inactive
    if ($newStatus === null) {
        $partner->status = $partner->status == 1 ? 0 : 1;
    } else {
        // Set to the specified status
        $partner->status = $newStatus;
    }
    
    $partner->save();
    
    $statusMessages = [
        0 => 'deactivated',
        1 => 'activated',
        2 => 'paused',
        3 => 'terminated'
    ];
    
    $statusText = $statusMessages[$partner->status] ?? 'updated';
    
    return redirect()->route('admin.partner-management.index')
        ->with('success', "Partner has been {$statusText} successfully.");
}
```

### Routes

The status management is accessible via the following route:

```php
Route::get('/{partner}/status', [PartnerManagementController::class, 'toggleStatus'])
    ->name('partner-management.status');
```

## User Interface

### Status Indicators

Each partner's status is displayed in the partner management list with a color-coded badge:

- **Active**: Green badge
- **Inactive**: Red badge
- **Paused**: Yellow badge
- **Terminated**: Dark badge

### Status Management UI

Administrators can change a partner's status through a dropdown menu in the action column. The dropdown includes options to set the partner to any status other than their current one.

## Usage Guidelines

### When to Use Each Status

- **Active**: Use for partners in good standing who should have full access to the platform.
- **Inactive**: Use for partners who are temporarily not using the platform but may return in the future.
- **Paused**: Use when there's a need to temporarily restrict access pending review or resolution of issues.
- **Terminated**: Use only in serious cases where a partner has violated platform policies and should not be reinstated.

### Status Change Workflow

1. Navigate to the Partner Management page
2. Locate the partner whose status needs to be changed
3. Click the settings (cog) icon in the Actions column
4. Select the desired status from the dropdown menu
5. The system will update the status and display a confirmation message

## Security Considerations

- Status changes are logged in the system for audit purposes
- Only administrators with appropriate permissions can change partner statuses
- Partners with non-active statuses cannot access the platform

## Future Enhancements

- Add notification system to alert partners of status changes
- Implement automatic status changes based on activity/inactivity
- Add reason field for status changes, especially for Paused and Terminated statuses
- Create a status history log to track all status changes over time
