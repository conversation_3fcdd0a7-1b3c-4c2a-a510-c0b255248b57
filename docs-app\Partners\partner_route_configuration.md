# Partner Route Configuration Guide

## Overview

This document provides detailed instructions for configuring routes for the Partner interface. The key principle is to use existing admin controllers with partner-specific routes and middleware.

## Implementation Steps

### 1. Update RouteServiceProvider

Open `app/Providers/RouteServiceProvider.php` and add the partner routes registration:

```php
// Partner routes
Route::prefix("partner")
    ->middleware(["web", "auth:admin", "role:partner"])
    ->namespace($this->namespace . "\\Admin")  // Use existing Admin controllers
    ->name("partner.")
    ->group(base_path("routes/partner.php"));
```

This configuration:
- Uses the `/partner` URL prefix
- Applies the `web`, `auth:admin`, and `role:partner` middleware
- Uses the same namespace as admin controllers
- Adds the `partner.` prefix to route names
- Loads routes from the `routes/partner.php` file

### 2. Create Partner Routes File

Create a new file at `routes/partner.php`:

```php
<?php

use App\Http\Controllers\Admin\DealController;
use Illuminate\Support\Facades\Route;

// Dashboard (uses the deals index as the dashboard)
Route::get('/dashboard', [DealController::class, 'index'])->name('dashboard');

// Deal routes with access control middleware
Route::group(['middleware' => ['partner.deal.access']], function () {
    // Deals
    Route::get('/deals', [DealController::class, 'index'])->name('deals.index');
    Route::get('/deals/create', [DealController::class, 'create'])->name('deals.create');
    Route::post('/deals', [DealController::class, 'store'])->name('deals.store');
    Route::get('/deals/{deal}', [DealController::class, 'show'])->name('deals.show');
    Route::get('/deals/{deal}/edit', [DealController::class, 'edit'])->name('deals.edit');
    Route::put('/deals/{deal}', [DealController::class, 'update'])->name('deals.update');
    Route::delete('/deals/{deal}', [DealController::class, 'destroy'])->name('deals.destroy');
});

// Profile routes
Route::get('/profile', [DealController::class, 'index'])->name('profile.index');
```

### 3. Clear Route Cache

After making changes to the routes, clear the route cache:

```bash
php artisan optimize:clear
```

## Route Naming Convention

The route naming convention is important for the Partner interface:

- Admin routes: `admin.deals.index`, `admin.deals.create`, etc.
- Partner routes: `partner.deals.index`, `partner.deals.create`, etc.

This separation allows controllers to determine which view to render based on the route name.

## Controller Modifications

Controllers need to check the route name to determine which view to render. Here's an example for the `index` method in `DealController`:

```php
public function index(Request $request)
{
    $user = Auth::guard('admin')->user();
    $isPartner = $user->hasRole('Partner');
    
    // For partners, only show their deals
    if ($isPartner) {
        $dealIds = PartnerDeal::where('admin_id', $user->id)->pluck('deal_id');
        $deals = Deal::whereIn('id', $dealIds)->get();
    } else {
        // For admins, show all deals
        $deals = Deal::all();
    }
    
    // Determine which view to use based on the route
    $routeName = $request->route()->getName();
    if (strpos($routeName, 'partner.') === 0) {
        return view('partner.deals.index', compact('deals'));
    }
    
    return view('admin.deals.index', compact('deals'));
}
```

## Troubleshooting

### Common Issues

1. **Route Not Found**: Ensure that the `partner.php` file is being loaded by the RouteServiceProvider.

2. **Middleware Not Applied**: Check that the middleware is registered in `Kernel.php` and applied to the routes.

3. **Controller Not Found**: Verify that the namespace in RouteServiceProvider is correct.

### Debugging Tips

List all registered routes to verify that the partner routes are registered correctly:

```bash
php artisan route:list | grep partner
```

If routes are not appearing, try clearing the route cache again:

```bash
php artisan optimize:clear
```

This implementation follows the principle that "roles and permissions are there to interact with existing modules and not the other way around" by reusing existing controllers with partner-specific routes and middleware.
