# Extension System Database Schema

## 1. Overview

This document outlines the database schema for the Extension System. The schema consists of several tables that store extension metadata, settings, and related information.

## 2. Tables

### 2.1 Extensions Table

The `extensions` table stores metadata about installed extensions.

```sql
CREATE TABLE `extensions` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT 'Display name of the extension',
  `code` varchar(255) NOT NULL COMMENT 'Unique identifier code for the extension',
  `version` varchar(50) NOT NULL COMMENT 'Current version of the extension',
  `description` text DEFAULT NULL COMMENT 'Description of the extension',
  `author` varchar(255) DEFAULT NULL COMMENT 'Author name',
  `author_url` varchar(255) DEFAULT NULL COMMENT 'Author website URL',
  `manifest` text NOT NULL COMMENT 'Full extension manifest as JSON',
  `permissions` text DEFAULT NULL COMMENT 'Required permissions as <PERSON><PERSON><PERSON>',
  `settings` text DEFAULT NULL COMMENT 'Extension settings as JSON',
  `is_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether the extension is enabled',
  `is_core` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether this is a core extension',
  `installed_at` timestamp NULL DEFAULT NULL COMMENT 'When the extension was installed',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `extensions_code_unique` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### Field Descriptions

- **id**: Primary key
- **name**: Human-readable name of the extension
- **code**: Unique identifier for the extension (used in URLs, file paths, etc.)
- **version**: Current version of the extension (semver format)
- **description**: Detailed description of the extension's purpose and features
- **author**: Name of the extension author or organization
- **author_url**: Website URL of the author or organization
- **manifest**: Complete extension manifest as a JSON object
- **permissions**: Required permissions for the extension as a JSON array
- **settings**: Extension settings as a JSON object
- **is_enabled**: Boolean flag indicating if the extension is currently enabled
- **is_core**: Boolean flag indicating if this is a core extension (cannot be uninstalled)
- **installed_at**: Timestamp when the extension was installed

### 2.2 Extension Settings Table

The `extension_settings` table stores individual settings for extensions, allowing for more efficient querying of specific settings.

```sql
CREATE TABLE `extension_settings` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `extension_code` varchar(255) NOT NULL COMMENT 'Extension code this setting belongs to',
  `key` varchar(255) NOT NULL COMMENT 'Setting key',
  `value` text DEFAULT NULL COMMENT 'Setting value',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `extension_settings_extension_code_key_unique` (`extension_code`, `key`),
  CONSTRAINT `extension_settings_extension_code_foreign` FOREIGN KEY (`extension_code`) REFERENCES `extensions` (`code`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### Field Descriptions

- **id**: Primary key
- **extension_code**: Foreign key referencing the extension code
- **key**: Setting key name
- **value**: Setting value (stored as text, can be JSON for complex values)

### 2.3 Extension Hooks Table

The `extension_hooks` table stores registered hooks for extensions, allowing for efficient querying of hooks by name.

```sql
CREATE TABLE `extension_hooks` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `extension_code` varchar(255) NOT NULL COMMENT 'Extension code this hook belongs to',
  `hook_name` varchar(255) NOT NULL COMMENT 'Name of the hook',
  `priority` int(11) NOT NULL DEFAULT 10 COMMENT 'Hook execution priority',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `extension_hooks_hook_name_index` (`hook_name`),
  KEY `extension_hooks_extension_code_index` (`extension_code`),
  CONSTRAINT `extension_hooks_extension_code_foreign` FOREIGN KEY (`extension_code`) REFERENCES `extensions` (`code`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### Field Descriptions

- **id**: Primary key
- **extension_code**: Foreign key referencing the extension code
- **hook_name**: Name of the hook (e.g., 'deal.create.after')
- **priority**: Integer priority value for hook execution order (lower numbers execute first)

### 2.4 Extension Dependencies Table

The `extension_dependencies` table stores dependencies between extensions.

```sql
CREATE TABLE `extension_dependencies` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `extension_code` varchar(255) NOT NULL COMMENT 'Extension code that has the dependency',
  `dependency_code` varchar(255) NOT NULL COMMENT 'Extension code that is required',
  `version_constraint` varchar(50) NOT NULL COMMENT 'Version constraint for the dependency',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `extension_dependencies_extension_code_dependency_code_unique` (`extension_code`, `dependency_code`),
  KEY `extension_dependencies_dependency_code_index` (`dependency_code`),
  CONSTRAINT `extension_dependencies_extension_code_foreign` FOREIGN KEY (`extension_code`) REFERENCES `extensions` (`code`) ON DELETE CASCADE,
  CONSTRAINT `extension_dependencies_dependency_code_foreign` FOREIGN KEY (`dependency_code`) REFERENCES `extensions` (`code`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### Field Descriptions

- **id**: Primary key
- **extension_code**: Foreign key referencing the extension code that has the dependency
- **dependency_code**: Foreign key referencing the extension code that is required
- **version_constraint**: Version constraint for the dependency (e.g., '>=1.0.0')

## 3. Loopnet Extension Tables

### 3.1 Loopnet Listings Table

The `ext_loopnet_listings` table stores property listings imported from Loopnet.

```sql
CREATE TABLE `ext_loopnet_listings` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `loopnet_id` varchar(255) NOT NULL COMMENT 'Loopnet listing ID',
  `title` varchar(255) NOT NULL COMMENT 'Listing title',
  `description` text DEFAULT NULL COMMENT 'Listing description',
  `property_type` varchar(50) NOT NULL COMMENT 'Property type',
  `status` varchar(50) NOT NULL COMMENT 'Listing status',
  `price` decimal(15, 2) DEFAULT NULL COMMENT 'Listing price',
  `price_per_sqft` decimal(10, 2) DEFAULT NULL COMMENT 'Price per square foot',
  `building_size` int(11) DEFAULT NULL COMMENT 'Building size in square feet',
  `lot_size` int(11) DEFAULT NULL COMMENT 'Lot size in square feet',
  `address` varchar(255) NOT NULL COMMENT 'Property address',
  `city` varchar(100) NOT NULL COMMENT 'Property city',
  `state` varchar(50) NOT NULL COMMENT 'Property state',
  `zip` varchar(20) NOT NULL COMMENT 'Property zip code',
  `latitude` decimal(10, 7) DEFAULT NULL COMMENT 'Property latitude',
  `longitude` decimal(10, 7) DEFAULT NULL COMMENT 'Property longitude',
  `features` text DEFAULT NULL COMMENT 'Property features as JSON',
  `images` text DEFAULT NULL COMMENT 'Property images as JSON',
  `documents` text DEFAULT NULL COMMENT 'Property documents as JSON',
  `contacts` text DEFAULT NULL COMMENT 'Property contacts as JSON',
  `deal_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT 'Associated deal ID',
  `offering_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT 'Associated offering ID',
  `asset_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT 'Associated asset ID',
  `imported_at` timestamp NULL DEFAULT NULL COMMENT 'When the listing was imported',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ext_loopnet_listings_loopnet_id_unique` (`loopnet_id`),
  KEY `ext_loopnet_listings_deal_id_index` (`deal_id`),
  KEY `ext_loopnet_listings_offering_id_index` (`offering_id`),
  KEY `ext_loopnet_listings_asset_id_index` (`asset_id`),
  CONSTRAINT `ext_loopnet_listings_deal_id_foreign` FOREIGN KEY (`deal_id`) REFERENCES `deals` (`id`) ON DELETE SET NULL,
  CONSTRAINT `ext_loopnet_listings_offering_id_foreign` FOREIGN KEY (`offering_id`) REFERENCES `offerings` (`id`) ON DELETE SET NULL,
  CONSTRAINT `ext_loopnet_listings_asset_id_foreign` FOREIGN KEY (`asset_id`) REFERENCES `assets` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### Field Descriptions

- **id**: Primary key
- **loopnet_id**: Unique identifier from Loopnet
- **title**: Listing title
- **description**: Detailed description of the property
- **property_type**: Type of property (office, retail, industrial, etc.)
- **status**: Listing status (active, pending, sold, etc.)
- **price**: Listing price
- **price_per_sqft**: Price per square foot
- **building_size**: Building size in square feet
- **lot_size**: Lot size in square feet
- **address**: Street address
- **city**: City
- **state**: State
- **zip**: ZIP code
- **latitude**: Geographic latitude
- **longitude**: Geographic longitude
- **features**: Property features as JSON
- **images**: Property images as JSON
- **documents**: Property documents as JSON
- **contacts**: Property contacts as JSON
- **deal_id**: Foreign key to the deals table
- **offering_id**: Foreign key to the offerings table
- **asset_id**: Foreign key to the assets table
- **imported_at**: Timestamp when the listing was imported as a deal/offering

### 3.2 Loopnet Searches Table

The `ext_loopnet_searches` table stores saved search parameters for Loopnet.

```sql
CREATE TABLE `ext_loopnet_searches` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT 'Search name',
  `parameters` text NOT NULL COMMENT 'Search parameters as JSON',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT 'User who created the search',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ext_loopnet_searches_user_id_index` (`user_id`),
  CONSTRAINT `ext_loopnet_searches_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### Field Descriptions

- **id**: Primary key
- **name**: Human-readable name for the saved search
- **parameters**: Search parameters as JSON
- **user_id**: Foreign key to the users table

## 4. Entity Relationship Diagram

```
+---------------+       +--------------------+       +------------------+
|  extensions   |       | extension_settings |       | extension_hooks  |
+---------------+       +--------------------+       +------------------+
| id            |<----->| id                 |       | id               |
| name          |       | extension_code     |       | extension_code   |
| code          |       | key                |       | hook_name        |
| version       |       | value              |       | priority         |
| description   |       | created_at         |       | created_at       |
| author        |       | updated_at         |       | updated_at       |
| author_url    |       +--------------------+       +------------------+
| manifest      |                                             ^
| permissions   |                                             |
| settings      |                                             |
| is_enabled    |       +----------------------+              |
| is_core       |       | extension_dependencies|              |
| installed_at  |<----->| id                   |              |
| created_at    |       | extension_code       |              |
| updated_at    |       | dependency_code      |              |
+---------------+       | version_constraint   |              |
        ^               | created_at           |              |
        |               | updated_at           |              |
        |               +----------------------+              |
        |                                                    |
        |                                                    |
        |               +--------------------+               |
        |               | ext_loopnet_listings|               |
        +-------------->| id                 |---------------+
                        | loopnet_id         |
                        | title              |
                        | description        |
                        | property_type      |
                        | status             |
                        | price              |
                        | price_per_sqft     |
                        | building_size      |
                        | lot_size           |
                        | address            |
                        | city               |
                        | state              |
                        | zip                |
                        | latitude           |
                        | longitude          |
                        | features           |
                        | images             |
                        | documents          |
                        | contacts           |
                        | deal_id            |----------+
                        | offering_id        |-------+  |
                        | asset_id           |----+  |  |
                        | imported_at        |    |  |  |
                        | created_at         |    |  |  |
                        | updated_at         |    |  |  |
                        +--------------------+    |  |  |
                                                  |  |  |
                        +--------------------+    |  |  |
                        | ext_loopnet_searches|    |  |  |
                        +--------------------+    |  |  |
                        | id                 |    |  |  |
                        | name               |    |  |  |
                        | parameters         |    |  |  |
                        | user_id            |    |  |  |
                        | created_at         |    |  |  |
                        | updated_at         |    |  |  |
                        +--------------------+    |  |  |
                                                  |  |  |
                        +--------------------+    |  |  |
                        | assets             |<---+  |  |
                        +--------------------+       |  |
                                                    |  |
                        +--------------------+       |  |
                        | offerings          |<------+  |
                        +--------------------+          |
                                                       |
                        +--------------------+          |
                        | deals              |<---------+
                        +--------------------+
```

## 5. Migration Files

### 5.1 Create Extensions Table Migration

```php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateExtensionsTable extends Migration
{
    public function up()
    {
        Schema::create('extensions', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->string('version');
            $table->text('description')->nullable();
            $table->string('author')->nullable();
            $table->string('author_url')->nullable();
            $table->text('manifest');
            $table->text('permissions')->nullable();
            $table->text('settings')->nullable();
            $table->boolean('is_enabled')->default(false);
            $table->boolean('is_core')->default(false);
            $table->timestamp('installed_at')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('extensions');
    }
}
```

### 5.2 Create Extension Settings Table Migration

```php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateExtensionSettingsTable extends Migration
{
    public function up()
    {
        Schema::create('extension_settings', function (Blueprint $table) {
            $table->id();
            $table->string('extension_code');
            $table->string('key');
            $table->text('value')->nullable();
            $table->timestamps();
            
            $table->unique(['extension_code', 'key']);
            $table->foreign('extension_code')
                  ->references('code')
                  ->on('extensions')
                  ->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('extension_settings');
    }
}
```

### 5.3 Create Extension Hooks Table Migration

```php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateExtensionHooksTable extends Migration
{
    public function up()
    {
        Schema::create('extension_hooks', function (Blueprint $table) {
            $table->id();
            $table->string('extension_code');
            $table->string('hook_name');
            $table->integer('priority')->default(10);
            $table->timestamps();
            
            $table->index('hook_name');
            $table->index('extension_code');
            $table->foreign('extension_code')
                  ->references('code')
                  ->on('extensions')
                  ->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('extension_hooks');
    }
}
```

### 5.4 Create Extension Dependencies Table Migration

```php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateExtensionDependenciesTable extends Migration
{
    public function up()
    {
        Schema::create('extension_dependencies', function (Blueprint $table) {
            $table->id();
            $table->string('extension_code');
            $table->string('dependency_code');
            $table->string('version_constraint');
            $table->timestamps();
            
            $table->unique(['extension_code', 'dependency_code']);
            $table->index('dependency_code');
            $table->foreign('extension_code')
                  ->references('code')
                  ->on('extensions')
                  ->onDelete('cascade');
            $table->foreign('dependency_code')
                  ->references('code')
                  ->on('extensions')
                  ->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('extension_dependencies');
    }
}
```

### 5.5 Create Loopnet Listings Table Migration

```php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateExtLoopnetListingsTable extends Migration
{
    public function up()
    {
        Schema::create('ext_loopnet_listings', function (Blueprint $table) {
            $table->id();
            $table->string('loopnet_id')->unique();
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('property_type');
            $table->string('status');
            $table->decimal('price', 15, 2)->nullable();
            $table->decimal('price_per_sqft', 10, 2)->nullable();
            $table->integer('building_size')->nullable();
            $table->integer('lot_size')->nullable();
            $table->string('address');
            $table->string('city');
            $table->string('state');
            $table->string('zip');
            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();
            $table->text('features')->nullable();
            $table->text('images')->nullable();
            $table->text('documents')->nullable();
            $table->text('contacts')->nullable();
            $table->unsignedBigInteger('deal_id')->nullable();
            $table->unsignedBigInteger('offering_id')->nullable();
            $table->unsignedBigInteger('asset_id')->nullable();
            $table->timestamp('imported_at')->nullable();
            $table->timestamps();
            
            $table->index('deal_id');
            $table->index('offering_id');
            $table->index('asset_id');
            $table->foreign('deal_id')
                  ->references('id')
                  ->on('deals')
                  ->onDelete('set null');
            $table->foreign('offering_id')
                  ->references('id')
                  ->on('offerings')
                  ->onDelete('set null');
            $table->foreign('asset_id')
                  ->references('id')
                  ->on('assets')
                  ->onDelete('set null');
        });
    }

    public function down()
    {
        Schema::dropIfExists('ext_loopnet_listings');
    }
}
```

### 5.6 Create Loopnet Searches Table Migration

```php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateExtLoopnetSearchesTable extends Migration
{
    public function up()
    {
        Schema::create('ext_loopnet_searches', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('parameters');
            $table->unsignedBigInteger('user_id');
            $table->timestamps();
            
            $table->index('user_id');
            $table->foreign('user_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('ext_loopnet_searches');
    }
}
```

## 6. Data Flow

### 6.1 Extension Installation Flow

1. Admin uploads extension package or installs from repository
2. System extracts package and validates manifest
3. System registers extension in `extensions` table
4. System runs extension migrations
5. System publishes extension assets
6. Extension is ready to be enabled

### 6.2 Extension Activation Flow

1. Admin enables extension in admin panel
2. System updates `is_enabled` flag in `extensions` table
3. System loads extension service provider
4. Extension registers hooks in `extension_hooks` table
5. Extension is now active and integrated with the platform

### 6.3 Hook Execution Flow

1. Platform code reaches a hook point
2. System queries `extension_hooks` table for registered hooks
3. System loads and executes hook handlers in priority order
4. Hook handlers can modify data or perform actions
5. Platform code continues execution with potentially modified data

### 6.4 Loopnet Import Flow

1. Admin searches for properties on Loopnet
2. Admin selects a property to import
3. System creates record in `ext_loopnet_listings` table
4. Admin configures import options
5. System creates deal, offering, and asset records
6. System updates `ext_loopnet_listings` with references to created records
7. System downloads and attaches property images to asset
