# Partner System Documentation

This directory contains all documentation related to the Partner system in the CREI Investment Platform.

## Available Documentation

1. [Partner Dashboard](./partner_dashboard.md) - Implementation of the Partner Dashboard feature
2. [Partner Management](./partner-management.md) - Overview of partner management functionality
3. [Partner Status Management](./partner_status_management.md) - Guide for managing partner statuses
4. [Partner System Implementation](./partner_system_implementation.md) - Detailed implementation guide for the partner system
5. [Partner Routes Changes Log](./partner_routes_changes_log.md) - Documentation of route naming fixes for the partner system

## Partner Dashboard

The Partner Dashboard allows partners to access and manage their offerings, assets, users, investments, and distributions in a secure and controlled manner. It follows Laravel's MVC architecture and is designed to mirror the Admin Dashboard UI for consistency while ensuring proper data isolation and security.

## Key Components

1. **Controllers**: Located in `app/Http/Controllers/Partner/`
   - `OfferingController` - Manages partner offerings
   - `AssetController` - Manages partner assets
   - `UserController` - Manages users who invested in partner deals
   - `InvestmentController` - Manages partner investments
   - `DistributionController` - Manages partner distributions

2. **Service Layer**: Located in `app/Services/`
   - `PartnerDealService` - Core service that handles partner-deal associations and access control

3. **Views**: Located in `resources/views/partner/`
   - Organized by feature (offerings, assets, users, investments, distributions)

## Access Control

All partner functionality is protected by middleware `['auth:admin', 'role:partner']` to ensure only authenticated partners can access these routes. Data access is controlled through the `PartnerDealService` which ensures partners can only view and manage data related to their own deals.
