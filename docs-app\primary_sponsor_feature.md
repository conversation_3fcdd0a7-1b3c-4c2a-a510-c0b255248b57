# Primary Sponsor Feature Implementation

## Overview
The Primary Sponsor feature allows assignment and management of primary sponsors for investment offerings, separate from the Deal's Lead Sponsor.

## Business Context
- Deal's Lead Sponsor represents the entity responsible for the overall real estate project
- Offering's Primary Sponsor represents who is primarily responsible for that specific investment offering
- Multiple offerings of the same deal may have different primary sponsors based on:
  - Regulatory requirements
  - Marketing strategy
  - Investor relationships
  - Geographic focus

## Technical Implementation

### Database Changes
```sql
-- Add to offerings table
ALTER TABLE offerings ADD COLUMN primary_sponsor VARCHAR(255) DEFAULT 'Sitename';
```

### Model Updates
```php
// In Offering.php model
protected $attributes = [
    'primary_sponsor' => 'Sitename'
];

protected $fillable = [
    // ... existing fillable fields ...
    'primary_sponsor',
];
```

### UI Implementation
1. **Display Location**
   - Sidebar section of offering details page
   - Grouped with other key offering information
   - Consistent styling with existing UI patterns

2. **Management Interface**
   - Located in offering management form
   - Dropdown selection with:
     - Default "Sitename" option
     - List of authorized administrators
   - Clear helper text explaining the purpose

```php
<div class="side-bar col-md-4">
    <div class="card">
        <div class="card-body">
            <h6 class="mb-0">Primary Sponsor</h6>
            <p style="font-size: larger;" class="fw-bold mb-3">
                {{ $offering->primary_sponsor }}
            </p>
        </div>
    </div>
</div>
```

### Controller Logic
```php
public function storeManageOffering(Request $request, Offering $offering)
{
    $validate = Validator::make($request->all(), [
        'primary_sponsor' => 'required|string',
        // ... other validations
    ]);

    // Update offering with primary sponsor
    $offering->update([
        'primary_sponsor' => $request->primary_sponsor
    ]);
}
```

## Future Considerations

### Authorization
- Implement specific permissions for changing primary sponsor
- Consider role-based access control
- Add audit logging for sponsor changes

### UI Enhancements
- Add primary sponsor filter in offering lists
- Include primary sponsor in offering exports
- Display primary sponsor change history

### Business Rules
- Consider restrictions on when primary sponsor can be changed
- Implement approval workflow for sponsor changes
- Add notification system for sponsor changes

## Related Components
- Deal Management System
- Offering Management Interface
- User Authorization System
- Admin Management Interface

## Testing Requirements
1. Default sponsor assignment
2. Sponsor change functionality
3. Display in various contexts
4. Permission validation
5. Data integrity checks

## Documentation Updates Needed
- [ ] Update user guide with primary sponsor feature
- [ ] Add primary sponsor section to admin documentation
- [ ] Include primary sponsor in API documentation
