{"__meta": {"id": "01K19VX4TPDV6RSZGCYQB8052C", "datetime": "2025-07-28 21:14:15", "utime": **********.25613, "method": "GET", "uri": "/admin/", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": *********4.701963, "end": **********.256159, "duration": 0.5541961193084717, "duration_str": "554ms", "measures": [{"label": "Booting", "start": *********4.701963, "relative_start": 0, "end": **********.119393, "relative_end": **********.119393, "duration": 0.*****************, "duration_str": "417ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.119411, "relative_start": 0.****************, "end": **********.256163, "relative_end": 3.814697265625e-06, "duration": 0.*****************, "duration_str": "137ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.147969, "relative_start": 0.*****************, "end": **********.153277, "relative_end": **********.153277, "duration": 0.005307912826538086, "duration_str": "5.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.181469, "relative_start": 0.****************, "end": **********.251532, "relative_end": **********.251532, "duration": 0.*****************, "duration_str": "70.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin.auth.login", "start": **********.185198, "relative_start": 0.*****************, "end": **********.185198, "relative_end": **********.185198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.<PERSON><PERSON>a", "start": **********.195443, "relative_start": 0.*****************, "end": **********.195443, "relative_end": **********.195443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.layouts.master", "start": **********.241881, "relative_start": 0.5399179458618164, "end": **********.241881, "relative_end": **********.241881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.notify", "start": **********.246793, "relative_start": 0.5448300838470459, "end": **********.246793, "relative_end": **********.246793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 29461720, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.7", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/New_York", "Locale": "en"}}, "views": {"count": 4, "nb_templates": 4, "templates": [{"name": "admin.auth.login", "param_count": null, "params": [], "start": **********.185099, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/admin/auth/login.blade.phpadmin.auth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fadmin%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}}, {"name": "partials.cap<PERSON>a", "param_count": null, "params": [], "start": **********.195335, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/partials/captcha.blade.phppartials.captcha", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fpartials%2Fcaptcha.blade.php&line=1", "ajax": false, "filename": "captcha.blade.php", "line": "?"}}, {"name": "admin.layouts.master", "param_count": null, "params": [], "start": **********.241752, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/master.blade.phpadmin.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}}, {"name": "partials.notify", "param_count": null, "params": [], "start": **********.246651, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/partials/notify.blade.phppartials.notify", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fpartials%2Fnotify.blade.php&line=1", "ajax": false, "filename": "notify.blade.php", "line": "?"}}]}, "queries": {"count": 3, "nb_statements": 2, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00631, "accumulated_duration_str": "6.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 39}, {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.210761, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "Captcha.php:39", "source": {"index": 15, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FLib%2FCaptcha.php&line=39", "ajax": false, "filename": "Captcha.php", "line": "39"}, "connection": "main", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `extensions` where `act` = 'custom-captcha' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["custom-captcha", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.2249272, "duration": 0.005019999999999999, "duration_str": "5.02ms", "memory": 0, "memory_str": null, "filename": "Captcha.php:39", "source": {"index": 16, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FLib%2FCaptcha.php&line=39", "ajax": false, "filename": "Captcha.php", "line": "39"}, "connection": "main", "explain": null, "start_percent": 0, "width_percent": 79.556}, {"sql": "select * from `extensions` where `act` = 'google-recaptcha2' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["google-recaptcha2", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 27}, {"index": 17, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 76}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.235478, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "Captcha.php:27", "source": {"index": 16, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FLib%2FCaptcha.php&line=27", "ajax": false, "filename": "Captcha.php", "line": "27"}, "connection": "main", "explain": null, "start_percent": 79.556, "width_percent": 20.444}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin", "action_name": "admin.login", "controller_action": "App\\Http\\Controllers\\Admin\\Auth\\LoginController@showLoginForm", "uri": "GET admin", "controller": "App\\Http\\Controllers\\Admin\\Auth\\LoginController@showLoginForm<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FAdmin%2FAuth%2FLoginController.php&line=35\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin\\Auth", "prefix": "/admin", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FAdmin%2FAuth%2FLoginController.php&line=35\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/Auth/LoginController.php:35-39</a>", "middleware": "web, admin.guest", "duration": "555ms", "peak_memory": "30MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1436278837 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1436278837\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-379279293 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-379279293\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-879111239 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://127.0.0.1:8000/admin/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"130 characters\">cre_session=Hh6TGHqPVCzTYE14FsgPTMaR8dDM9c6s6FwIM1bh; gdpr_cookie=CRE%20CLOUD; XSRF-TOKEN=nPgUeYuap1VpAV456Xq88p6o8wi3IsgjhLus7AJH</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-879111239\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1469790487 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cre_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Hh6TGHqPVCzTYE14FsgPTMaR8dDM9c6s6FwIM1bh</span>\"\n  \"<span class=sf-dump-key>gdpr_cookie</span>\" => \"<span class=sf-dump-str title=\"9 characters\">CRE CLOUD</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nPgUeYuap1VpAV456Xq88p6o8wi3IsgjhLus7AJH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1469790487\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1216670022 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 01:14:15 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1216670022\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1666984980 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nPgUeYuap1VpAV456Xq88p6o8wi3IsgjhLus7AJH</span>\"\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K19VX47FX0G8QRG7TS3MGR51</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">_old_input</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">errors</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_old_input</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JNbaS9iRaVJug6d74VFNQFIGOXxO31IgUBbm6V88</span>\"\n    \"<span class=sf-dump-key>username</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref>#1319</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">bags</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>default</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\MessageBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">MessageBag</span></span> {<a class=sf-dump-ref>#1320</a><samp data-depth=4 class=sf-dump-compact>\n        #<span class=sf-dump-protected title=\"Protected property\">messages</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>username</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">These credentials do not match our records.</span>\"\n          </samp>]\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">format</span>: \"<span class=sf-dump-str title=\"8 characters\">:message</span>\"\n      </samp>}\n    </samp>]\n  </samp>}\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1666984980\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin", "action_name": "admin.login", "controller_action": "App\\Http\\Controllers\\Admin\\Auth\\LoginController@showLoginForm"}, "badge": null}}