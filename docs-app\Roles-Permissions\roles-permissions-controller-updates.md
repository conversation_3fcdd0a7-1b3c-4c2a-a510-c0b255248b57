# Controller Updates for Organizational Role Management

## Overview

The `StaffController` needs to be updated to support the organizational hierarchy by adding protection for executive roles, improving the role management UI, and adding department information for better context.

## Implementation Steps

### 1. Update the Roles Method

Enhance the `roles` method to provide organizational context:

```php
public function roles()
{
    $pageTitle = 'Staff Roles';
    $roles = Role::whereNotIn('name', ['user'])->get();
    
    // Get and group permissions by category
    $permissions = Permission::all()->groupBy(function($permission) {
        // Handle permissions without dots gracefully
        $parts = explode('.', $permission->name);
        return count($parts) > 1 ? $parts[0] : 'general';
    });
    
    // Get organizational structure for role hierarchy display
    $organizationalHierarchy = [
        'Executive' => ['Super Admin', 'CEO', 'CFO', 'COO', 'CTO', 'CLO'],
        'Department Managers' => [
            'Accounting Manager', 'Deals Manager', 'Asset Manager', 'Partnerships Manager',
            'Investor Relations Manager', 'Marketing Manager', 'General Management Manager',
            'Technology Manager', 'Compliance & Legal Manager'
        ],
        'Staff' => ['Staff', 'Manager']
    ];
    
    return view('admin.staff.roles', compact('pageTitle', 'roles', 'permissions', 'organizationalHierarchy'));
}
```

### 2. Enhance the EditRole Method

Update the `editRole` method to protect executive roles and provide better department context:

```php
public function editRole(Role $role)
{
    if ($role->name === 'Super Admin') {
        $notify[] = ['error', 'Super Admin role cannot be edited'];
        return back()->withNotify($notify);
    }
    
    // Protect C-Suite roles from being edited by non-Super Admin users
    $protectedRoles = ['CEO', 'CFO', 'COO', 'CTO', 'CLO'];
    if (in_array($role->name, $protectedRoles) && !auth()->guard('admin')->user()->hasRole('Super Admin')) {
        $notify[] = ['error', 'Executive roles can only be edited by Super Admin'];
        return back()->withNotify($notify);
    }

    $pageTitle = 'Edit Role';
    $permissions = Permission::all()->groupBy(function($permission) {
        $parts = explode('.', $permission->name);
        return count($parts) > 1 ? $parts[0] : 'general';
    });
    
    // Get department information for better UI organization
    $departments = [
        'executive' => 'Executive Management',
        'accounting' => 'Accounting Department',
        'deals' => 'Deals Department',
        'assets' => 'Asset Management Department',
        'partnerships' => 'Partnerships Department',
        'investor_relations' => 'Investor Relations Department',
        'marketing' => 'Marketing Department',
        'general_management' => 'General Management Department',
        'technology' => 'Technology Department',
        'compliance_legal' => 'Compliance & Legal Department',
        'staff' => 'Staff Management',
        'roles' => 'Role Management',
        'users' => 'User Management',
        'settings' => 'Settings Management',
        'system' => 'System Management'
    ];
    
    return view('admin.staff.edit_role', compact('pageTitle', 'role', 'permissions', 'departments'));
}
```

### 3. Enhance the DestroyRole Method

Update the `destroyRole` method to protect organizational roles:

```php
public function destroyRole(Role $role)
{
    if ($role->name === 'Super Admin') {
        $notify[] = ['error', 'Super Admin role cannot be deleted'];
        return back()->withNotify($notify);
    }
    
    // Protect C-Suite and department manager roles from being deleted by non-Super Admin users
    $protectedRoles = ['CEO', 'CFO', 'COO', 'CTO', 'CLO', 
                      'Accounting Manager', 'Deals Manager', 'Asset Manager', 
                      'Partnerships Manager', 'Investor Relations Manager', 
                      'Marketing Manager', 'General Management Manager',
                      'Technology Manager', 'Compliance & Legal Manager'];
                      
    if (in_array($role->name, $protectedRoles)) {
        if (!auth()->guard('admin')->user()->hasRole('Super Admin')) {
            $notify[] = ['error', 'Organizational roles can only be deleted by Super Admin'];
            return back()->withNotify($notify);
        }
        
        // Even for Super Admin, warn about deleting organizational roles
        $notify[] = ['warning', 'Deleting organizational roles may disrupt system functionality. Proceed with caution.'];
        return back()->withNotify($notify);
    }

    // Check if any admin has this role using the model_has_roles table
    if ($role->users()->where('model_type', Admin::class)->count() > 0) {
        $notify[] = ['error', 'Role is assigned to staff members and cannot be deleted'];
        return back()->withNotify($notify);
    }

    // Continue with role deletion...
}
```

### 4. Update the Create Method

Enhance the `create` method to provide organizational context:

```php
public function create()
{
    $pageTitle = 'Add New Staff Member';
    $roles = Role::where('guard_name', 'admin')->whereNotIn('name', ['user'])->get();
    
    // Group roles by organizational hierarchy for better UI
    $roleGroups = [
        'Executive' => ['Super Admin', 'CEO', 'CFO', 'COO', 'CTO', 'CLO'],
        'Department Managers' => [
            'Accounting Manager', 'Deals Manager', 'Asset Manager', 'Partnerships Manager',
            'Investor Relations Manager', 'Marketing Manager', 'General Management Manager',
            'Technology Manager', 'Compliance & Legal Manager'
        ],
        'Staff' => ['Staff', 'Manager']
    ];
    
    return view('admin.staff.create', compact('pageTitle', 'roles', 'roleGroups'));
}
```

### 5. Backward Compatibility Considerations

These controller updates maintain backward compatibility by:

1. Not changing any route definitions
2. Preserving existing method signatures and return types
3. Adding new variables to views without removing existing ones
4. Ensuring all existing functionality continues to work
