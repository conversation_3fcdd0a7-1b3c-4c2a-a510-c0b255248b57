<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('distributions', function (Blueprint $table) {
            // Drop foreign key first
            if (Schema::hasColumn('distributions', 'investment_id')) {
                $table->dropForeign(['investment_id']);
                $table->dropColumn('investment_id');
            }

            // Add included_classes column
            if (!Schema::hasColumn('distributions', 'included_classes')) {
                $table->json('included_classes')->nullable()->after('deal_id');
            }

            // Drop distribution_waterfall_id if exists
            if (Schema::hasColumn('distributions', 'distribution_waterfall_id')) {
                $table->dropColumn('distribution_waterfall_id');
            }
        });
    }

    public function down(): void
    {
        Schema::table('distributions', function (Blueprint $table) {
            $table->unsignedBigInteger('investment_id')->nullable();
            $table->dropColumn('included_classes');
        });
    }
};
