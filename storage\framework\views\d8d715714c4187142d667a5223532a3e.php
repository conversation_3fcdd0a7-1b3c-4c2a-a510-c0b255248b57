<?php
    $blogContent = getContent('blog.content', true);
    $blogElements = getContent('blog.element', limit: 3, orderById: true);
?>
<section class="latest-blogs py-120 bg-pattern">
    <div class="container ">
        <div class="section-heading style-left">
            <p class="section-heading__subtitle"><?php echo e(__(@$blogContent->data_values->title)); ?></p>
            <div class="section-heading__wrapper">
                <h2 class="section-heading__title"><?php echo e(__(@$blogContent->data_values->heading)); ?></h2>
                <a class="section-heading__link" href="<?php echo e(route('blog')); ?>">
                    <span><?php echo app('translator')->get('Explore'); ?></span>
                    <i class="las la-long-arrow-alt-right"></i>
                </a>
            </div>
        </div>
        <div class="row  gy-3 justify-content-center">
            <?php $__currentLoopData = $blogElements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-sm-6 col-lg-4">
                    <article class="card blog--card">
                        <a class="card-img" href="<?php echo e(route('blog.details', @$blog->slug)); ?>">
                            <img src="<?php echo e(frontendImage('blog','thumb_'.@$blog->data_values->image, '420x250')); ?>"
                                alt="<?php echo app('translator')->get('Blog Image'); ?>">
                        </a>
                        <div class="card-body">
                            <div class="card-date text-center">
                                <h4 class="day"><?php echo e(showDateTime(@$blog->created_at, 'd')); ?></h4>
                                <span class="month"><?php echo e(showDateTime(@$blog->created_at, 'M')); ?> </span>
                            </div>
                            <h4 class="card-title mt-3 mb-3">
                                <a href="<?php echo e(route('blog.details', @$blog->slug)); ?>">
                                    <?php echo e(__(@$blog->data_values->title)); ?>

                                </a>
                            </h4>
                            <p class="mb-3">
                                <?php echo e(strLimit(strip_tags(@$blog->data_values->description), 150)); ?>

                            </p>
                            <a href="<?php echo e(route('blog.details', @$blog->slug)); ?>"><?php echo app('translator')->get('Read More'); ?></a>
                        </div>
                    </article>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php /**PATH C:\laragon\www\PLATFORM\resources\views/templates/basic/sections/blog.blade.php ENDPATH**/ ?>