@extends('admin.layouts.app')

@section('panel')
    <div class="card card-body" x-data="TemplateDetail()">
        <div class="main-box">
            <a class="item-detail" href="{{ route('admin.emails.templates.add') }}">
                <svg>
                    <text x="50%" y="50%" text-anchor="middle" dominant-baseline="middle" font-size="40" fill="#3F4258">+</text>
                </svg>
                <div>
                    <p class="text-center">Blank</p>
                    <p>Drag & drop</p>
                </div>
            </a>
            <a class="item-detail">
                <svg>
                    <text x="50%" y="50%" text-anchor="middle" dominant-baseline="middle" font-size="40"
                        fill="#3F4258">+</text>
                </svg>
                <div>
                    <p class="text-center">Blank</p>
                    <p>simple</p>
                </div>
            </a>

        </div>
        <hr>
        <div class="main-box">
            @foreach ($templates as $template)
                <a class="item-detail" href="">
                    <svg>
                        <rect width="30" height="30" fill="#8AACDF" />
                    </svg>
                    <div>
                        <p class="text-center">{{ $template->name }}</p>
                        <p>{{ Str::limit($template->description, 30) }}</p>
                    </div>
                </a>
            @endforeach
        </div>

    </div>
    <style>
        .btn-primary-header {
            padding: 10px 50px;
            border-radius: 15px;
            background-color: blue;
            color: white;
        }

        .btn-primary-header:hover {
            background-color: darkblue;
        }

        .item-detail {
            display: flex;
            flex-direction: column;
            -webkit-box-pack: center;
            justify-content: center;
            -webkit-box-align: center;
            align-items: center;
            gap: 12px;
            height: 260px;
            background: rgb(255, 255, 255);
            border: 1px solid rgb(233, 233, 233);
            border-radius: 8px;
            transition: 150ms ease-in-out;
            width: 420px;
        }

        .item-detail:hover {
            border-color: #8AACDF;
            box-shadow: 0px 3px 10px var(--color-border);
            cursor: pointer;
        }

        .item-detail svg {
            height: 30px;

        }

        .item-detail div p {
            margin: 0px;
            font-size: 14px;
            font-weight: 700;
        }

        .main-box {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
    </style>
@endsection
@section('scripts')
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script>
        function TemplateDetail() {
            return {

            }
        }
    </script>
    <script src="{{ asset('js/admin/emails.js') }}"></script>
@endsection
