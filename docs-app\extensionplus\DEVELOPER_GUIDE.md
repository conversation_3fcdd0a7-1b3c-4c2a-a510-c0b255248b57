# ExtensionPlus Development Guide

## 1. Introduction

This guide provides step-by-step instructions for developing extensions for the CRE Investment Platform using the ExtensionPlus system. Extensions allow developers to add new features, integrations, and functionality to the platform without modifying the core codebase.

## 2. Getting Started

### 2.1 Prerequisites

Before you begin developing an extension, ensure you have:

- A local development environment with the CRE Investment Platform installed
- Familiarity with Laravel, PHP, and Vue.js
- Understanding of the platform's core concepts (deals, offerings, assets, etc.)
- Access to the ExtensionPlus documentation

### 2.2 Extension Structure

The ExtensionPlus system uses a standardized directory structure for extensions:

```
extensions/
  └── YourExtension/
      ├── Http/
      │   └── Controllers/
      ├── Models/
      ├── resources/
      │   ├── views/
      │   └── assets/
      ├── routes/
      │   └── web.php
      ├── migrations/
      ├── YourExtensionServiceProvider.php
      ├── manifest.json
      ├── install.php (optional)
      └── uninstall.php (optional)
```

## 3. Extension Components

### 3.1 Manifest File

Every extension must include a `manifest.json` file in its root directory. This file defines the extension's metadata and requirements:

```json
{
    "name": "Your Extension Name",
    "code": "YourExtension",
    "version": "1.0.0",
    "description": "Description of your extension",
    "author": "Your Name",
    "author_url": "https://example.com",
    "provider": "Extensions\\YourExtension\\YourExtensionServiceProvider",
    "permissions": {
        "admin": ["view", "manage"]
    },
    "is_core": false
}
```

### 3.2 Service Provider

The service provider is the entry point for your extension. It handles registration of routes, views, assets, and other components:

```php
<?php

namespace Extensions\YourExtension;

use App\Facades\ExtensionPlus;
use App\Facades\Hook;
use Illuminate\Support\ServiceProvider;

class YourExtensionServiceProvider extends ServiceProvider
{
    public function register()
    {
        // Register bindings and services
    }

    public function boot()
    {
        // Register routes
        $this->loadRoutesFrom(__DIR__ . '/routes/web.php');
        
        // Register views
        $this->loadViewsFrom(__DIR__ . '/resources/views', 'your-extension');
        
        // Register assets
        $this->publishes([
            __DIR__ . '/resources/assets' => public_path('assets/extensions/your-extension'),
        ], 'your-extension-assets');
        
        // Register hooks
        Hook::register('admin.sidebar.after', function () {
            return view('your-extension::hooks.sidebar');
        });
    }
}
```

### 3.3 Installation and Uninstallation Scripts

You can include optional `install.php` and `uninstall.php` scripts to perform custom actions during installation and uninstallation:

```php
<?php
// install.php

function extension_install($extension)
{
    // Perform installation tasks
    // $extension is the Extension model instance
}
```

```php
<?php
// uninstall.php

function extension_uninstall($extension)
{
    // Perform uninstallation tasks
    // $extension is the Extension model instance
}
```

## 4. Extension Features

### 4.1 Hooks

Hooks allow your extension to integrate with specific points in the platform's execution flow:

```php
// Register a hook
Hook::register('admin.sidebar.after', function () {
    return view('your-extension::hooks.sidebar');
});

// Execute a hook
$content = Hook::execute('admin.sidebar.after');
```

### 4.2 Shortcodes

Shortcodes provide a way to insert dynamic content into static text:

```php
// Register a shortcode
ExtensionPlus::registerShortcode('your_shortcode', function ($attributes) {
    $name = $attributes['name'] ?? 'Default';
    return "Hello, {$name}!";
});

// Use the shortcode in content
// [your_shortcode name="John"]
```

### 4.3 Routes

Define your extension's routes in the `routes/web.php` file:

```php
<?php

use Illuminate\Support\Facades\Route;
use Extensions\YourExtension\Http\Controllers\YourController;

Route::group(['middleware' => ['web', 'auth', 'admin'], 'prefix' => 'admin/extensions/your-extension'], function () {
    Route::get('/', [YourController::class, 'index'])->name('admin.extensions.your-extension.index');
});
```

### 4.4 Views

Store your extension's views in the `resources/views` directory and access them using the namespace you defined in your service provider:

```php
// In your controller
return view('your-extension::index', ['data' => $data]);
```

## 5. Extension Installation

Extensions are installed through the admin interface. The process involves:

1. Uploading the extension package or placing it in the `extensions` directory
2. Discovering the extension through the admin interface
3. Installing the extension, which:
   - Creates a database record
   - Runs any migrations
   - Executes the installation script
   - Publishes assets

## 6. Best Practices

1. **Isolation**: Keep your extension code isolated from the core platform
2. **Namespacing**: Use proper namespacing to avoid conflicts
3. **Hooks**: Use hooks to integrate with the platform rather than modifying core files
4. **Database**: Prefix your database tables with your extension code
5. **Assets**: Organize assets in a dedicated directory
6. **Documentation**: Provide clear documentation for your extension

## 7. Troubleshooting

### Common Issues

1. **Extension Not Found**: Ensure your manifest.json file is correctly formatted
2. **Service Provider Not Loading**: Check that the provider class is correctly specified in the manifest
3. **Database Errors**: Verify that your migrations are compatible with the platform's database schema
4. **Asset Loading Issues**: Make sure assets are published correctly

## 8. Example Extension

For a complete example of an extension, refer to the sample extensions in the documentation repository.
