{"__meta": {"id": "01K1A2RNVP4D8VJJ3B7TFJZB56", "datetime": "2025-07-28 23:14:08", "utime": **********.888274, "method": "GET", "uri": "/admin/deals", "ip": "127.0.0.1"}, "messages": {"count": 18, "messages": [{"message": "[23:14:08] LOG.warning: Optional parameter $options declared before required parameter $entity is implicitly treated as a required parameter in C:\\laragon\\www\\PLATFORM\\app\\Services\\StripeACHService.php on line 314", "message_html": null, "is_string": false, "label": "warning", "time": **********.563086, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:08] LOG.info: Permission check for: deals.view", "message_html": null, "is_string": false, "label": "info", "time": **********.67627, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:08] LOG.info: User: Super admin (ID: 5)", "message_html": null, "is_string": false, "label": "info", "time": **********.676489, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:08] LOG.info: User roles: Super Admin", "message_html": null, "is_string": false, "label": "info", "time": **********.689995, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:08] LOG.info: User has Super Admin role - bypassing permission check for: deals.view", "message_html": null, "is_string": false, "label": "info", "time": **********.690533, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:08] LOG.info: Deal ID: 2 {\n    \"has_achsettings\": true,\n    \"entity_name\": \"test\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.719049, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:08] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\laragon\\www\\PLATFORM\\storage\\framework\\views\\3c43f0df1ed1c7d0f88edb6fce55e749.php on line 322", "message_html": null, "is_string": false, "label": "warning", "time": **********.742269, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:08] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\laragon\\www\\PLATFORM\\storage\\framework\\views\\3c43f0df1ed1c7d0f88edb6fce55e749.php on line 324", "message_html": null, "is_string": false, "label": "warning", "time": **********.742585, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:08] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\laragon\\www\\PLATFORM\\storage\\framework\\views\\3c43f0df1ed1c7d0f88edb6fce55e749.php on line 327", "message_html": null, "is_string": false, "label": "warning", "time": **********.742817, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:08] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\laragon\\www\\PLATFORM\\storage\\framework\\views\\3c43f0df1ed1c7d0f88edb6fce55e749.php on line 472", "message_html": null, "is_string": false, "label": "warning", "time": **********.745209, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:08] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\laragon\\www\\PLATFORM\\storage\\framework\\views\\3c43f0df1ed1c7d0f88edb6fce55e749.php on line 474", "message_html": null, "is_string": false, "label": "warning", "time": **********.745509, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:08] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\laragon\\www\\PLATFORM\\storage\\framework\\views\\3c43f0df1ed1c7d0f88edb6fce55e749.php on line 482", "message_html": null, "is_string": false, "label": "warning", "time": **********.745718, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:08] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\PLATFORM\\app\\Casts\\JsonCast.php on line 20", "message_html": null, "is_string": false, "label": "warning", "time": **********.7559, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:08] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\PLATFORM\\app\\Casts\\JsonCast.php on line 20", "message_html": null, "is_string": false, "label": "warning", "time": **********.757146, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:08] LOG.warning: strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\\laragon\\www\\PLATFORM\\storage\\framework\\views\\0a55897e01daddb40d816b22b2c6c8e8.php on line 10", "message_html": null, "is_string": false, "label": "warning", "time": **********.861533, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:08] LOG.warning: strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\\laragon\\www\\PLATFORM\\storage\\framework\\views\\0a55897e01daddb40d816b22b2c6c8e8.php on line 10", "message_html": null, "is_string": false, "label": "warning", "time": **********.861721, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:08] LOG.warning: strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\\laragon\\www\\PLATFORM\\storage\\framework\\views\\0a55897e01daddb40d816b22b2c6c8e8.php on line 10", "message_html": null, "is_string": false, "label": "warning", "time": **********.867839, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:08] LOG.warning: strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\\laragon\\www\\PLATFORM\\storage\\framework\\views\\0a55897e01daddb40d816b22b2c6c8e8.php on line 10", "message_html": null, "is_string": false, "label": "warning", "time": **********.868191, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753758847.947376, "end": **********.888321, "duration": 0.9409449100494385, "duration_str": "941ms", "measures": [{"label": "Booting", "start": 1753758847.947376, "relative_start": 0, "end": **********.492352, "relative_end": **********.492352, "duration": 0.***************, "duration_str": "545ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.492414, "relative_start": 0.****************, "end": **********.888324, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "396ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.540188, "relative_start": 0.****************, "end": **********.546957, "relative_end": **********.546957, "duration": 0.006768941879272461, "duration_str": "6.77ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.728873, "relative_start": 0.****************, "end": **********.883843, "relative_end": **********.883843, "duration": 0.****************, "duration_str": "155ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin.deals.index", "start": **********.733509, "relative_start": 0.****************, "end": **********.733509, "relative_end": **********.733509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.layouts.app", "start": **********.761699, "relative_start": 0.****************, "end": **********.761699, "relative_end": **********.761699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.partials.sidenav", "start": **********.813882, "relative_start": 0.8665060997009277, "end": **********.813882, "relative_end": **********.813882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.partials.topnav", "start": **********.859083, "relative_start": 0.9117069244384766, "end": **********.859083, "relative_end": **********.859083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.partials.breadcrumb", "start": **********.873828, "relative_start": 0.9264519214630127, "end": **********.873828, "relative_end": **********.873828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.layouts.master", "start": **********.874968, "relative_start": 0.9275920391082764, "end": **********.874968, "relative_end": **********.874968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.notify", "start": **********.878278, "relative_start": 0.9309020042419434, "end": **********.878278, "relative_end": **********.878278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 32774512, "peak_usage_str": "31MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.7", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/New_York", "Locale": "en"}}, "views": {"count": 7, "nb_templates": 7, "templates": [{"name": "admin.deals.index", "param_count": null, "params": [], "start": **********.733431, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/admin/deals/index.blade.phpadmin.deals.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fadmin%2Fdeals%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin.layouts.app", "param_count": null, "params": [], "start": **********.761615, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.phpadmin.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "admin.partials.sidenav", "param_count": null, "params": [], "start": **********.813767, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/admin/partials/sidenav.blade.phpadmin.partials.sidenav", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fadmin%2Fpartials%2Fsidenav.blade.php&line=1", "ajax": false, "filename": "sidenav.blade.php", "line": "?"}}, {"name": "admin.partials.topnav", "param_count": null, "params": [], "start": **********.858962, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/admin/partials/topnav.blade.phpadmin.partials.topnav", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fadmin%2Fpartials%2Ftopnav.blade.php&line=1", "ajax": false, "filename": "topnav.blade.php", "line": "?"}}, {"name": "admin.partials.breadcrumb", "param_count": null, "params": [], "start": **********.873707, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/admin/partials/breadcrumb.blade.phpadmin.partials.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fadmin%2Fpartials%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}}, {"name": "admin.layouts.master", "param_count": null, "params": [], "start": **********.874858, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/master.blade.phpadmin.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}}, {"name": "partials.notify", "param_count": null, "params": [], "start": **********.878218, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/partials/notify.blade.phppartials.notify", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fpartials%2Fnotify.blade.php&line=1", "ajax": false, "filename": "notify.blade.php", "line": "?"}}]}, "queries": {"count": 21, "nb_statements": 20, "nb_visible_statements": 21, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03359, "accumulated_duration_str": "33.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/StripeACHService.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Services\\StripeACHService.php", "line": 33}, {"index": 16, "namespace": null, "name": "app/Services/StripeACHService.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Services\\StripeACHService.php", "line": 24}, {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}], "start": **********.585204, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "StripeACHService.php:33", "source": {"index": 15, "namespace": null, "name": "app/Services/StripeACHService.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Services\\StripeACHService.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FServices%2FStripeACHService.php&line=33", "ajax": false, "filename": "StripeACHService.php", "line": "33"}, "connection": "main", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `gateways` where `code` = '114' limit 1", "type": "query", "params": [], "bindings": ["114"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/StripeACHService.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Services\\StripeACHService.php", "line": 33}, {"index": 17, "namespace": null, "name": "app/Services/StripeACHService.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Services\\StripeACHService.php", "line": 24}, {"index": 18, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 28}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}], "start": **********.609881, "duration": 0.00726, "duration_str": "7.26ms", "memory": 0, "memory_str": null, "filename": "StripeACHService.php:33", "source": {"index": 16, "namespace": null, "name": "app/Services/StripeACHService.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Services\\StripeACHService.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FServices%2FStripeACHService.php&line=33", "ajax": false, "filename": "StripeACHService.php", "line": "33"}, "connection": "main", "explain": null, "start_percent": 0, "width_percent": 21.614}, {"sql": "select * from `admins` where `id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Middleware\\RedirectIfNotAdmin.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.6691298, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "main", "explain": null, "start_percent": 21.614, "width_percent": 3.394}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (5) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": ["App\\Models\\Admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 395}, {"index": 23, "namespace": "middleware", "name": "permission", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Middleware\\PermissionMiddleware.php", "line": 34}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 25, "namespace": "middleware", "name": "admin", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Middleware\\RedirectIfNotAdmin.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.683674, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:395", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 395}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=395", "ajax": false, "filename": "HasRoles.php", "line": "395"}, "connection": "main", "explain": null, "start_percent": 25.007, "width_percent": 5.776}, {"sql": "select count(*) as aggregate from `deals`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DealController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\Admin\\DealController.php", "line": 76}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.692212, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "DealController.php:76", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DealController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\Admin\\DealController.php", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FAdmin%2FDealController.php&line=76", "ajax": false, "filename": "DealController.php", "line": "76"}, "connection": "main", "explain": null, "start_percent": 30.783, "width_percent": 3.96}, {"sql": "select * from `deals` order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DealController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\Admin\\DealController.php", "line": 76}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.6966212, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "DealController.php:76", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DealController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\Admin\\DealController.php", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FAdmin%2FDealController.php&line=76", "ajax": false, "filename": "DealController.php", "line": "76"}, "connection": "main", "explain": null, "start_percent": 34.742, "width_percent": 3.93}, {"sql": "select count(distinct `investor_id`) as aggregate from `investments` where `deal_id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DealController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\Admin\\DealController.php", "line": 83}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.7043371, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "DealController.php:83", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DealController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\Admin\\DealController.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FAdmin%2FDealController.php&line=83", "ajax": false, "filename": "DealController.php", "line": "83"}, "connection": "main", "explain": null, "start_percent": 38.672, "width_percent": 5.716}, {"sql": "select * from `deal_ach_settings` where `deal_ach_settings`.`deal_id` = 2 and `deal_ach_settings`.`deal_id` is not null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/DealController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\Admin\\DealController.php", "line": 87}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.714808, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "DealController.php:87", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/DealController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\Admin\\DealController.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FAdmin%2FDealController.php&line=87", "ajax": false, "filename": "DealController.php", "line": "87"}, "connection": "main", "explain": null, "start_percent": 44.388, "width_percent": 4.912}, {"sql": "select * from `distributions` where `distributions`.`deal_id` = 2 and `distributions`.`deal_id` is not null and `amount` is not null and `amount` != ''", "type": "query", "params": [], "bindings": [2, ""], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DealController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\Admin\\DealController.php", "line": 93}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.720914, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "DealController.php:93", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DealController.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Controllers\\Admin\\DealController.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FAdmin%2FDealController.php&line=93", "ajax": false, "filename": "DealController.php", "line": "93"}, "connection": "main", "explain": null, "start_percent": 49.3, "width_percent": 4.347}, {"sql": "select * from `distributions` where `distributions`.`deal_id` = 2 and `distributions`.`deal_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "admin.deals.index", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/deals/index.blade.php", "line": 485}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.746296, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "admin.deals.index:485", "source": {"index": 20, "namespace": "view", "name": "admin.deals.index", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/deals/index.blade.php", "line": 485}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fadmin%2Fdeals%2Findex.blade.php&line=485", "ajax": false, "filename": "index.blade.php", "line": "485"}, "connection": "main", "explain": null, "start_percent": 53.647, "width_percent": 4.198}, {"sql": "select count(*) as aggregate from `users` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 63}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 23}], "start": **********.766494, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:63", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=63", "ajax": false, "filename": "AppServiceProvider.php", "line": "63"}, "connection": "main", "explain": null, "start_percent": 57.845, "width_percent": 4.495}, {"sql": "select count(*) as aggregate from `users` where `ev` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 64}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 23}], "start": **********.770791, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:64", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=64", "ajax": false, "filename": "AppServiceProvider.php", "line": "64"}, "connection": "main", "explain": null, "start_percent": 62.34, "width_percent": 2.65}, {"sql": "select count(*) as aggregate from `users` where `sv` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 65}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 23}], "start": **********.773413, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:65", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=65", "ajax": false, "filename": "AppServiceProvider.php", "line": "65"}, "connection": "main", "explain": null, "start_percent": 64.99, "width_percent": 3.305}, {"sql": "select count(*) as aggregate from `users` where `kv` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 23}], "start": **********.7771502, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:66", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=66", "ajax": false, "filename": "AppServiceProvider.php", "line": "66"}, "connection": "main", "explain": null, "start_percent": 68.294, "width_percent": 3.007}, {"sql": "select count(*) as aggregate from `users` where `kv` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 67}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 23}], "start": **********.780422, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:67", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 67}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=67", "ajax": false, "filename": "AppServiceProvider.php", "line": "67"}, "connection": "main", "explain": null, "start_percent": 71.301, "width_percent": 1.548}, {"sql": "select count(*) as aggregate from `support_tickets` where `status` in (0, 2)", "type": "query", "params": [], "bindings": [0, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 68}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 23}], "start": **********.78762, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:68", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=68", "ajax": false, "filename": "AppServiceProvider.php", "line": "68"}, "connection": "main", "explain": null, "start_percent": 72.849, "width_percent": 3.692}, {"sql": "select count(*) as aggregate from `deposits` where `method_code` >= 1000 and `status` = 2", "type": "query", "params": [], "bindings": [1000, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 69}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 23}], "start": **********.79305, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:69", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=69", "ajax": false, "filename": "AppServiceProvider.php", "line": "69"}, "connection": "main", "explain": null, "start_percent": 76.541, "width_percent": 5.329}, {"sql": "select count(*) as aggregate from `withdrawals` where `status` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 23}], "start": **********.8000221, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:70", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=70", "ajax": false, "filename": "AppServiceProvider.php", "line": "70"}, "connection": "main", "explain": null, "start_percent": 81.87, "width_percent": 4.525}, {"sql": "select * from `profits` where `status` = 0 group by `property_id`", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 72}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 22, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 23}], "start": **********.808032, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:72", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=72", "ajax": false, "filename": "AppServiceProvider.php", "line": "72"}, "connection": "main", "explain": null, "start_percent": 86.395, "width_percent": 5.359}, {"sql": "select * from `admin_notifications` where `is_read` = 0 order by `id` desc limit 10", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 78}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 22, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 24}], "start": **********.848594, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:78", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=78", "ajax": false, "filename": "AppServiceProvider.php", "line": "78"}, "connection": "main", "explain": null, "start_percent": 91.753, "width_percent": 4.882}, {"sql": "select count(*) as aggregate from `admin_notifications` where `is_read` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 79}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/app.blade.php", "line": 24}], "start": **********.854452, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:79", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=79", "ajax": false, "filename": "AppServiceProvider.php", "line": "79"}, "connection": "main", "explain": null, "start_percent": 96.636, "width_percent": 3.364}]}, "models": {"data": {"App\\Models\\Distribution": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FModels%2FDistribution.php&line=1", "ajax": false, "filename": "Distribution.php", "line": "?"}}, "App\\Models\\Gateway": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FModels%2FGateway.php&line=1", "ajax": false, "filename": "Gateway.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Deal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FModels%2FDeal.php&line=1", "ajax": false, "filename": "Deal.php", "line": "?"}}, "App\\Models\\DealAchSetting": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FModels%2FDealAchSetting.php&line=1", "ajax": false, "filename": "DealAchSetting.php", "line": "?"}}}, "count": 9, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/deals", "action_name": "admin.deals.index", "controller_action": "App\\Http\\Controllers\\Admin\\DealController@index", "uri": "GET admin/deals", "controller": "App\\Http\\Controllers\\Admin\\DealController@index<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FAdmin%2FDealController.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "/admin", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FAdmin%2FDealController.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/DealController.php:51-110</a>", "middleware": "web, admin, permission:deals.view", "duration": "949ms", "peak_memory": "34MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-639219146 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-639219146\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-551345859 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-551345859\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1319776955 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/admin/system/info</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"105 characters\">XSRF-TOKEN=pLm3TihqRtb34dGMldUxyW5DOGNvHVKdyksoPZZ8; cre_session=aMheYUN422CXHKbhZMa5iyXxDfyUJaMeBlHhboSA</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1319776955\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-365047719 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pLm3TihqRtb34dGMldUxyW5DOGNvHVKdyksoPZZ8</span>\"\n  \"<span class=sf-dump-key>cre_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aMheYUN422CXHKbhZMa5iyXxDfyUJaMeBlHhboSA</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-365047719\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-226674261 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 03:14:08 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-226674261\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-865572273 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pLm3TihqRtb34dGMldUxyW5DOGNvHVKdyksoPZZ8</span>\"\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/placeholder-image/400x400</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-865572273\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/deals", "action_name": "admin.deals.index", "controller_action": "App\\Http\\Controllers\\Admin\\DealController@index"}, "badge": null}}