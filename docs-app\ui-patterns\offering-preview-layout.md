# Offering Preview Layout Implementation

## Overview
This document describes the implementation of a minimal layout for non-authenticated offering preview pages, which removes the full header while maintaining essential UI elements.

## Implementation Date
February 15, 2025

## Problem Solved
Previously, non-authenticated users viewing offering previews would see the full application header, which was unnecessary and potentially distracting. The solution provides a cleaner, more focused preview experience.

## Key Files Modified

### 1. Preview Layout Template
**File**: `resources/views/templates/basic/layouts/preview.blade.php` (New)
```php
@extends($activeTemplate . 'layouts.app')

@section('main-content')
    @yield('content')
@endsection

@push('style-lib')
    <link href="{{ asset($activeTemplateTrue . 'css/slick.css') }}" rel="stylesheet">
@endpush

@push('script-lib')
    <script src="{{ asset($activeTemplateTrue . 'js/slick.min.js') }}"></script>
@endpush
```

### 2. Offering Template
**File**: `resources/views/templates/basic/user/deals/offerings/offering.blade.php`
```php
@extends(auth()->check() ? $activeTemplate . 'layouts.master' : $activeTemplate . 'layouts.preview')
```

## UI Elements Preserved
- Logo on left
- Sign-in button on right
- Centered main content
- All offering details and functionality

## Technical Implementation Details
- Extends base app layout for core functionality
- Maintains all necessary asset includes (CSS/JS)
- Preserves slick carousel for image galleries
- Clean separation between authenticated and non-authenticated views

## Layout Inheritance Chain
```
offering.blade.php
↳ preview.blade.php (non-auth)
  ↳ app.blade.php
OR
↳ master.blade.php (auth)
  ↳ app.blade.php
```

## Troubleshooting Guide

### Common Issues and Solutions

1. **Header Still Appearing**
   - Check if any other templates are including the header partial
   - Verify the auth check is working correctly
   - Clear Laravel view cache: `php artisan view:clear`

2. **Missing Styles/Scripts**
   - The preview layout must extend `app.blade.php`
   - Required assets:
     ```php
     @push('style-lib')
         <link href="{{ asset($activeTemplateTrue . 'css/slick.css') }}" rel="stylesheet">
     @endpush
     @push('script-lib')
         <script src="{{ asset($activeTemplateTrue . 'js/slick.min.js') }}"></script>
     @endpush
     ```

3. **Authentication Issues**
   - The `auth()->check()` relies on proper session configuration
   - Verify session middleware is properly configured
   - Check for any custom authentication middleware that might interfere

### Quick Fixes
1. Clear Laravel caches:
   ```bash
   php artisan view:clear
   php artisan cache:clear
   php artisan config:clear
   ```
2. Verify template paths in `config/view.php`
3. Check `resources/views/templates/basic/layouts/` exists and has correct permissions

## Related Components
- Authentication middleware
- Session configuration
- Template inheritance system
- Asset management

## Reusability
This pattern can be reused for other preview pages that need a minimal layout for non-authenticated users while maintaining full functionality for authenticated users.
