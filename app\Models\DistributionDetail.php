<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DistributionDetail extends Model
{
    protected $fillable = [
        'deal_id',
        'offering_id',
        'investment_id',
        'distribution_id',
        'amount',
        'is_distributed',
        'is_transferred',
        'transaction_id',
    ];

    protected $casts = [
        'is_distributed' => 'boolean',
        'amount' => 'decimal:2',
    ];

    public function deal()
    {
        return $this->belongsTo(Deal::class);
    }

    public function offering()
    {
        return $this->belongsTo(Offering::class);
    }

    public function investment()
    {
        return $this->belongsTo(Investment::class);
    }

    public function distribution()
    {
        return $this->belongsTo(Distribution::class);
    }
}