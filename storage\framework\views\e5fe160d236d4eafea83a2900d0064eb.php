<?php
    $pages = App\Models\Page::where('tempname', $activeTemplate)
        ->where('is_default', Status::NO)
        ->get();
?>

<header class="header " id="header">
    <div class="container ">
        <nav class="navbar navbar-expand-lg">
            <a class="navbar-brand logo order-1" href="<?php echo e(route('home')); ?>">
                <img src="<?php echo e(siteLogo()); ?>" alt="logo">
            </a>
            <button class="navbar-toggler header-button order-3 order-lg-2" type="button" data-bs-toggle="collapse"
                data-bs-target="#navbar-content" aria-expanded="false">
                <i class="las la-bars"></i>
            </button>
            <div class="collapse navbar-collapse order-4 order-lg-3" id="navbar-content">
                <ul class="navbar-nav nav-menu ms-auto align-items-lg-center">
                    <li>
                        <div class="navbar-actions navbar-actions--sm">
                            <?php echo $__env->make($activeTemplate . 'partials.language', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            <?php if(auth()->guard()->guest()): ?>
                                <a href="<?php echo e(route('user.login')); ?>" class="btn btn--base"><?php echo app('translator')->get('Login'); ?></a>
                            <?php endif; ?>
                            <?php if(auth()->guard()->check()): ?>
                                <?php if(!request()->routeIs('user.*') && !request()->routeIs('ticket*')): ?>
                                    <a href="<?php echo e(route('user.home')); ?>" class="btn btn--base"><?php echo app('translator')->get('Dashboard'); ?></a>
                                <?php else: ?>
                                    <div class="user-info">
                                        <button class="user-info__button flex-align">
                                            <span class="user-info__name">
                                                <?php echo app('translator')->get('More'); ?>
                                            </span>
                                        </button>
                                        <ul class="user-info-dropdown">
                                            <li class="user-info-dropdown__item">
                                                <a class="user-info-dropdown__link <?php echo e(menuActive('user.profile.setting')); ?>"
                                                    href="<?php echo e(route('user.profile.setting')); ?>">
                                                    <span class="icon"><i class="far fa-user-circle"></i></span>
                                                    <span class="text"><?php echo app('translator')->get('Profile Setting'); ?></span>
                                                </a>
                                            </li>
                                            <li class="user-info-dropdown__item">
                                                <a class="user-info-dropdown__link <?php echo e(menuActive('user.change.password')); ?>"
                                                    href="<?php echo e(route('user.change.password')); ?>">
                                                    <span class="icon"><i class="fas fa-cog"></i></span>
                                                    <span class="text"><?php echo app('translator')->get('Change Password'); ?></span>
                                                </a>
                                            </li>
                                            <li class="user-info-dropdown__item">
                                                <a class="user-info-dropdown__link <?php echo e(menuActive('user.twofactor')); ?>"
                                                    href="<?php echo e(route('user.twofactor')); ?>">
                                                    <span class="icon"><i class="fas fa-shield-alt"></i></span>
                                                    <span class="text"><?php echo app('translator')->get('2FA Security'); ?></span>
                                                </a>
                                            </li>
                                            <li class="user-info-dropdown__item">
                                                <a class="user-info-dropdown__link" href="<?php echo e(route('user.logout')); ?>">
                                                    <span class="icon"><i class="fas fa-sign-out-alt"></i></span>
                                                    <span class="text"><?php echo app('translator')->get('Logout'); ?></span>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(menuActive('home')); ?>" href="<?php echo e(route('home')); ?>"><?php echo app('translator')->get('Home'); ?></a>
                    </li>
                    <?php $__currentLoopData = @$pages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li class="nav-item <?php echo e(menuActive('pages', null, @$data->slug)); ?>">
                        <a href="<?php echo e(route('pages', @$data->slug)); ?>" class="nav-link"><?php echo e(__(@$data->name)); ?></a>
                    </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(menuActive('exploreDeals*')); ?>"
                            href="<?php echo e(route('deals')); ?>"><?php echo app('translator')->get('Deals'); ?></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(menuActive('blog*')); ?>" href="<?php echo e(route('blog')); ?>"><?php echo app('translator')->get('Blog'); ?></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(menuActive('contact')); ?>"
                            href="<?php echo e(route('contact')); ?>"><?php echo app('translator')->get('Contact'); ?></a>
                    </li>
                </ul>
            </div>
            <div class="navbar-actions navbar-actions--md order-2 order-lg-4">
                <?php echo $__env->make($activeTemplate . 'partials.language', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <?php if(auth()->guard()->guest()): ?>
                    <a href="<?php echo e(route('user.login')); ?>" class="btn btn--base"><?php echo app('translator')->get('Login'); ?></a>
                <?php endif; ?>
                <?php if(auth()->guard()->check()): ?>
                    <?php if(!request()->routeIs('user.*') && !request()->routeIs('ticket*')): ?>
                        <a href="<?php echo e(route('user.home')); ?>" class="btn btn--base"><?php echo app('translator')->get('Dashboard'); ?></a>
                    <?php else: ?>
                        <div class="user-info">
                            <button class="user-info__button flex-align">
                                <span class="user-info__name">
                                    <?php echo app('translator')->get('More'); ?>
                                </span>
                            </button>
                            <ul class="user-info-dropdown">
                                <li class="user-info-dropdown__item">
                                    <a class="user-info-dropdown__link" href="<?php echo e(route('user.profile.setting')); ?>">
                                        <span class="icon"><i class="far fa-user-circle"></i></span>
                                        <span class="text"><?php echo app('translator')->get('Profile Setting'); ?></span>
                                    </a>
                                </li>
                                <li class="user-info-dropdown__item">
                                    <a class="user-info-dropdown__link" href="<?php echo e(route('user.change.password')); ?>">
                                        <span class="icon"><i class="fas fa-cog"></i></span>
                                        <span class="text"><?php echo app('translator')->get('Change Password'); ?></span>
                                    </a>
                                </li>
                                <li class="user-info-dropdown__item">
                                    <a class="user-info-dropdown__link" href="<?php echo e(route('user.twofactor')); ?>">
                                        <span class="icon"><i class="fas fa-shield-alt"></i></span>
                                        <span class="text"><?php echo app('translator')->get('2FA Security'); ?></span>
                                    </a>
                                </li>
                                <li class="user-info-dropdown__item">
                                    <a class="user-info-dropdown__link" href="<?php echo e(route('user.logout')); ?>">
                                        <span class="icon"><i class="fas fa-sign-out-alt"></i></span>
                                        <span class="text"><?php echo app('translator')->get('Logout'); ?></span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </nav>
    </div>
</header>
<?php /**PATH C:\laragon\www\PLATFORM\resources\views/templates/basic/partials/header.blade.php ENDPATH**/ ?>