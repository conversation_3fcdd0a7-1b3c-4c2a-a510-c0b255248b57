{"__meta": {"id": "01K19SE5A143F3CTB29APFCX9K", "datetime": "2025-07-28 20:31:07", "utime": **********.073755, "method": "GET", "uri": "/user/login", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753749065.810196, "end": **********.073773, "duration": 1.2635769844055176, "duration_str": "1.26s", "measures": [{"label": "Booting", "start": 1753749065.810196, "relative_start": 0, "end": **********.190224, "relative_end": **********.190224, "duration": 0.*****************, "duration_str": "380ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.190268, "relative_start": 0.*****************, "end": **********.073775, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "884ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.225292, "relative_start": 0.****************, "end": **********.237478, "relative_end": **********.237478, "duration": 0.012186050415039062, "duration_str": "12.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.278468, "relative_start": 0.*****************, "end": **********.070713, "relative_end": **********.070713, "duration": 0.****************, "duration_str": "792ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: Template::user.auth.login", "start": **********.282829, "relative_start": 0.*****************, "end": **********.282829, "relative_end": **********.282829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.partials.social_login", "start": **********.778508, "relative_start": 0.****************, "end": **********.778508, "relative_end": **********.778508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.<PERSON><PERSON>a", "start": **********.023531, "relative_start": 1.2133350372314453, "end": **********.023531, "relative_end": **********.023531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.layouts.app", "start": **********.039477, "relative_start": 1.2292811870574951, "end": **********.039477, "relative_end": **********.039477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.seo", "start": **********.047058, "relative_start": 1.2368621826171875, "end": **********.047058, "relative_end": **********.047058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.notify", "start": **********.066823, "relative_start": 1.256627082824707, "end": **********.066823, "relative_end": **********.066823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 29854288, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.7", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/New_York", "Locale": "en"}}, "views": {"count": 6, "nb_templates": 6, "templates": [{"name": "Template::user.auth.login", "param_count": null, "params": [], "start": **********.282646, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/user/auth/login.blade.phpTemplate::user.auth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fuser%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}}, {"name": "templates.basic.partials.social_login", "param_count": null, "params": [], "start": **********.778382, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/partials/social_login.blade.phptemplates.basic.partials.social_login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fsocial_login.blade.php&line=1", "ajax": false, "filename": "social_login.blade.php", "line": "?"}}, {"name": "partials.cap<PERSON>a", "param_count": null, "params": [], "start": **********.023415, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/partials/captcha.blade.phppartials.captcha", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fpartials%2Fcaptcha.blade.php&line=1", "ajax": false, "filename": "captcha.blade.php", "line": "?"}}, {"name": "templates.basic.layouts.app", "param_count": null, "params": [], "start": **********.039415, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/layouts/app.blade.phptemplates.basic.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "partials.seo", "param_count": null, "params": [], "start": **********.046932, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/partials/seo.blade.phppartials.seo", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fpartials%2Fseo.blade.php&line=1", "ajax": false, "filename": "seo.blade.php", "line": "?"}}, {"name": "partials.notify", "param_count": null, "params": [], "start": **********.066706, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/partials/notify.blade.phppartials.notify", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fpartials%2Fnotify.blade.php&line=1", "ajax": false, "filename": "notify.blade.php", "line": "?"}}]}, "queries": {"count": 8, "nb_statements": 7, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04102999999999999, "accumulated_duration_str": "41.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 359}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.718812, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "helpers.php:359", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=359", "ajax": false, "filename": "helpers.php", "line": "359"}, "connection": "main", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `frontends` where `tempname` = 'basic' and `data_keys` = 'login.content' order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["basic", "login.content"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 359}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.735615, "duration": 0.03302, "duration_str": "33.02ms", "memory": 0, "memory_str": null, "filename": "helpers.php:359", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=359", "ajax": false, "filename": "helpers.php", "line": "359"}, "connection": "main", "explain": null, "start_percent": 0, "width_percent": 80.478}, {"sql": "select * from `extensions` where `act` = 'custom-captcha' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["custom-captcha", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.0275772, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "Captcha.php:39", "source": {"index": 16, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FLib%2FCaptcha.php&line=39", "ajax": false, "filename": "Captcha.php", "line": "39"}, "connection": "main", "explain": null, "start_percent": 80.478, "width_percent": 4.192}, {"sql": "select * from `extensions` where `act` = 'google-recaptcha2' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["google-recaptcha2", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 27}, {"index": 17, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 76}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.033895, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "Captcha.php:27", "source": {"index": 16, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FLib%2FCaptcha.php&line=27", "ajax": false, "filename": "Captcha.php", "line": "27"}, "connection": "main", "explain": null, "start_percent": 84.67, "width_percent": 3.924}, {"sql": "select * from `frontends` where `data_keys` = 'seo.data' limit 1", "type": "query", "params": [], "bindings": ["seo.data"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 84}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 161}, {"index": 23, "namespace": "view", "name": "templates.basic.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/layouts/app.blade.php", "line": 10}], "start": **********.041477, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:84", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Providers\\AppServiceProvider.php", "line": 84}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FProviders%2FAppServiceProvider.php&line=84", "ajax": false, "filename": "AppServiceProvider.php", "line": "84"}, "connection": "main", "explain": null, "start_percent": 88.594, "width_percent": 2.681}, {"sql": "select * from `extensions` where `act` = 'google-analytics' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["google-analytics", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 91}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.051626, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "helpers.php:91", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=91", "ajax": false, "filename": "helpers.php", "line": "91"}, "connection": "main", "explain": null, "start_percent": 91.275, "width_percent": 2.681}, {"sql": "select * from `frontends` where `data_keys` = 'cookie.data' limit 1", "type": "query", "params": [], "bindings": ["cookie.data"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "templates.basic.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/layouts/app.blade.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.056085, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "templates.basic.layouts.app:41", "source": {"index": 16, "namespace": "view", "name": "templates.basic.layouts.app", "file": "C:\\laragon\\www\\PLATFORM\\resources\\views/templates/basic/layouts/app.blade.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Flayouts%2Fapp.blade.php&line=41", "ajax": false, "filename": "app.blade.php", "line": "41"}, "connection": "main", "explain": null, "start_percent": 93.956, "width_percent": 2.803}, {"sql": "select * from `extensions` where `act` = 'tawk-chat' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["tawk-chat", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 91}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.0624151, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "helpers.php:91", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=91", "ajax": false, "filename": "helpers.php", "line": "91"}, "connection": "main", "explain": null, "start_percent": 96.758, "width_percent": 3.242}]}, "models": {"data": {"App\\Models\\Frontend": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FModels%2FFrontend.php&line=1", "ajax": false, "filename": "Frontend.php", "line": "?"}}}, "count": 3, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/user/login", "action_name": "user.login", "controller_action": "App\\Http\\Controllers\\User\\Auth\\LoginController@showLoginForm", "uri": "GET user/login", "controller": "App\\Http\\Controllers\\User\\Auth\\LoginController@showLoginForm<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FUser%2FAuth%2FLoginController.php&line=25\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\User\\Auth", "prefix": "/user", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FUser%2FAuth%2FLoginController.php&line=25\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/User/Auth/LoginController.php:25-30</a>", "middleware": "web, maintenance, guest", "duration": "1.27s", "peak_memory": "30MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1485071689 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>username</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1485071689\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-232959257 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-232959257\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-846517977 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;130&quot;, &quot;Google Chrome&quot;;v=&quot;130&quot;, &quot;Not?A_Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"105 characters\">XSRF-TOKEN=lbl3Mp4IWnWsfnjOsypfTJKrJtYPVJvx6VioNUKK; cre_session=t34d64aqUEh0BKRZIr2OAUkKxFPCOPzujhJX1s4A</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-846517977\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1428118115 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lbl3Mp4IWnWsfnjOsypfTJKrJtYPVJvx6VioNUKK</span>\"\n  \"<span class=sf-dump-key>cre_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">t34d64aqUEh0BKRZIr2OAUkKxFPCOPzujhJX1s4A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1428118115\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-780824418 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 00:31:06 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-780824418\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-570236805 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lbl3Mp4IWnWsfnjOsypfTJKrJtYPVJvx6VioNUKK</span>\"\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://127.0.0.1:8000/placeholder-image/65x65</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-570236805\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/user/login", "action_name": "user.login", "controller_action": "App\\Http\\Controllers\\User\\Auth\\LoginController@showLoginForm"}, "badge": null}}