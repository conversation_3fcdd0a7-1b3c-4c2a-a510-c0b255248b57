# Roles, Permissions, and Staff Management Documentation

This directory contains all documentation related to the roles, permissions, and staff management systems in the CREI Investment Platform.

## Core Principles

As stated in our fundamental principles: **Roles and permissions are there to interact with existing modules and not the other way around**. The modules should NOT be changed to adjust to the roles and permissions structure.

## Available Documentation

### Roles and Permissions

1. [Core Principles](./roles-permissions-core-principles.md) - Fundamental principles guiding the roles and permissions system
2. [Implementation Overview](./roles-permissions-implementation-overview.md) - High-level overview of the implementation
3. [Database Schema](./roles-permissions-database-schema.md) - Database structure for roles and permissions
4. [Implementation Guide](./roles-permissions-implementation-guide.md) - Detailed implementation guide
5. [Controller Updates](./roles-permissions-controller-updates.md) - Updates to controllers for roles and permissions
6. [Middleware Enhancements](./roles-permissions-middleware-enhancements.md) - Middleware changes for roles and permissions
7. [Seeder Implementation](./roles-permissions-seeder-implementation.md) - Database seeders for roles and permissions
8. [UI Improvements](./roles-permissions-ui-improvements.md) - User interface for managing roles and permissions
9. [Testing Plan](./roles-permissions-testing-plan.md) - Testing strategy for roles and permissions
10. [Permissions Documentation](./permissions_documentation.md) - Detailed documentation of available permissions
11. [Roles and Permissions Overview](./roles_and_permissions.md) - General overview of the roles and permissions system

### Staff Management

1. [Staff Management](./staff_management.md) - Overview of staff management functionality
2. [Staff Permissions System](./staff_permissions_system.md) - Permissions system specific to staff
3. [Organization Structure](./Organization%20Structure%20-%20Departments%20%26%20Staff.md) - Departments and staff organizational structure

## Key Components

### Models
- Role
- Permission
- Admin (Staff members)

### Controllers
- RoleController
- PermissionController
- StaffController

### Middleware
- RoleMiddleware
- PermissionMiddleware

## Implementation Approach

Following Laravel's MVC architecture, the roles and permissions system is built on top of Spatie's Laravel-Permission package with customizations to fit the platform's specific needs. The system ensures:

1. **Granular Access Control**: Permissions are specific to actions and resources
2. **Role-Based Access**: Users are assigned roles which contain collections of permissions
3. **Middleware Protection**: Routes are protected by role and permission middleware
4. **UI Integration**: Admin interface for managing roles and permissions

## Best Practices

1. Always use middleware to protect routes
2. Check permissions in controllers for additional security
3. Use blade directives for conditional UI rendering based on permissions
4. Follow the principle that roles adapt to the system, not vice versa
