<?php
    $featureContent = getContent('feature.content', true);
    $featureElements = getContent('feature.element', limit: 4, orderById: true);
?>
<section class="why-invest py-120">
    <div class="container ">
        <div class="section-heading">
            <p class="section-heading__subtitle"><?php echo e(__(@$featureContent->data_values->title)); ?></p>
            <h2 class="section-heading__title"><?php echo e(__(@$featureContent->data_values->heading)); ?></h2>
        </div>
        <div class="row gy-4 justify-content-center">
            <?php $__currentLoopData = @$featureElements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $featureElement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-xsm-6 col-sm-6 col-md-4 col-lg-3">
                    <div class="why-invest-card">
                        <span class="why-invest-card__icon">
                            <?php echo @$featureElement->data_values->feature_icon; ?>
                        </span>
                        <div class="why-invest-card__content">
                            <h6 class="why-invest-card__title"><?php echo e(__(@$featureElement->data_values->title)); ?></h6>
                            <p class="why-invest-card__desc"><?php echo e(__(@$featureElement->data_values->description)); ?></p>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php /**PATH C:\laragon\www\PLATFORM\resources\views/templates/basic/sections/feature.blade.php ENDPATH**/ ?>