<?php $__currentLoopData = $featuredProperties; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $featuredProperty): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <div class="card border-0 property-horizontal--card">
        <a class="card-img card-img--lg" href="<?php echo e(route('property.details', @$featuredProperty->slug)); ?>">
            <img src="<?php echo e(getImage(getFilePath('propertyThumb') . '/' . @$featuredProperty->thumb_image, getFileSize('propertyThumb'))); ?>"
                alt="property-image">
        </a>
        <div class="card-body py-md-4 px-md-4">
            <div class="card-body-top mb-4">
                <div class="card-wrapper flex-column">
                    <h4 class="card-title mb-1">
                        <a href="<?php echo e(route('property.details', @$featuredProperty->slug)); ?>">
                            <?php echo e(__(@$featuredProperty->title)); ?>

                        </a>
                    </h4>
                    <ul class="card-meta card-meta--one">
                        <li class="card-meta__item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span class="text"><?php echo e(__(@$featuredProperty->location->name)); ?></span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="card-body-middle mb-4">
                <div class="card-progress mb-4">
                    <div class="card-progress__bar">
                        <div class="card-progress__thumb" style="width: <?php echo e(@$featuredProperty->invest_progress); ?>%;">
                        </div>
                    </div>
                    <span class="card-progress__label fs-12">
                        <?php echo e(@$featuredProperty->invests_count); ?> <?php echo app('translator')->get('Investors'); ?> |
                        <?php echo e(showAmount(@$featuredProperty->invested_amount)); ?>

                        (<?php echo e(getAmount(@$featuredProperty->invest_progress)); ?>%)
                    </span>
                </div>
                <ul class="card-meta card-meta--two">
                    <li class="card-meta__item">
                        <div class="text">
                            <?php echo e(@$featuredProperty->getProfit); ?>

                        </div>
                        <span class="subtext"><?php echo app('translator')->get('Profit'); ?></span>
                    </li>
                    <li class="card-meta__item">
                        <div class="text">
                            <span><?php echo e(@$featuredProperty->getProfitSchedule); ?></span>
                        </div>
                        <span class="subtext"><?php echo app('translator')->get('Profit Schedule'); ?></span>
                    </li>
                    <li class="card-meta__item">
                        <div class="text">
                            <?php echo e(@$featuredProperty->getCapitalBackStatus); ?>

                        </div>
                        <span class="subtext"><?php echo app('translator')->get('Capital Back'); ?></span>
                    </li>
                </ul>
            </div>
            <div class="card-body-bottom">
                <a class="btn btn--sm btn--base" href="<?php echo e(route('property.details', @$featuredProperty->slug)); ?>"
                    role="button">
                    <?php echo app('translator')->get('Details'); ?>
                </a>
                <span class="card-price">
                    <?php echo e(showAmount(@$featuredProperty->per_share_amount)); ?>

                </span>
            </div>
        </div>
    </div>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\laragon\www\PLATFORM\resources\views/templates/basic/partials/featured_property.blade.php ENDPATH**/ ?>