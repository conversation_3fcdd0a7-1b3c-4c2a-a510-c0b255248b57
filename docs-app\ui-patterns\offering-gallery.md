# Investment Offering Gallery Implementation

## Overview
Enhanced gallery implementation for the investment offering preview page with synchronized carousels and improved image display.

## Technical Specifications

### Main Gallery
- Main image container height: 385px
- Thumbnail height: 118px
- Image display: object-fit: cover
- Hover preview functionality
- Responsive image sizing

### Modal Gallery
- Main carousel with fade transitions
- Synchronized thumbnail strip (6 thumbnails visible)
- Thumbnail dimensions: 60px height
- Thumbnail states: 0.6 opacity (default), 1.0 opacity (active)
- Smooth transitions between slides

### Key Components

1. Main Gallery Structure:
```php
<div class="d-flex justify-content-center align-items-center bg-light position-relative" style="height: 385px;">
    <img id="mainImage" style="height: 100%; width: 100%; object-fit: cover;">
</div>
```

2. Thumbnail Grid:
```php
<div class="image-box" style="height: 118px;">
    <img class="gallery-thumb" style="height: 100%; width: 100%; object-fit: cover;">
</div>
```

3. Modal Implementation:
```php
<div class="gallery-carousel">
    <!-- Main carousel images -->
</div>
<div class="gallery-thumbnails">
    <!-- Thumbnail strip -->
</div>
```

### JavaScript Configuration
```javascript
// Main carousel
$('.gallery-carousel').slick({
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: true,
    fade: true,
    asNavFor: '.gallery-thumbnails'
});

// Thumbnail carousel
$('.gallery-thumbnails').slick({
    slidesToShow: 6,
    slidesToScroll: 1,
    asNavFor: '.gallery-carousel',
    dots: false,
    arrows: false,
    focusOnSelect: true
});
```

### Key CSS Styles
```css
.gallery-carousel .slick-slide img {
    height: 500px;
    object-fit: cover;
}

.gallery-thumbnails .thumbnail-item img {
    height: 60px;
    object-fit: cover;
    opacity: 0.6;
    transition: opacity 0.3s;
}

.gallery-thumbnails .thumbnail-item.slick-current img {
    opacity: 1;
}
```

### Important: Z-Index Stacking Context
When implementing the gallery modal in authenticated views (dashboard layout), special attention must be paid to z-index stacking context to ensure navigation arrows remain visible and clickable.

#### Critical CSS for Navigation Arrows
```css
/* Essential z-index hierarchy for authenticated view */
#galleryModal {
    z-index: 1060 !important; /* Above Bootstrap's default modal z-index (1050) */
}

.gallery-carousel .slick-prev,
.gallery-carousel .slick-next {
    z-index: 1070 !important; /* Above modal to ensure clickability */
    background: rgba(0, 0, 0, 0.5);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 24px;
}
```

#### Troubleshooting Notes
1. **Dashboard Layout Impact**: The dashboard layout creates its own stacking context which can affect modal elements
2. **Z-Index Hierarchy**:
   - Bootstrap modals: 1050
   - Gallery modal: 1060
   - Navigation arrows: 1070
3. **Common Issues**:
   - Navigation arrows hidden behind images in authenticated view
   - Arrows visible but not clickable
   - Inconsistent behavior between authenticated and non-authenticated views

#### Solution Requirements
1. Modal must have higher z-index than Bootstrap's default (1050)
2. Navigation arrows must have higher z-index than the modal
3. Semi-transparent background helps with visibility
4. Larger font size (24px) improves usability

This z-index configuration ensures proper functionality across all views while maintaining the gallery's visual hierarchy.

## Features
1. Hover preview on thumbnails
2. Synchronized carousels in modal
3. Smooth transitions
4. Responsive image display
5. Optimized thumbnail navigation

## File Location
`resources/views/templates/basic/user/deals/offerings/offering.blade.php`

## Dependencies
- Slick Carousel
- jQuery
- Bootstrap 5 (for modal)

## Related Files
- `offering-preview-layout.md` - Main layout documentation
