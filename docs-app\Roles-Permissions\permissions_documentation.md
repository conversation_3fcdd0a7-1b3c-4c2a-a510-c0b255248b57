# Permissions Documentation

## Permissions Actually Used in Code

The following permissions are actually referenced in the codebase:

- `deals.view` (used in middleware)
- `investments.view` (used in middleware)
- `assets.view` (used in middleware)
- `system.view` (used in middleware)
- `system.manage_logs` (used in middleware)
- `system.manage_cache` (used in middleware)
- `roles.edit` (used in routes)
- `deals.create` (used in routes)
- `deals.edit` (used in routes)
- `investments.create` (used in routes)
- `assets.create` (used in routes)
- `assets.edit` (used in routes)
- `dashboard.view` (used in controllers)

## Sidebar Items and Corresponding Permissions

| Sidebar Item | Permission Prefix | Has Permissions |
|-------------|-------------------|-------------------|
| dashboard | dashboard | Yes |
| deals | deals | Yes |
| offerings | offerings | Yes |
| investment | investments | Yes |
| assets | assets | Yes |
| manage_properties | properties | No |
| manage_users | users | Yes |
| staff_management | staff | Yes |
| deposits | deposits | Yes |
| withdrawals | withdrawals | Yes |
| documents | documents | Yes |
| emails | emails | Yes |
| updates | updates | Yes |
| subscriber | subscribers | Yes |
| report_and_request | reports, requests | Yes |

## Sidebar Items Without Permissions

The following sidebar items do not have corresponding permissions:

- manage_properties (should use prefix: properties)

## Unused Permissions

The following permissions are not used in code and don't correspond to sidebar items:

- `executive.view`
- `executive.manage`
- `roles.view`
- `roles.create`
- `roles.delete`
- `accounting.view`
- `accounting.create`
- `accounting.edit`
- `accounting.delete`
- `accounting.approve_distributions`
- `accounting.manage_funding`
- `partnerships.view`
- `partnerships.create`
- `partnerships.edit`
- `partnerships.delete`
- `partnerships.approve`
- `investor_relations.view`
- `investor_relations.create`
- `investor_relations.edit`
- `investor_relations.delete`
- `marketing.view`
- `marketing.create`
- `marketing.edit`
- `marketing.delete`
- `general_management.view`
- `general_management.create`
- `general_management.edit`
- `general_management.delete`
- `tech.manage_integrations`
- `tech.manage_apis`
- `tech.troubleshoot`
- `tech.manage_user_access`
- `tech.view_logs`
- `tech.manage_data`
- `tech.system_monitoring`
- `compliance_legal.view`
- `compliance_legal.create`
- `compliance_legal.edit`
- `compliance_legal.delete`
- `property.approve`
- `property.create`
- `property.delete`
- `property.edit`
- `property.manage`
- `property.view`
- `referrals.view`
- `report_and_request.view`
- `support.approve`
- `support.create`
- `support.delete`
- `support.edit`
- `support.manage`
- `support.view`
