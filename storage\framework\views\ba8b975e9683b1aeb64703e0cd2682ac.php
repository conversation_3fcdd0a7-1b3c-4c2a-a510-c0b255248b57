<?php
    $credentials = gs('socialite_credentials');
    $text = isset($register) ? 'Register' : 'Login';
?>

<?php if(
    $credentials->google->status == Status::ENABLE ||
        $credentials->facebook->status == Status::ENABLE ||
        $credentials->linkedin->status == Status::ENABLE): ?>
    <div class="account-form__social-btns">
        <?php if($credentials->facebook->status == Status::ENABLE): ?>
            <div class="continue-facebook flex-grow-1">
                <a href="<?php echo e(route('user.social.login', 'facebook')); ?>" class="btn w-100 facebook">
                    <span class="facebook-icon">
                        <img src="<?php echo e(asset($activeTemplateTrue . 'images/facebook.svg')); ?>" alt="Facebook">
                    </span> <?php echo app('translator')->get('Facebook'); ?>
                </a>
            </div>
        <?php endif; ?>
        <?php if($credentials->google->status == Status::ENABLE): ?>
            <div class="continue-google flex-grow-1">
                <a href="<?php echo e(route('user.social.login', 'google')); ?>" class="btn w-100 google">
                    <span class="google-icon">
                        <img src="<?php echo e(asset($activeTemplateTrue . 'images/google.svg')); ?>" alt="Google">
                    </span> <?php echo app('translator')->get('Google'); ?>
                </a>
            </div>
        <?php endif; ?>
        <?php if($credentials->linkedin->status == Status::ENABLE): ?>
            <div class="continue-facebook flex-grow-1">
                <a href="<?php echo e(route('user.social.login', 'linkedin')); ?>" class="btn w-100 linkedin">
                    <span class="facebook-icon">
                        <img src="<?php echo e(asset($activeTemplateTrue . 'images/linkedin.svg')); ?>" alt="Linkedin">
                    </span> <?php echo app('translator')->get('Linkedin'); ?>
                </a>
            </div>
        <?php endif; ?>
    </div>
    <div class="other-option">
        <span class="other-option__text"><?php echo app('translator')->get('OR'); ?></span>
    </div>
<?php endif; ?>
<?php /**PATH C:\laragon\www\PLATFORM\resources\views/templates/basic/partials/social_login.blade.php ENDPATH**/ ?>