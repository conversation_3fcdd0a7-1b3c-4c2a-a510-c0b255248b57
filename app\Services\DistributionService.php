<?php

namespace App\Services;

use App\Models\Deal;
use App\Models\Distribution;
use App\Models\Investment;
use App\Models\Waterfall;
use App\Models\WaterfallHurdle;
use Illuminate\Support\Facades\DB;
use App\Models\Gateway;
use App\Models\Offering;
class DistributionService
{
    protected $stripeACHService;
    protected $stripeSecret;

    public function __construct(StripeACHService $stripeACHService)
    {
        $stripeAccSecret = $this->getStripeConfig()['secret_key'] ?? null;
        if (!$stripeAccSecret) {
            throw new \Exception('Stripe ACH gateway secret key not found in configuration.');
        }
        $this->stripeSecret = $stripeAccSecret['value'];
        $this->stripeACHService = $stripeACHService;
    }

    protected function getStripeConfig()
    {
        $stripeConfig = Gateway::where('code', '114')->first();
        if (!$stripeConfig) {
            throw new \Exception('Stripe ACH gateway configuration not found.');
        }
        return json_decode($stripeConfig->gateway_parameters, true);
    }

    /**
     * Calculate and distribute profit to all investors based on their contributions.
     *
     * @param int $offeringId
     * @param float $amount Total profit to distribute
     * @param int $waterfallId
     * @param string $distributionType
     * @param string $memo
     * @param array $attributes Additional distribution attributes
     * @return bool
     * @throws \Exception
     */
    public function createAndDistribute($offeringId, $amount, $waterfallId, $distributionType, $memo, array $attributes = [])
    {
        \Log::info("Starting distribution for Deal ID: {$offeringId}, Profit Amount: {$amount}, Waterfall ID: {$waterfallId}, Type: {$distributionType}");

        try {
            return DB::transaction(function () use ($offeringId, $amount, $waterfallId, $distributionType, $memo, $attributes) {
                // Load offering with investments and their completed transactions
                $offering = Offering::with(['investments.investor', 'investments.transactions' => function ($query) {
                    $query->where('status', 'completed')->whereNotNull('stripe_transaction_id');
                }])->findOrFail($offeringId);
                \Log::info("Loaded Deal ID: {$offeringId}, Name: {$offering->name}, Investments: {$offering->investments->count()}");

                if ($offering->investments->isEmpty()) {
                    \Log::warning("No investments found for Deal ID: {$offeringId}");
                    throw new \Exception("No investments available for distribution.");
                }

                // Log investment details for debugging
                \Log::debug("Investments for Deal ID: {$offeringId}", $offering->investments->map(function ($investment) {
                    return [
                        'investment_id' => $investment->id,
                        'investor_id' => $investment->investor_id,
                        'transaction' => $investment->transactions->first()?->toArray() ?? 'No transaction',
                    ];
                })->toArray());

                $waterfall = Waterfall::with('hurdles')->findOrFail($waterfallId);
                $hurdles = $waterfall->hurdles()->orderBy('sort_order')->get();
                \Log::info("Loaded Waterfall ID: {$waterfallId}, Hurdles: {$hurdles->count()}");

                // Validate total amount
                $remainingAmount = (float) preg_replace('/[^\d.]/', '', (string) $amount);
                if ($remainingAmount <= 0) {
                    \Log::warning("Invalid distribution amount for Deal ID: {$offeringId}: {$remainingAmount}");
                    throw new \Exception("Distribution amount must be greater than zero.");
                }

                // Calculate total contribution from completed transactions
                $totalContribution = $offering->investments->sum(function ($investment) {
                    $transaction = $investment->transactions->first();
                    if (!$transaction) {
                        \Log::warning("No completed transaction for Investment ID: {$investment->id}");
                        return 0;
                    }
                    return (float) $transaction->amount;
                });
                if ($totalContribution <= 0) {
                    \Log::warning("Total contribution is zero for Deal ID: {$offeringId}");
                    throw new \Exception("Total investment contribution is zero.");
                }
                \Log::info("Total contribution for Deal ID: {$offeringId}: {$totalContribution}");

                foreach ($hurdles as $hurdle) {
                    if ($remainingAmount <= 0) {
                        \Log::info("No remaining amount to distribute for Deal ID: {$offeringId}");
                        break;
                    }

                    \Log::info("Processing hurdle ID: {$hurdle->id}, Type: {$hurdle->hurdle_type}");

                    switch ($hurdle->hurdle_type) {
                        case WaterfallHurdle::HURDLE_TYPE_CASH_ON_CASH:
                            $remainingAmount = $this->distributeCashOnCash($offering, $hurdle, $remainingAmount, $totalContribution, $distributionType, $memo, $attributes);
                            break;
                        case WaterfallHurdle::HURDLE_TYPE_PREFERRED_RETURN:
                            $remainingAmount = $this->distributePreferredReturn($offering, $hurdle, $remainingAmount, $totalContribution, $distributionType, $memo, $attributes);
                            break;
                        case WaterfallHurdle::HURDLE_TYPE_SPLIT:
                            $remainingAmount = $this->distributeProfitSplit($offering, $hurdle, $remainingAmount, $totalContribution, $distributionType, $memo, $attributes);
                            break;
                        case WaterfallHurdle::HURDLE_TYPE_INTERNAL_RATE:
                            $remainingAmount = $this->distributeInternalRate($offering, $hurdle, $remainingAmount, $totalContribution, $distributionType, $memo, $attributes);
                            break;
                        case WaterfallHurdle::HURDLE_TYPE_RETURN_ON_INVESTMENT:
                            $remainingAmount = $this->distributeReturnOnInvestment($offering, $hurdle, $remainingAmount, $totalContribution, $distributionType, $memo, $attributes);
                            break;
                        case WaterfallHurdle::HURDLE_TYPE_MANAGEMENT_FEE:
                            $remainingAmount = $this->distributeManagementFee($offering, $hurdle, $remainingAmount, $distributionType, $memo, $attributes);
                            break;
                        case WaterfallHurdle::HURDLE_TYPE_RETURN_OF_CAPITAL:
                            $remainingAmount = $this->distributeReturnOfCapital($offering, $hurdle, $remainingAmount, $totalContribution, $distributionType, $memo, $attributes);
                            break;
                        case WaterfallHurdle::HURDLE_TYPE_CUMULATIVE_RETURN:
                            $remainingAmount = $this->distributeCumulativeReturn($offering, $hurdle, $remainingAmount, $totalContribution, $distributionType, $memo, $attributes);
                            break;
                        case WaterfallHurdle::HURDLE_TYPE_INTEREST:
                            $remainingAmount = $this->distributeInterest($offering, $hurdle, $remainingAmount, $totalContribution, $distributionType, $memo, $attributes);
                            break;
                        default:
                            \Log::warning("Unsupported hurdle type: {$hurdle->hurdle_type} for Deal ID: {$offeringId}");
                            break;
                    }
                }

                if ($remainingAmount > 0) {
                    \Log::info("Remaining amount for GP in Deal ID: {$offeringId}: {$remainingAmount}");
                }

                \Log::info("Completed distribution for Deal ID: {$offeringId}");
                return true;
            });
        } catch (\Exception $e) {
            \Log::error("Distribution failed for Deal ID: {$offeringId}: {$e->getMessage()}");
            throw $e;
        }
    }

    /**
     * Distribute cash-on-cash return to investors based on proportional contribution.
     */
    protected function distributeCashOnCash($offering, $hurdle, float $remainingAmount, float $totalContribution, $distributionType, $memo, $attributes)
    {
        $cashOnCashRate = (float) ($hurdle->cumulated_return_reach ?? 0.1); // Default to 10%
        \Log::info("Distributing cash-on-cash return for Deal ID: {$offering->id}, Rate: {$cashOnCashRate}");

        foreach ($offering->investments as $investment) {
            if ($remainingAmount <= 0) {
                \Log::info("No remaining amount for cash-on-cash return in Deal ID: {$offering->id}");
                break;
            }

            $transaction = $investment->transactions->first();
            if (!$transaction) {
                \Log::warning("No completed transaction for Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}");
                continue;
            }

            $investorContribution = (float) $transaction->amount;
            $investorShare = ($investorContribution / $totalContribution) * ($remainingAmount * $cashOnCashRate);
            $investorShare = min($investorShare, $remainingAmount);
            \Log::info("Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}, Contribution: {$investorContribution}, Cash-on-Cash Share: {$investorShare}");

            if ($investorShare > 0) {
                $distribution = $this->createDistributionRecord(
                    $offering->id,
                    $investment->id,
                    $investment->investor_id,
                    $investorShare,
                    $distributionType,
                    $hurdle->waterfall_id,
                    $memo . ' - Cash on Cash',
                    $attributes
                );
                try {
                    $stripeTransactionId = $this->stripeACHService->transferToInvestor(
                        $investment->investor,
                        $investorShare,
                        $offering->id,
                        $memo
                    );
                    $distribution->update(['stripe_transaction_id' => $stripeTransactionId]);
                    \Log::info("Created distribution ID: {$distribution->id}, Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}, Amount: {$investorShare}, Stripe Transaction ID: {$stripeTransactionId}");
                } catch (\Exception $e) {
                    \Log::error("Failed to transfer for Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}: {$e->getMessage()}");
                    throw $e;
                }

                $remainingAmount -= $investorShare;
            }
        }

        \Log::info("Remaining amount after cash-on-cash return for Deal ID: {$offering->id}: {$remainingAmount}");
        return $remainingAmount;
    }

    /**
     * Distribute preferred return to investors based on proportional contribution.
     */
    protected function distributePreferredReturn($offering, $hurdle, float $remainingAmount, float $totalContribution, $distributionType, $memo, $attributes)
    {
        $preferredReturnRate = (float) ($hurdle->preferred_return_rate ?? 0.08); // Default to 8%
        \Log::info("Distributing preferred return for Deal ID: {$offering->id}, Rate: {$preferredReturnRate}");

        foreach ($offering->investments as $investment) {
            if ($remainingAmount <= 0) {
                \Log::info("No remaining amount for preferred return in Deal ID: {$offering->id}");
                break;
            }

            $transaction = $investment->transactions->first();
            if (!$transaction) {
                \Log::warning("No completed transaction for Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}");
                continue;
            }

            $investorContribution = (float) $transaction->amount;
            $investorShare = ($investorContribution / $totalContribution) * ($remainingAmount * $preferredReturnRate);
            $investorShare = min($investorShare, $remainingAmount);
            \Log::info("Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}, Contribution: {$investorContribution}, Preferred Return Share: {$investorShare}");

            if ($investorShare > 0) {
                $distribution = $this->createDistributionRecord(
                    $offering->id,
                    $investment->id,
                    $investment->investor_id,
                    $investorShare,
                    $distributionType,
                    $hurdle->waterfall_id,
                    $memo . ' - Preferred Return',
                    $attributes
                );
                try {
                    $stripeTransactionId = $this->stripeACHService->transferToInvestor(
                        $investment->investor,
                        $investorShare,
                        $offering->id,
                        $memo
                    );
                    $distribution->update(['stripe_transaction_id' => $stripeTransactionId]);
                    \Log::info("Created distribution ID: {$distribution->id}, Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}, Amount: {$investorShare}, Stripe Transaction ID: {$stripeTransactionId}");
                } catch (\Exception $e) {
                    \Log::error("Failed to transfer for Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}: {$e->getMessage()}");
                    throw $e;
                }

                $remainingAmount -= $investorShare;
            }
        }

        \Log::info("Remaining amount after preferred return for Deal ID: {$offering->id}: {$remainingAmount}");
        return $remainingAmount;
    }

    /**
     * Distribute profit split to investors based on ownership percentage.
     */
    protected function distributeProfitSplit($offering, $hurdle, float $remainingAmount, float $totalContribution, $distributionType, $memo, $attributes)
    {
        $splits = $hurdle->splits ?? ['investors' => 0.7, 'gp' => 0.3];
        $investorSplit = $splits['investors'] ?? 0.7;
        $totalInvestorShare = $remainingAmount * $investorSplit;
        \Log::info("Distributing profit split for Deal ID: {$offering->id}, Investor Split: {$investorSplit}, Total Investor Share: {$totalInvestorShare}");

        foreach ($offering->investments as $investment) {
            if ($totalInvestorShare <= 0) {
                \Log::info("No remaining investor share for profit split in Deal ID: {$offering->id}");
                break;
            }

            $transaction = $investment->transactions->first();
            if (!$transaction) {
                \Log::warning("No completed transaction for Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}");
                continue;
            }

            $investorShare = $totalInvestorShare * ($investment->pcb_ownership / 100);
            $investorShare = min($investorShare, $totalInvestorShare);
            \Log::info("Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}, Ownership: {$investment->pcb_ownership}%, Profit Share: {$investorShare}");

            if ($investorShare > 0) {
                $distribution = $this->createDistributionRecord(
                    $offering->id,
                    $investment->id,
                    $investment->investor_id,
                    $investorShare,
                    $distributionType,
                    $hurdle->waterfall_id,
                    $memo . ' - Profit Split',
                    $attributes
                );
                try {
                    $stripeTransactionId = $this->stripeACHService->transferToInvestor(
                        $investment->investor,
                        $investorShare,
                        $offering->id,
                        $memo
                    );
                    $distribution->update(['stripe_transaction_id' => $stripeTransactionId]);
                    \Log::info("Created distribution ID: {$distribution->id}, Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}, Amount: {$investorShare}, Stripe Transaction ID: {$stripeTransactionId}");
                } catch (\Exception $e) {
                    \Log::error("Failed to transfer for Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}: {$e->getMessage()}");
                    throw $e;
                }

                $totalInvestorShare -= $investorShare;
                $remainingAmount -= $investorShare;
            }
        }

        \Log::info("Remaining amount after profit split for Deal ID: {$offering->id}: {$remainingAmount}");
        return $remainingAmount;
    }

    /**
     * Distribute internal rate of return (IRR) to investors based on proportional contribution.
     */
    protected function distributeInternalRate($offering, $hurdle, float $remainingAmount, float $totalContribution, $distributionType, $memo, $attributes)
    {
        $targetIRR = (float) ($hurdle->internal_rate ?? 0.12); // Default to 12%
        \Log::info("Distributing internal rate for Deal ID: {$offering->id}, Rate: {$targetIRR}");

        foreach ($offering->investments as $investment) {
            if ($remainingAmount <= 0) {
                \Log::info("No remaining amount for internal rate in Deal ID: {$offering->id}");
                break;
            }

            $transaction = $investment->transactions->first();
            if (!$transaction) {
                \Log::warning("No completed transaction for Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}");
                continue;
            }

            $investorContribution = (float) $transaction->amount;
            $investorShare = ($investorContribution / $totalContribution) * ($remainingAmount * $targetIRR);
            $investorShare = min($investorShare, $remainingAmount);
            \Log::info("Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}, Contribution: {$investorContribution}, IRR Share: {$investorShare}");

            if ($investorShare > 0) {
                $distribution = $this->createDistributionRecord(
                    $offering->id,
                    $investment->id,
                    $investment->investor_id,
                    $investorShare,
                    $distributionType,
                    $hurdle->waterfall_id,
                    $memo . ' - Internal Rate',
                    $attributes
                );
                try {
                    $stripeTransactionId = $this->stripeACHService->transferToInvestor(
                        $investment->investor,
                        $investorShare,
                        $offering->id,
                        $memo
                    );
                    $distribution->update(['stripe_transaction_id' => $stripeTransactionId]);
                    \Log::info("Created distribution ID: {$distribution->id}, Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}, Amount: {$investorShare}, Stripe Transaction ID: {$stripeTransactionId}");
                } catch (\Exception $e) {
                    \Log::error("Failed to transfer for Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}: {$e->getMessage()}");
                    throw $e;
                }

                $remainingAmount -= $investorShare;
            }
        }

        \Log::info("Remaining amount after internal rate for Deal ID: {$offering->id}: {$remainingAmount}");
        return $remainingAmount;
    }

    /**
     * Distribute return on investment (ROI) to investors based on proportional contribution.
     */
    protected function distributeReturnOnInvestment($offering, $hurdle, float $remainingAmount, float $totalContribution, $distributionType, $memo, $attributes)
    {
        $roiRate = (float) ($hurdle->roi_rate ?? 0.15); // Default to 15%
        \Log::info("Distributing return on investment for Deal ID: {$offering->id}, ROI Rate: {$roiRate}");

        foreach ($offering->investments as $investment) {
            if ($remainingAmount <= 0) {
                \Log::info("No remaining amount for ROI in Deal ID: {$offering->id}");
                break;
            }

            $transaction = $investment->transactions->first();
            if (!$transaction) {
                \Log::warning("No completed transaction for Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}");
                continue;
            }

            $investorContribution = (float) $transaction->amount;
            $investorShare = ($investorContribution / $totalContribution) * ($remainingAmount * $roiRate);
            $investorShare = min($investorShare, $remainingAmount);
            \Log::info("Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}, Contribution: {$investorContribution}, ROI Share: {$investorShare}");

            if ($investorShare > 0) {
                $distribution = $this->createDistributionRecord(
                    $offering->id,
                    $investment->id,
                    $investment->investor_id,
                    $investorShare,
                    $distributionType,
                    $hurdle->waterfall_id,
                    $memo . ' - Return on Investment',
                    $attributes
                );
                try {
                    $stripeTransactionId = $this->stripeACHService->transferToInvestor(
                        $investment->investor,
                        $investorShare,
                        $offering->id,
                        $memo
                    );
                    $distribution->update(['stripe_transaction_id' => $stripeTransactionId]);
                    \Log::info("Created distribution ID: {$distribution->id}, Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}, Amount: {$investorShare}, Stripe Transaction ID: {$stripeTransactionId}");
                } catch (\Exception $e) {
                    \Log::error("Failed to transfer for Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}: {$e->getMessage()}");
                    throw $e;
                }

                $remainingAmount -= $investorShare;
            }
        }

        \Log::info("Remaining amount after ROI for Deal ID: {$offering->id}: {$remainingAmount}");
        return $remainingAmount;
    }

    /**
     * Distribute management fee (typically to GP, not investors).
     */
    protected function distributeManagementFee($offering, $hurdle, float $remainingAmount, $distributionType, $memo, $attributes)
    {
        $feeRate = (float) ($hurdle->management_fee_rate ?? 0.02); // Default to 2%
        \Log::info("Distributing management fee for Deal ID: {$offering->id}, Fee Rate: {$feeRate}");

        $totalInvestment = $offering->investments->sum(function ($investment) {
            $transaction = $investment->transactions->first();
            return $transaction ? (float) $transaction->amount : 0;
        });
        $feeAmount = $totalInvestment * $feeRate;
        $feeAmount = min($feeAmount, $remainingAmount);
        \Log::info("Management fee for Deal ID: {$offering->id}: {$feeAmount}, allocated to GP");

        $remainingAmount -= $feeAmount;

        \Log::info("Remaining amount after management fee for Deal ID: {$offering->id}: {$remainingAmount}");
        return $remainingAmount;
    }

    /**
     * Distribute return of capital to investors based on proportional contribution.
     */
    protected function distributeReturnOfCapital($offering, $hurdle, float $remainingAmount, float $totalContribution, $distributionType, $memo, $attributes)
    {
        \Log::info("Distributing return of capital for Deal ID: {$offering->id}");

        foreach ($offering->investments as $investment) {
            if ($remainingAmount <= 0) {
                \Log::info("No remaining amount for return of capital in Deal ID: {$offering->id}");
                break;
            }
            foreach($investment->transactions as $transaction){
                
            }
            $transaction = $investment->transactions->first();
            if (!$transaction) {
                \Log::warning("No completed transaction for Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}");
                continue;
            }

            $investorContribution = (float) $transaction->amount;
            $investorShare = ($investorContribution / $totalContribution) * $remainingAmount;
            $investorShare = min($investorShare, $remainingAmount, $investorContribution);
            \Log::info("Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}, Contribution: {$investorContribution}, Return of Capital Share: {$investorShare}");

            if ($investorShare > 0) {
                $distribution = $this->createDistributionRecord(
                    $offering->id,
                    $investment->id,
                    $investment->investor_id,
                    $investorShare,
                    $distributionType,
                    $hurdle->waterfall_id,
                    $memo . ' - Return of Capital',
                    $attributes
                );
                try {
                    $stripeTransactionId = $this->stripeACHService->transferToInvestor(
                        $investment->investor,
                        $investorShare,
                        $offering->id,
                        $memo
                    );
                    $distribution->update(['stripe_transaction_id' => $stripeTransactionId]);
                    \Log::info("Created distribution ID: {$distribution->id}, Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}, Amount: {$investorShare}, Stripe Transaction ID: {$stripeTransactionId}");
                } catch (\Exception $e) {
                    \Log::error("Failed to transfer for Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}: {$e->getMessage()}");
                    throw $e;
                }

                $remainingAmount -= $investorShare;
            }
        }

        \Log::info("Remaining amount after return of capital for Deal ID: {$offering->id}: {$remainingAmount}");
        return $remainingAmount;
    }

    /**
     * Distribute cumulative return to investors based on proportional contribution.
     */
    protected function distributeCumulativeReturn($offering, $hurdle, float $remainingAmount, float $totalContribution, $distributionType, $memo, $attributes)
    {
        $cumulativeReturnRate = (float) ($hurdle->cumulative_return_rate ?? 0.1); // Default to 10%
        \Log::info("Distributing cumulative return for Deal ID: {$offering->id}, Rate: {$cumulativeReturnRate}");

        foreach ($offering->investments as $investment) {
            if ($remainingAmount <= 0) {
                \Log::info("No remaining amount for cumulative return in Deal ID: {$offering->id}");
                break;
            }

            $transaction = $investment->transactions->first();
            if (!$transaction) {
                \Log::warning("No completed transaction for Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}");
                continue;
            }

            $investorContribution = (float) $transaction->amount;
            $investorShare = ($investorContribution / $totalContribution) * ($remainingAmount * $cumulativeReturnRate);
            $investorShare = min($investorShare, $remainingAmount);
            \Log::info("Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}, Contribution: {$investorContribution}, Cumulative Return Share: {$investorShare}");

            if ($investorShare > 0) {
                $distribution = $this->createDistributionRecord(
                    $offering->id,
                    $investment->id,
                    $investment->investor_id,
                    $investorShare,
                    $distributionType,
                    $hurdle->waterfall_id,
                    $memo . ' - Cumulative Return',
                    $attributes
                );
                try {
                    $stripeTransactionId = $this->stripeACHService->transferToInvestor(
                        $investment->investor,
                        $investorShare,
                        $offering->id,
                        $memo
                    );
                    $distribution->update(['stripe_transaction_id' => $stripeTransactionId]);
                    \Log::info("Created distribution ID: {$distribution->id}, Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}, Amount: {$investorShare}, Stripe Transaction ID: {$stripeTransactionId}");
                } catch (\Exception $e) {
                    \Log::error("Failed to transfer for Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}: {$e->getMessage()}");
                    throw $e;
                }

                $remainingAmount -= $investorShare;
            }
        }

        \Log::info("Remaining amount after cumulative return for Deal ID: {$offering->id}: {$remainingAmount}");
        return $remainingAmount;
    }

    /**
     * Distribute interest to investors based on proportional contribution.
     */
    protected function distributeInterest($offering, $hurdle, float $remainingAmount, float $totalContribution, $distributionType, $memo, $attributes)
    {
        $interestRate = (float) ($hurdle->interest_rate ?? 0.05); // Default to 5%
        \Log::info("Distributing interest for Deal ID: {$offering->id}, Rate: {$interestRate}");

        foreach ($offering->investments as $investment) {
            if ($remainingAmount <= 0) {
                \Log::info("No remaining amount for interest in Deal ID: {$offering->id}");
                break;
            }

            $transaction = $investment->transactions->first();
            if (!$transaction) {
                \Log::warning("No completed transaction for Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}");
                continue;
            }

            $investorContribution = (float) $transaction->amount;
            $investorShare = ($investorContribution / $totalContribution) * ($remainingAmount * $interestRate);
            $investorShare = min($investorShare, $remainingAmount);
            \Log::info("Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}, Contribution: {$investorContribution}, Interest Share: {$investorShare}");

            if ($investorShare > 0) {
                $distribution = $this->createDistributionRecord(
                    $offering->id,
                    $investment->id,
                    $investment->investor_id,
                    $investorShare,
                    $distributionType,
                    $hurdle->waterfall_id,
                    $memo . ' - Interest',
                    $attributes
                );
                try {
                    $stripeTransactionId = $this->stripeACHService->transferToInvestor(
                        $investment->investor,
                        $investorShare,
                        $offering->id,
                        $memo
                    );
                    $distribution->update(['stripe_transaction_id' => $stripeTransactionId]);
                    \Log::info("Created distribution ID: {$distribution->id}, Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}, Amount: {$investorShare}, Stripe Transaction ID: {$stripeTransactionId}");
                } catch (\Exception $e) {
                    \Log::error("Failed to transfer for Investment ID: {$investment->id}, Investor ID: {$investment->investor_id}: {$e->getMessage()}");
                    throw $e;
                }

                $remainingAmount -= $investorShare;
            }
        }

        \Log::info("Remaining amount after interest for Deal ID: {$offering->id}: {$remainingAmount}");
        return $remainingAmount;
    }

    /**
     * Create a distribution record.
     */
    protected function createDistributionRecord($offeringId, $investmentId, $investorId, $amount, $distributionType, $waterfallId, $memo, $attributes)
    {
        $distribution = Distribution::create(array_merge([
            'offering_id' => $offeringId,
            'investment_id' => $investmentId,
            'investor_id' => $investorId,
            'amount' => $amount,
            'distribution_date' => now(),
            'distribution_type' => $distributionType,
            'distribution_waterfall_id' => $waterfallId,
            'count_toward' => in_array($distributionType, ['preferred_return', 'cumulative_return']) ? 'accrued_pref' : 'no_applicable',
            'is_visible' => true,
            'memo' => $memo,
            'approved' => true,
            'stripe_transaction_id' => null,
        ], $attributes));

        return $distribution;
    }
}