<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTransactionsTable extends Migration
{
    public function up()
    {
        Schema::create('investment_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('investment_id');
            $table->string('stripe_transaction_id')->nullable();
            $table->decimal('amount', 15, 2);
            $table->string('status')->default('pending'); // e.g., pending, completed, failed
            $table->string('payment_method')->nullable(); // e.g., ach, wire, invoice
            $table->timestamps();
            $table->foreign('investment_id')->references('id')->on('investments')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('investment_transactions');
    }
}