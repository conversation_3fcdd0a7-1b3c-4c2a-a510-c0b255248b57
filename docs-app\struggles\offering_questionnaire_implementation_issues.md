# Offering Questionnaire Implementation Issues

## Problem Description

During the implementation of the Offering Questionnaire module, we encountered several issues that prevented the module from functioning correctly:

1. **Missing Sidebar and Top Navigation**: The pages would load without the sidebar and top navigation bar, appearing as blank pages with only the content visible.

2. **Database Error with `created_by` Field**: When creating a new questionnaire, we encountered a database error: `Integrity constraint violation: 1048 Column 'created_by' cannot be null`.

3. **Livewire Component Not Found**: After fixing the first two issues, we encountered a Livewire error: `Unable to find component: [admin.offering-questionnaires.builder]`.

## Root Causes

### 1. Blade Section Naming Mismatch

The issue with the missing sidebar and top navigation was caused by a mismatch between how the admin layout system defines content sections and how they were being used in the new module:

- In the admin layout system (`resources/views/admin/layouts/app.blade.php`), the main content area is defined using `@yield('panel')`.
- However, the Offering Questionnaire views were incorrectly using `@section('content')`.
- Because of this mismatch, the content from the Offering Questionnaire views was never being rendered in the layout.

### 2. Authentication Guard Issue

The database error with the `created_by` field was caused by using the wrong authentication guard:

- The controller was using `Auth::id()` without specifying the guard.
- The application uses a custom 'admin' guard for admin authentication.
- This resulted in `null` being passed to the `created_by` field, which is not nullable in the database.

### 3. Livewire Component Namespace Mismatch

The Livewire component error was caused by a namespace and location mismatch:

- The view was looking for a component in the `App\Livewire\Admin\OfferingQuestionnaires` namespace.
- The existing component was defined in the `App\Http\Livewire\Admin\OfferingQuestionnaires` namespace.
- This mismatch in component location prevented Livewire from finding and rendering the component.

## Solutions Implemented

### 1. Fixed Blade Section Naming

Updated all view files in the Offering Questionnaire module to use the correct section name:

```php
@section('panel')
```

instead of:

```php
@section('content')
```

This change was made in:
- `resources/views/admin/offering-questionnaires/index.blade.php`
- `resources/views/admin/offering-questionnaires/create.blade.php`
- `resources/views/admin/offering-questionnaires/edit.blade.php`
- `resources/views/admin/offering-questionnaires/preview.blade.php`

### 2. Fixed Authentication Guard

Updated the controller to use the correct authentication guard:

```php
$validated['created_by'] = Auth::guard('admin')->id();
```

instead of:

```php
$validated['created_by'] = Auth::id();
```

Also fixed all route references in the controller to include the 'admin.' prefix, which is automatically added to routes defined in admin.php:

```php
return redirect()->route('admin.offering-questionnaires.edit', $questionnaire);
```

instead of:

```php
return redirect()->route('offering-questionnaires.edit', $questionnaire);
```

### 3. Fixed Livewire Component

Created a new Livewire component at the correct location that matches what's being called in the view:

```php
@livewire('admin.offering-questionnaires.builder', ['questionnaire' => $questionnaire])
```

The new component was created at:
`app/Livewire/Admin/OfferingQuestionnaires/Builder.php`

And implemented with all the necessary functionality for the questionnaire builder.

## Lessons Learned

1. **Understand Layout Structure**: Before creating new views, understand how the layout system is structured and which section names are being used.

2. **Use Correct Authentication Guards**: Always specify the guard when using authentication in a multi-guard application.

3. **Follow Route Naming Conventions**: Be aware of automatic prefixing of route names and ensure consistency in route references.

4. **Check Component Namespaces**: When using Livewire, ensure that component namespaces and locations match what's being called in the views.

5. **Consistent Naming Patterns**: Maintain consistent naming patterns across the application to avoid confusion and errors.

## Prevention Strategy

To prevent these issues in the future:

1. Create template documentation that clearly specifies which section names should be used in different parts of the application.

2. Implement a code review process that specifically checks for template structure consistency, authentication guard usage, and route naming.

3. Create a starter template or scaffolding command for new modules to ensure consistency.

4. Add comments in layout files and controllers indicating which guards and naming conventions should be used.

5. Maintain a consistent directory structure for Livewire components and ensure proper namespace usage.
