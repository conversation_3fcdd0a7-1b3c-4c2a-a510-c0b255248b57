{"__meta": {"id": "01K19X7VY1J8P39H3B186JHYSQ", "datetime": "2025-07-28 21:37:35", "utime": **********.169769, "method": "GET", "uri": "/admin", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753753054.979065, "end": **********.16978, "duration": 0.19071507453918457, "duration_str": "191ms", "measures": [{"label": "Booting", "start": 1753753054.979065, "relative_start": 0, "end": **********.123715, "relative_end": **********.123715, "duration": 0.*****************, "duration_str": "145ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.123723, "relative_start": 0.*****************, "end": **********.169781, "relative_end": 9.5367431640625e-07, "duration": 0.046057939529418945, "duration_str": "46.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.134337, "relative_start": 0.****************, "end": **********.135841, "relative_end": **********.135841, "duration": 0.0015039443969726562, "duration_str": "1.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.145004, "relative_start": 0.****************, "end": **********.168553, "relative_end": **********.168553, "duration": 0.*****************, "duration_str": "23.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin.auth.login", "start": **********.14642, "relative_start": 0.*****************, "end": **********.14642, "relative_end": **********.14642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.<PERSON><PERSON>a", "start": **********.149771, "relative_start": 0.*****************, "end": **********.149771, "relative_end": **********.149771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.layouts.master", "start": **********.166435, "relative_start": 0.18737006187438965, "end": **********.166435, "relative_end": **********.166435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.notify", "start": **********.167575, "relative_start": 0.18850994110107422, "end": **********.167575, "relative_end": **********.167575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 29461720, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.7", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/New_York", "Locale": "en"}}, "views": {"count": 4, "nb_templates": 4, "templates": [{"name": "admin.auth.login", "param_count": null, "params": [], "start": **********.146391, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/admin/auth/login.blade.phpadmin.auth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fadmin%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}}, {"name": "partials.cap<PERSON>a", "param_count": null, "params": [], "start": **********.149744, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/partials/captcha.blade.phppartials.captcha", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fpartials%2Fcaptcha.blade.php&line=1", "ajax": false, "filename": "captcha.blade.php", "line": "?"}}, {"name": "admin.layouts.master", "param_count": null, "params": [], "start": **********.166408, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/admin/layouts/master.blade.phpadmin.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}}, {"name": "partials.notify", "param_count": null, "params": [], "start": **********.167548, "type": "blade", "hash": "bladeC:\\laragon\\www\\PLATFORM\\resources\\views/partials/notify.blade.phppartials.notify", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fresources%2Fviews%2Fpartials%2Fnotify.blade.php&line=1", "ajax": false, "filename": "notify.blade.php", "line": "?"}}]}, "queries": {"count": 3, "nb_statements": 2, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0036999999999999997, "accumulated_duration_str": "3.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 39}, {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.154615, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "Captcha.php:39", "source": {"index": 15, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FLib%2FCaptcha.php&line=39", "ajax": false, "filename": "Captcha.php", "line": "39"}, "connection": "main", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `extensions` where `act` = 'custom-captcha' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["custom-captcha", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.159812, "duration": 0.0030499999999999998, "duration_str": "3.05ms", "memory": 0, "memory_str": null, "filename": "Captcha.php:39", "source": {"index": 16, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FLib%2FCaptcha.php&line=39", "ajax": false, "filename": "Captcha.php", "line": "39"}, "connection": "main", "explain": null, "start_percent": 0, "width_percent": 82.432}, {"sql": "select * from `extensions` where `act` = 'google-recaptcha2' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["google-recaptcha2", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 27}, {"index": 17, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Http\\Helpers\\helpers.php", "line": 76}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\PLATFORM\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.164679, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Captcha.php:27", "source": {"index": 16, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\laragon\\www\\PLATFORM\\app\\Lib\\Captcha.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FLib%2FCaptcha.php&line=27", "ajax": false, "filename": "Captcha.php", "line": "27"}, "connection": "main", "explain": null, "start_percent": 82.432, "width_percent": 17.568}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin", "action_name": "admin.login", "controller_action": "App\\Http\\Controllers\\Admin\\Auth\\LoginController@showLoginForm", "uri": "GET admin", "controller": "App\\Http\\Controllers\\Admin\\Auth\\LoginController@showLoginForm<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FAdmin%2FAuth%2FLoginController.php&line=35\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin\\Auth", "prefix": "/admin", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FPLATFORM%2Fapp%2FHttp%2FControllers%2FAdmin%2FAuth%2FLoginController.php&line=35\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/Auth/LoginController.php:35-39</a>", "middleware": "web, admin.guest", "duration": "191ms", "peak_memory": "30MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-456107789 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-456107789\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2014198476 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2014198476\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1774100270 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"130 characters\">cre_session=Hh6TGHqPVCzTYE14FsgPTMaR8dDM9c6s6FwIM1bh; gdpr_cookie=CRE%20CLOUD; XSRF-TOKEN=1QvYyUuZvNXP3QvUIz8yJKO3UO7m20Q8B9joLzR1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1774100270\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-439313019 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cre_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Hh6TGHqPVCzTYE14FsgPTMaR8dDM9c6s6FwIM1bh</span>\"\n  \"<span class=sf-dump-key>gdpr_cookie</span>\" => \"<span class=sf-dump-str title=\"9 characters\">CRE CLOUD</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1QvYyUuZvNXP3QvUIz8yJKO3UO7m20Q8B9joLzR1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-439313019\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-959683765 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 01:37:35 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-959683765\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-775683999 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1QvYyUuZvNXP3QvUIz8yJKO3UO7m20Q8B9joLzR1</span>\"\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">_old_input</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">errors</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_old_input</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Hi3oCbj2Qbf5E0x2C0wDsuITZpWOIOAeGfKowpkw</span>\"\n    \"<span class=sf-dump-key>username</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref>#1319</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">bags</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>default</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\MessageBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">MessageBag</span></span> {<a class=sf-dump-ref>#1320</a><samp data-depth=4 class=sf-dump-compact>\n        #<span class=sf-dump-protected title=\"Protected property\">messages</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>username</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">These credentials do not match our records.</span>\"\n          </samp>]\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">format</span>: \"<span class=sf-dump-str title=\"8 characters\">:message</span>\"\n      </samp>}\n    </samp>]\n  </samp>}\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K19X7VQG8QNK7ST9Y93AVFK2</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-775683999\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin", "action_name": "admin.login", "controller_action": "App\\Http\\Controllers\\Admin\\Auth\\LoginController@showLoginForm"}, "badge": null}}